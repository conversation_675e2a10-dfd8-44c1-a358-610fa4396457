#!/usr/bin/env python3
"""
统一的股票分析运行脚本 - 支持指定输出目录
"""

import os
import argparse
import subprocess
import sys
from datetime import datetime

class AnalysisRunner:
    def __init__(self, input_dir, output_dir="analysis_reports", api_key=None):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.api_key = api_key
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {os.path.abspath(self.output_dir)}")
    
    def run_bottom_stock_analysis(self):
        """运行后排股票分析"""
        print("\n🔮 开始后排股票潜力分析...")
        print("=" * 50)
        
        cmd = [
            sys.executable, "bottom_stock_analyzer.py",
            "--input_dir", self.input_dir,
            "--output_dir", self.output_dir
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print("✅ 后排股票分析完成")
                print(result.stdout)
                return True
            else:
                print("❌ 后排股票分析失败")
                print(result.stderr)
                return False
        except subprocess.TimeoutExpired:
            print("⏰ 后排股票分析超时")
            return False
        except Exception as e:
            print(f"❌ 运行后排股票分析时出错: {e}")
            return False
    
    def run_enhanced_gemini_analysis(self):
        """运行增强版Gemini分析"""
        if not self.api_key:
            print("⚠️ 跳过Gemini分析 - 未提供API密钥")
            return False
        
        print("\n🤖 开始增强版Gemini AI分析...")
        print("=" * 50)
        
        cmd = [
            sys.executable, "enhanced_gemini_analyzer.py",
            "--input_dir", self.input_dir,
            "--output_dir", self.output_dir,
            "--api_key", self.api_key
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode == 0:
                print("✅ 增强版Gemini分析完成")
                print(result.stdout)
                return True
            else:
                print("❌ 增强版Gemini分析失败")
                print(result.stderr)
                return False
        except subprocess.TimeoutExpired:
            print("⏰ Gemini分析超时")
            return False
        except Exception as e:
            print(f"❌ 运行Gemini分析时出错: {e}")
            return False
    
    def run_robust_gemini_analysis(self):
        """运行增强版连接Gemini分析"""
        if not self.api_key:
            print("⚠️ 跳过增强版连接分析 - 未提供API密钥")
            return False
        
        print("\n🛡️ 开始增强版连接Gemini分析...")
        print("=" * 50)
        
        cmd = [
            sys.executable, "robust_gemini_analyzer.py",
            "--input_dir", self.input_dir,
            "--output_dir", self.output_dir,
            "--api_key", self.api_key
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode == 0:
                print("✅ 增强版连接分析完成")
                print(result.stdout)
                return True
            else:
                print("❌ 增强版连接分析失败")
                print(result.stderr)
                return False
        except subprocess.TimeoutExpired:
            print("⏰ 增强版连接分析超时")
            return False
        except Exception as e:
            print(f"❌ 运行增强版连接分析时出错: {e}")
            return False
    
    def run_network_diagnostic(self):
        """运行网络诊断"""
        print("\n🔍 开始网络连接诊断...")
        print("=" * 50)
        
        cmd = [sys.executable, "network_diagnostic.py"]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return result.returncode == 0
        except subprocess.TimeoutExpired:
            print("⏰ 网络诊断超时")
            return False
        except Exception as e:
            print(f"❌ 运行网络诊断时出错: {e}")
            return False
    
    def list_output_files(self):
        """列出生成的输出文件"""
        print(f"\n📄 生成的分析报告文件:")
        print("=" * 50)
        
        if not os.path.exists(self.output_dir):
            print("❌ 输出目录不存在")
            return
        
        files = []
        for filename in os.listdir(self.output_dir):
            if filename.endswith(('.md', '.json')):
                filepath = os.path.join(self.output_dir, filename)
                stat = os.stat(filepath)
                files.append({
                    'name': filename,
                    'size': stat.st_size,
                    'mtime': datetime.fromtimestamp(stat.st_mtime)
                })
        
        if not files:
            print("📭 没有找到分析报告文件")
            return
        
        # 按修改时间排序
        files.sort(key=lambda x: x['mtime'], reverse=True)
        
        for i, file_info in enumerate(files, 1):
            size_str = f"{file_info['size']/1024:.1f}KB" if file_info['size'] > 1024 else f"{file_info['size']}B"
            time_str = file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"  {i}. {file_info['name']} ({size_str}, {time_str})")
        
        print(f"\n📁 输出目录: {os.path.abspath(self.output_dir)}")
    
    def run_all_analysis(self):
        """运行所有分析"""
        print("🚀 开始完整股票分析流程")
        print("=" * 60)
        
        results = {}
        
        # 1. 网络诊断（如果有API密钥）
        if self.api_key:
            results['network_diagnostic'] = self.run_network_diagnostic()
        
        # 2. 后排股票分析（必须）
        results['bottom_stock_analysis'] = self.run_bottom_stock_analysis()
        
        # 3. 增强版Gemini分析（如果有API密钥）
        if self.api_key:
            # 先尝试标准版
            results['enhanced_gemini'] = self.run_enhanced_gemini_analysis()
            
            # 如果标准版失败，尝试增强版连接
            if not results['enhanced_gemini']:
                print("\n🔄 标准版失败，尝试增强版连接...")
                results['robust_gemini'] = self.run_robust_gemini_analysis()
        
        # 4. 显示结果总结
        self.show_summary(results)
        
        # 5. 列出生成的文件
        self.list_output_files()
    
    def show_summary(self, results):
        """显示分析结果总结"""
        print("\n📊 分析结果总结")
        print("=" * 50)
        
        total_tasks = len(results)
        successful_tasks = sum(1 for success in results.values() if success)
        
        for task, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            task_name = {
                'network_diagnostic': '网络诊断',
                'bottom_stock_analysis': '后排股票分析',
                'enhanced_gemini': '增强版Gemini分析',
                'robust_gemini': '增强版连接分析'
            }.get(task, task)
            
            print(f"  {task_name}: {status}")
        
        success_rate = (successful_tasks / total_tasks) * 100 if total_tasks > 0 else 0
        print(f"\n🎯 总体成功率: {success_rate:.1f}% ({successful_tasks}/{total_tasks})")
        
        if successful_tasks > 0:
            print("🎉 分析完成！请查看生成的报告文件。")
        else:
            print("❌ 所有分析都失败了，请检查输入数据和网络连接。")

def main():
    parser = argparse.ArgumentParser(description="统一的股票分析运行脚本")
    parser.add_argument("--input_dir", type=str, required=True,
                       help="包含股票数据的目录路径")
    parser.add_argument("--output_dir", type=str, default="analysis_reports",
                       help="输出目录路径 (默认: analysis_reports)")
    parser.add_argument("--api_key", type=str,
                       help="Gemini API 密钥 (可选，用于AI分析)")
    parser.add_argument("--mode", type=str, choices=['all', 'bottom', 'gemini', 'diagnostic'],
                       default='all', help="运行模式 (默认: all)")
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 输入目录不存在: {args.input_dir}")
        sys.exit(1)
    
    # 从环境变量获取API密钥（如果未提供）
    api_key = args.api_key or os.getenv('GEMINI_API_KEY')
    
    runner = AnalysisRunner(args.input_dir, args.output_dir, api_key)
    
    try:
        if args.mode == 'all':
            runner.run_all_analysis()
        elif args.mode == 'bottom':
            runner.run_bottom_stock_analysis()
            runner.list_output_files()
        elif args.mode == 'gemini':
            if not api_key:
                print("❌ Gemini分析需要API密钥")
                sys.exit(1)
            runner.run_enhanced_gemini_analysis()
            runner.list_output_files()
        elif args.mode == 'diagnostic':
            runner.run_network_diagnostic()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了分析过程")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行分析时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
