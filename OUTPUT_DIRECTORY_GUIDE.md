# 📁 输出目录管理指南

## 🎯 概述

所有分析工具现在都支持指定输出目录，不再将结果文件散落在根目录中。这样可以更好地组织和管理分析报告。

## 📊 支持输出目录的工具

### 🔧 **已更新的工具**

| 工具名称 | 默认输出目录 | 参数 |
|----------|--------------|------|
| `bottom_stock_analyzer.py` | `analysis_reports` | `--output_dir` |
| `enhanced_gemini_analyzer.py` | `analysis_reports` | `--output_dir` |
| `gemini_stock_analyzer.py` | `analysis_reports` | `--output_dir` |
| `robust_gemini_analyzer.py` | `analysis_reports` | `--output_dir` |
| `run_analysis.py` | `analysis_reports` | `--output_dir` |

## 🚀 使用方法

### 1. **使用默认输出目录**
```bash
# 使用默认目录 analysis_reports
python bottom_stock_analyzer.py --input_dir output
```

### 2. **指定自定义输出目录**
```bash
# 指定自定义目录
python bottom_stock_analyzer.py --input_dir output --output_dir my_reports
```

### 3. **使用统一运行脚本**
```bash
# 运行所有分析，输出到指定目录
python run_analysis.py --input_dir output --output_dir daily_analysis

# 只运行后排股票分析
python run_analysis.py --input_dir output --output_dir my_analysis --mode bottom

# 只运行Gemini分析
python run_analysis.py --input_dir output --output_dir ai_reports --mode gemini --api_key YOUR_KEY
```

## 📁 目录结构示例

### 🗂️ **推荐的目录组织方式**

```
stock_paul/
├── output/                     # 原始数据目录
│   ├── 20250529/
│   ├── 20250530/
│   └── 20250603/
├── analysis_reports/           # 默认分析报告目录
│   ├── bottom_stocks_analysis_20250604_071159.md
│   ├── bottom_stocks_data_20250604_071159.json
│   ├── enhanced_gemini_analysis_20250604_080000.md
│   └── robust_gemini_analysis_20250604_080500.md
├── daily_analysis/             # 每日分析目录
│   ├── 2025-06-04/
│   └── 2025-06-03/
└── historical_reports/         # 历史报告存档
    ├── 2025-06/
    └── 2025-05/
```

### 📅 **按日期组织**
```bash
# 按日期创建目录
DATE=$(date +%Y-%m-%d)
python run_analysis.py --input_dir output --output_dir "daily_analysis/$DATE"
```

### 🏷️ **按分析类型组织**
```bash
# 后排股票分析
python bottom_stock_analyzer.py --input_dir output --output_dir "reports/bottom_stocks"

# AI分析
python enhanced_gemini_analyzer.py --input_dir output --output_dir "reports/ai_analysis"
```

## 🎨 **高级使用技巧**

### 1. **批量分析脚本**
```bash
#!/bin/bash
# batch_analysis.sh

DATE=$(date +%Y%m%d)
OUTPUT_DIR="analysis_$DATE"

echo "📊 开始批量分析，输出到: $OUTPUT_DIR"

# 后排股票分析
python bottom_stock_analyzer.py --input_dir output --output_dir "$OUTPUT_DIR"

# AI分析（如果有API密钥）
if [ ! -z "$GEMINI_API_KEY" ]; then
    python enhanced_gemini_analyzer.py --input_dir output --output_dir "$OUTPUT_DIR" --api_key "$GEMINI_API_KEY"
fi

echo "✅ 批量分析完成，结果保存在: $OUTPUT_DIR"
```

### 2. **自动清理旧报告**
```bash
#!/bin/bash
# cleanup_old_reports.sh

# 删除7天前的报告
find analysis_reports -name "*.md" -mtime +7 -delete
find analysis_reports -name "*.json" -mtime +7 -delete

echo "🧹 清理完成"
```

### 3. **报告归档脚本**
```bash
#!/bin/bash
# archive_reports.sh

DATE=$(date +%Y-%m)
ARCHIVE_DIR="historical_reports/$DATE"

mkdir -p "$ARCHIVE_DIR"
mv analysis_reports/*.md "$ARCHIVE_DIR/" 2>/dev/null
mv analysis_reports/*.json "$ARCHIVE_DIR/" 2>/dev/null

echo "📦 报告已归档到: $ARCHIVE_DIR"
```

## 📊 **统一运行脚本功能**

### 🎯 **运行模式**

| 模式 | 说明 | 命令示例 |
|------|------|----------|
| `all` | 运行所有分析 | `--mode all` |
| `bottom` | 只运行后排股票分析 | `--mode bottom` |
| `gemini` | 只运行Gemini AI分析 | `--mode gemini` |
| `diagnostic` | 只运行网络诊断 | `--mode diagnostic` |

### 📈 **完整示例**
```bash
# 完整分析流程
python run_analysis.py \
    --input_dir output \
    --output_dir "reports/$(date +%Y%m%d)" \
    --api_key "$GEMINI_API_KEY" \
    --mode all
```

## 🔧 **故障排除**

### ❌ **常见问题**

1. **权限错误**
   ```bash
   # 确保有写入权限
   chmod 755 analysis_reports
   ```

2. **目录不存在**
   ```bash
   # 工具会自动创建目录，但确保父目录存在
   mkdir -p reports/subdirectory
   ```

3. **磁盘空间不足**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 清理旧文件
   find analysis_reports -name "*.md" -mtime +30 -delete
   ```

## 📋 **最佳实践**

### 🎯 **目录命名规范**
- 使用有意义的名称：`daily_analysis`, `weekly_reports`
- 包含日期：`analysis_20250604`, `reports_2025_06`
- 避免空格和特殊字符

### 📅 **定期维护**
```bash
# 每周运行的维护脚本
#!/bin/bash

# 1. 归档上周的报告
LAST_WEEK=$(date -d "last week" +%Y%m%d)
mkdir -p "archive/$LAST_WEEK"
mv analysis_reports/*_$LAST_WEEK*.* "archive/$LAST_WEEK/" 2>/dev/null

# 2. 清理超过30天的归档
find archive -type d -mtime +30 -exec rm -rf {} \;

# 3. 生成本周分析
THIS_WEEK=$(date +%Y%m%d)
python run_analysis.py --input_dir output --output_dir "weekly_reports/$THIS_WEEK"
```

### 🔄 **自动化工作流**
```bash
# crontab 示例 - 每日自动分析
# 0 9 * * * cd /path/to/stock_paul && python run_analysis.py --input_dir output --output_dir "daily/$(date +\%Y\%m\%d)" --mode bottom
```

## 📊 **输出文件说明**

### 📄 **生成的文件类型**

| 文件类型 | 扩展名 | 说明 |
|----------|--------|------|
| 分析报告 | `.md` | Markdown格式的详细分析报告 |
| 数据文件 | `.json` | JSON格式的结构化数据 |

### 🏷️ **文件命名规则**
```
{tool_name}_{timestamp}.{extension}

示例:
- bottom_stocks_analysis_20250604_071159.md
- enhanced_gemini_analysis_20250604_080000.md
- bottom_stocks_data_20250604_071159.json
```

## 🎉 **总结**

✅ **优势**:
- 📁 文件组织更清晰
- 🔍 易于查找和管理
- 📊 支持批量处理
- 🗂️ 便于归档和备份

✅ **使用建议**:
- 使用有意义的目录名称
- 定期清理和归档旧报告
- 建立自动化工作流
- 备份重要的分析结果

现在所有的分析结果都会整齐地保存在指定目录中，不再散落在项目根目录！🎯
