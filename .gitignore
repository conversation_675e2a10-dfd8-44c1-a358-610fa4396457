# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Logs and databases
log/
logs/
*.log
*.sqlite
*.db

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
output/
*.csv

# Keep source code and configuration
!src/
!config/
!requirements.txt
!README.md
!backtests/*.py
!backtests/optimization_results/*.py
