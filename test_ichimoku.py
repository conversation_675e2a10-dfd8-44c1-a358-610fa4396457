"""
测试一目均衡表计算逻辑的简单测试用例
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import sys
import os

# 添加父目录到系统路径，以便导入ichimoku模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.technical_indicators.ichimoku import calculate_ichimoku, plot_ichimoku

# 尝试加载中文字体
try:
    # macOS常见中文字体
    font_list = ['PingFang SC', 'STHeiti', 'Heiti SC', 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    chinese_font = None
    
    for font in font_list:
        try:
            chinese_font = FontProperties(family=font)
            break
        except:
            continue
            
    if chinese_font is None:
        print("警告: 未能找到中文字体，图表中的中文可能无法正确显示")
except:
    print("警告: 字体配置失败，图表中的中文可能无法正确显示")
    chinese_font = FontProperties()

def generate_test_data(n_days=100, trend='uptrend', volatility=0.02, seed=42):
    """
    生成用于测试的模拟价格数据
    
    参数:
        n_days: 数据点数量
        trend: 趋势类型 ('uptrend', 'downtrend', 'sideways', 'reversal')
        volatility: 波动率
        seed: 随机种子
    
    返回:
        模拟的OHLC数据
    """
    np.random.seed(seed)
    
    # 基础价格 - 从100开始
    base_price = 100
    
    # 生成收盘价
    if trend == 'uptrend':
        # 上升趋势
        drift = np.linspace(0, 0.5, n_days)  # 逐渐增加的趋势
    elif trend == 'downtrend':
        # 下降趋势
        drift = np.linspace(0, -0.4, n_days)  # 逐渐下降的趋势
    elif trend == 'sideways':
        # 横盘整理
        drift = np.zeros(n_days)
    elif trend == 'reversal':
        # 趋势反转 (先上升后下降)
        half = n_days // 2
        drift = np.concatenate([
            np.linspace(0, 0.3, half),  # 上升
            np.linspace(0.3, -0.2, n_days - half)  # 下降
        ])
    else:
        raise ValueError(f"不支持的趋势类型: {trend}")
    
    # 生成随机波动
    random_walk = np.random.normal(0, volatility, n_days).cumsum()
    
    # 生成收盘价
    close = base_price * (1 + drift + random_walk)
    
    # 生成OHLC数据
    daily_volatility = volatility * 0.6  # 日内波动
    high = close * (1 + np.random.uniform(0, daily_volatility, n_days))
    low = close * (1 - np.random.uniform(0, daily_volatility, n_days))
    open_price = low + np.random.uniform(0, 1, n_days) * (high - low)
    
    # 创建日期索引
    dates = pd.date_range(start='2023-01-01', periods=n_days)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'Open': open_price,
        'High': high,
        'Low': low,
        'Close': close
    }, index=dates)
    
    return df

def plot_candles(ax, data, width=0.6):
    """
    在指定的坐标轴上绘制简化版蜡烛图
    
    参数:
        ax: matplotlib坐标轴对象
        data: 带有OHLC数据的DataFrame
        width: 蜡烛宽度
    """
    # 获取上涨和下跌的索引
    up = data[data.Close >= data.Open].index
    down = data[data.Close < data.Open].index
    
    # 绘制上涨蜡烛
    ax.bar(up, data.loc[up, 'High'] - data.loc[up, 'Low'], 
           bottom=data.loc[up, 'Low'], width=width/3, color='red', alpha=0.3)
    ax.bar(up, data.loc[up, 'Close'] - data.loc[up, 'Open'], 
           bottom=data.loc[up, 'Open'], width=width, color='red', alpha=0.5)
    
    # 绘制下跌蜡烛
    ax.bar(down, data.loc[down, 'High'] - data.loc[down, 'Low'], 
           bottom=data.loc[down, 'Low'], width=width/3, color='green', alpha=0.3)
    ax.bar(down, data.loc[down, 'Open'] - data.loc[down, 'Close'], 
           bottom=data.loc[down, 'Close'], width=width, color='green', alpha=0.5)

def test_ichimoku_visualization():
    """测试一目均衡表计算并可视化结果"""
    # 设置matplotlib样式
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # 生成不同趋势的测试数据
    trends = ['uptrend', 'downtrend', 'reversal']
    trend_names = {'uptrend': '上升趋势', 'downtrend': '下降趋势', 'reversal': '趋势反转'}
    
    fig, axes = plt.subplots(len(trends), 1, figsize=(12, 15), sharex=False)
    
    for i, trend in enumerate(trends):
        # 生成数据
        data = generate_test_data(n_days=100, trend=trend, volatility=0.03 if trend == 'reversal' else 0.02)
        
        # 计算一目均衡表指标
        ichimoku = calculate_ichimoku(data)
        
        # 获取坐标轴
        ax = axes[i]
        
        # 绘制蜡烛图
        plot_candles(ax, data)
        
        # 绘制一目均衡表
        plot_ichimoku(ax, data, ichimoku)
        
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 添加标题
        ax.set_title(f'一目均衡表 - {trend_names.get(trend, trend)}', fontproperties=chinese_font, fontsize=14)
        
        # 添加图例
        ax.legend(loc='best', prop=chinese_font)
        
        # 设置标签
        ax.set_ylabel('价格', fontproperties=chinese_font)
        if i == len(trends) - 1:
            ax.set_xlabel('日期', fontproperties=chinese_font)
        
        # 启用网格
        ax.grid(True, alpha=0.3)
        
        # 调整Y轴范围，确保看到所有信号线
        y_min = min(data['Low'].min(), ichimoku['lagging_span'].min(), 
                   ichimoku['leading_span_a'].min(), ichimoku['leading_span_b'].min())
        y_max = max(data['High'].max(), ichimoku['lagging_span'].max(),
                   ichimoku['leading_span_a'].max(), ichimoku['leading_span_b'].max())
        
        # 增加一些余量
        margin = (y_max - y_min) * 0.1
        ax.set_ylim(y_min - margin, y_max + margin)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('ichimoku_test_visualization.png', dpi=150)
    
    # 显示图片
    plt.show()
    
    # 打印一些关键统计数据进行验证
    print("\n一目均衡表指标统计验证:")
    for trend in trends:
        data = generate_test_data(n_days=100, trend=trend)
        ichimoku = calculate_ichimoku(data)
        
        # 检验数据非空
        non_na_count = {
            'conversion_line': ichimoku['conversion_line'].notna().sum(),
            'base_line': ichimoku['base_line'].notna().sum(),
            'lagging_span': ichimoku['lagging_span'].notna().sum(),
            'leading_span_a': ichimoku['leading_span_a'].notna().sum(),
            'leading_span_b': ichimoku['leading_span_b'].notna().sum()
        }
        
        print(f"\n趋势类型: {trend_names.get(trend, trend)}")
        print(f"数据点总数: {len(data)}")
        print("非空数据点数:")
        for key, value in non_na_count.items():
            print(f"  - {key}: {value}/{len(data)} ({value/len(data)*100:.1f}%)")
        
        # 验证关键信号 (示例：交叉次数)
        cross_above = ((ichimoku['conversion_line'] > ichimoku['base_line']) & 
                      (ichimoku['conversion_line'].shift(1) <= ichimoku['base_line'].shift(1))).sum()
        cross_below = ((ichimoku['conversion_line'] < ichimoku['base_line']) & 
                      (ichimoku['conversion_line'].shift(1) >= ichimoku['base_line'].shift(1))).sum()
        
        print(f"转换线穿越基准线次数: 向上={cross_above}, 向下={cross_below}")

if __name__ == "__main__":
    test_ichimoku_visualization() 