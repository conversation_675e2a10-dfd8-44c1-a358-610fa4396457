# 📚 文档索引 - 股票趋势分析工具套件

## 🎯 文档概览

这里是股票趋势分析工具套件的完整文档索引，帮助您快速找到所需的信息。

## 📖 主要文档

### 🚀 **核心文档**

| 文档名称 | 描述 | 适用人群 |
|----------|------|----------|
| `README.md` | **主要使用指南** - 工具套件概览和快速开始 | 所有用户 |
| `ENHANCED_ANALYSIS_GUIDE.md` | **增强分析功能指南** - 详细的功能介绍和使用方法 | 高级用户 |
| `OUTPUT_DIRECTORY_GUIDE.md` | **输出目录管理指南** - 文件组织和管理最佳实践 | 所有用户 |

### 🔧 **技术文档**

| 文档名称 | 描述 | 适用人群 |
|----------|------|----------|
| `FAILURE_ANALYSIS.md` | **故障分析和解决方案** - 常见问题排查 | 技术用户 |
| `GEMINI_FIX_NOTES.md` | **API修复说明** - Gemini API问题解决 | 开发者 |
| `README_GEMINI.md` | **Gemini API详细使用指南** - API配置和使用 | API用户 |

### 📊 **专项指南**

| 文档名称 | 描述 | 适用人群 |
|----------|------|----------|
| `NETWORK_DIAGNOSTIC_GUIDE.md` | **网络诊断指南** - 网络问题排查 | 技术用户 |
| `AI_PROMPT_GUIDE.md` | **AI提示词指南** - 提示词优化和定制 | 高级用户 |

## 🎯 按使用场景分类

### 🚀 **新手入门**
1. **开始阅读**: `README.md` - 了解工具概览
2. **快速体验**: 按照快速开始章节操作
3. **文件管理**: `OUTPUT_DIRECTORY_GUIDE.md` - 学习输出管理

### 📊 **日常使用**
1. **功能详解**: `ENHANCED_ANALYSIS_GUIDE.md` - 深入了解各项功能
2. **最佳实践**: `OUTPUT_DIRECTORY_GUIDE.md` - 高效的工作流程
3. **故障排除**: `FAILURE_ANALYSIS.md` - 解决常见问题

### 🤖 **AI分析用户**
1. **API配置**: `README_GEMINI.md` - 详细的API设置
2. **网络问题**: `GEMINI_FIX_NOTES.md` - API连接问题解决
3. **提示词优化**: `AI_PROMPT_GUIDE.md` - 自定义分析提示词

### 🔧 **技术用户**
1. **故障诊断**: `FAILURE_ANALYSIS.md` - 深度问题分析
2. **网络诊断**: `NETWORK_DIAGNOSTIC_GUIDE.md` - 网络连接排查
3. **API修复**: `GEMINI_FIX_NOTES.md` - API问题解决方案

## 📋 快速参考

### 🎯 **常用命令**
```bash
# 基础分析（无需API）
python bottom_stock_analyzer.py --input_dir output --output_dir analysis_reports

# AI增强分析（需要API密钥）
python enhanced_gemini_analyzer.py --input_dir output --output_dir analysis_reports --api_key YOUR_KEY

# 一键运行所有分析
python run_analysis.py --input_dir output --output_dir my_reports --mode all

# 网络诊断
python network_diagnostic.py

# 文件清理
python cleanup_old_files.py --execute
```

### 🔗 **重要链接**
- [Google AI Studio](https://makersuite.google.com/app/apikey) - 获取API密钥
- [Gemini API文档](https://ai.google.dev/docs) - 官方API文档

### 📊 **核心工具**
- `bottom_stock_analyzer.py` - 后排股票潜力分析
- `enhanced_gemini_analyzer.py` - AI深度分析
- `run_analysis.py` - 统一运行脚本
- `network_diagnostic.py` - 网络诊断工具

## 🎨 文档特色

### 📊 **结构化内容**
- 🎯 明确的使用场景分类
- 📋 详细的参数说明
- 💡 实用的操作建议
- ⚠️ 重要的风险提示

### 🔍 **易于查找**
- 📚 完整的文档索引
- 🏷️ 清晰的标签分类
- 🔗 便捷的交叉引用
- 📖 详细的目录结构

### 💡 **实用导向**
- 🚀 快速开始指南
- 📊 实际使用示例
- 🔧 故障排除方案
- 📈 最佳实践建议

## 🔄 文档更新

### 📅 **更新频率**
- **主要文档**: 随功能更新同步更新
- **技术文档**: 根据问题反馈及时更新
- **指南文档**: 定期优化和完善

### 📝 **更新内容**
- 新功能介绍和使用方法
- 常见问题解决方案
- 用户反馈和改进建议
- 最佳实践和使用技巧

## 📞 获取帮助

### 🔍 **查找信息**
1. **查看索引**: 本文档 - 快速定位相关文档
2. **搜索关键词**: 在相关文档中搜索具体问题
3. **参考示例**: 查看实际使用示例和命令

### 🛠️ **解决问题**
1. **故障排除**: `FAILURE_ANALYSIS.md` - 常见问题解决
2. **网络问题**: `NETWORK_DIAGNOSTIC_GUIDE.md` - 网络诊断
3. **API问题**: `GEMINI_FIX_NOTES.md` - API修复方案

### 💡 **优化使用**
1. **功能详解**: `ENHANCED_ANALYSIS_GUIDE.md` - 深入了解功能
2. **文件管理**: `OUTPUT_DIRECTORY_GUIDE.md` - 高效组织文件
3. **提示词优化**: `AI_PROMPT_GUIDE.md` - 自定义AI分析

## 🎯 核心价值

### 🔥 **独特优势**
- **后排股票挖掘**: 市场首创的潜力股评分算法
- **AI增强分析**: 超级强化的投资分析提示词
- **完整工具链**: 从数据分析到投资决策的全流程
- **实战导向**: 具体到股票代码和操作建议

### 📊 **数据驱动**
- 基于历史成功案例的量化分析
- 多维度综合评分系统
- 实时数据更新和分析
- 智能模式识别和预测

### 🛡️ **技术保障**
- 多重网络连接回退机制
- 智能错误处理和重试
- 完善的文件管理系统
- 详细的诊断和日志功能

---

**🎯 开始使用**: 建议从 `README.md` 开始，然后根据您的需求查看相应的专项文档。

**💡 提示**: 所有文档都包含详细的示例和实用建议，建议结合实际操作进行学习。

**⚠️ 重要**: 投资有风险，本工具仅供参考，不构成投资建议！
