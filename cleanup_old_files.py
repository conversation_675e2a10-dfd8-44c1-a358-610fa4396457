#!/usr/bin/env python3
"""
清理根目录中的旧分析文件，将它们移动到指定目录
"""

import os
import shutil
import glob
from datetime import datetime
import argparse

class FileCleanup:
    def __init__(self, target_dir="old_reports"):
        self.target_dir = target_dir
        self.moved_files = []
        self.skipped_files = []
        
        # 创建目标目录
        os.makedirs(self.target_dir, exist_ok=True)
    
    def find_analysis_files(self):
        """查找根目录中的分析文件"""
        patterns = [
            "*analysis*.md",
            "*gemini*.md", 
            "*bottom_stocks*.md",
            "*robust*.md",
            "*enhanced*.md",
            "*analysis*.json",
            "*bottom_stocks*.json"
        ]
        
        files = []
        for pattern in patterns:
            files.extend(glob.glob(pattern))
        
        # 去重并过滤掉不应该移动的文件
        exclude_patterns = [
            "README*.md",
            "GUIDE*.md", 
            "*GUIDE*.md",
            "OUTPUT_DIRECTORY_GUIDE.md",
            "ENHANCED_ANALYSIS_GUIDE.md",
            "FAILURE_ANALYSIS.md",
            "GEMINI_FIX_NOTES.md"
        ]
        
        filtered_files = []
        for file in set(files):
            should_exclude = False
            for exclude_pattern in exclude_patterns:
                if glob.fnmatch.fnmatch(file, exclude_pattern):
                    should_exclude = True
                    break
            
            if not should_exclude and os.path.isfile(file):
                filtered_files.append(file)
        
        return filtered_files
    
    def move_file(self, source_file):
        """移动单个文件到目标目录"""
        try:
            # 获取文件信息
            stat = os.stat(source_file)
            mtime = datetime.fromtimestamp(stat.st_mtime)
            size = stat.st_size
            
            # 构建目标路径
            target_path = os.path.join(self.target_dir, source_file)
            
            # 如果目标文件已存在，添加时间戳
            if os.path.exists(target_path):
                name, ext = os.path.splitext(source_file)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                target_path = os.path.join(self.target_dir, f"{name}_{timestamp}{ext}")
            
            # 移动文件
            shutil.move(source_file, target_path)
            
            self.moved_files.append({
                'source': source_file,
                'target': target_path,
                'size': size,
                'mtime': mtime
            })
            
            return True
            
        except Exception as e:
            self.skipped_files.append({
                'file': source_file,
                'error': str(e)
            })
            return False
    
    def cleanup(self, dry_run=False):
        """执行清理操作"""
        print("🧹 开始清理根目录中的旧分析文件")
        print("=" * 50)
        
        # 查找文件
        files_to_move = self.find_analysis_files()
        
        if not files_to_move:
            print("✅ 根目录中没有找到需要清理的分析文件")
            return
        
        print(f"📄 找到 {len(files_to_move)} 个文件需要清理:")
        for file in files_to_move:
            stat = os.stat(file)
            size_str = f"{stat.st_size/1024:.1f}KB" if stat.st_size > 1024 else f"{stat.st_size}B"
            mtime_str = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')
            print(f"  📄 {file} ({size_str}, {mtime_str})")
        
        if dry_run:
            print(f"\n🔍 预览模式 - 文件将被移动到: {self.target_dir}")
            print("使用 --execute 参数执行实际移动操作")
            return
        
        print(f"\n📁 移动文件到: {self.target_dir}")
        
        # 移动文件
        for file in files_to_move:
            if self.move_file(file):
                print(f"  ✅ 已移动: {file}")
            else:
                print(f"  ❌ 移动失败: {file}")
        
        # 显示结果
        self.show_results()
    
    def show_results(self):
        """显示清理结果"""
        print(f"\n📊 清理结果:")
        print("=" * 30)
        
        if self.moved_files:
            print(f"✅ 成功移动 {len(self.moved_files)} 个文件:")
            total_size = sum(f['size'] for f in self.moved_files)
            total_size_str = f"{total_size/1024:.1f}KB" if total_size > 1024 else f"{total_size}B"
            
            for file_info in self.moved_files:
                size_str = f"{file_info['size']/1024:.1f}KB" if file_info['size'] > 1024 else f"{file_info['size']}B"
                print(f"  📄 {file_info['source']} → {file_info['target']} ({size_str})")
            
            print(f"\n📊 总计移动: {total_size_str}")
        
        if self.skipped_files:
            print(f"\n❌ 跳过 {len(self.skipped_files)} 个文件:")
            for file_info in self.skipped_files:
                print(f"  📄 {file_info['file']}: {file_info['error']}")
        
        if self.moved_files:
            print(f"\n📁 文件已移动到: {os.path.abspath(self.target_dir)}")
            print("💡 如需恢复，可以手动将文件移回根目录")

def main():
    parser = argparse.ArgumentParser(description="清理根目录中的旧分析文件")
    parser.add_argument("--target_dir", type=str, default="old_reports",
                       help="目标目录 (默认: old_reports)")
    parser.add_argument("--execute", action="store_true",
                       help="执行实际移动操作 (默认为预览模式)")
    
    args = parser.parse_args()
    
    try:
        cleanup = FileCleanup(args.target_dir)
        cleanup.cleanup(dry_run=not args.execute)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了清理过程")
    except Exception as e:
        print(f"❌ 清理过程中出错: {e}")

if __name__ == "__main__":
    main()
