#!/usr/bin/env python3
"""
股票趋势深度分析脚本
分析排名上升与涨幅的关系，以及行业和概念的特征
"""

import pandas as pd
import os
from collections import defaultdict, Counter

def load_and_analyze_data():
    """加载数据并进行深度分析"""
    
    # 模拟从运行结果中提取的数据
    rising_stocks_data = [
        {"code": "300531", "name": "优博讯", "rank_change": 34, "total_gain": 19.22, "concepts": "华为鸿蒙,华为海思概念,跨境支付", "industry": "计算机设备"},
        {"code": "300746", "name": "汉嘉设计", "rank_change": 41, "total_gain": 15.85, "concepts": "DeepSeek概念,AI智能体,科技重组预期概念", "industry": "工程咨询服务"},
        {"code": "002550", "name": "千红制药", "rank_change": 32, "total_gain": 12.60, "concepts": "", "industry": "化学制药"},
        {"code": "300386", "name": "飞天诚信", "rank_change": 30, "total_gain": 11.13, "concepts": "跨境支付", "industry": "计算机设备"},
        {"code": "002878", "name": "元隆雅图", "rank_change": 29, "total_gain": 10.47, "concepts": "字节跳动概念,冰雪旅游概念,线上送礼物概念,谷子经济概念,服务消费概念,AI营销,小红书概念", "industry": "广告营销"},
        {"code": "002383", "name": "合众思壮", "rank_change": 27, "total_gain": 10.09, "concepts": "低空经济", "industry": "军工电子"},
        {"code": "300497", "name": "富祥药业", "rank_change": 21, "total_gain": 9.40, "concepts": "合成生物", "industry": "化学制药"},
        {"code": "300541", "name": "先进数通", "rank_change": 38, "total_gain": 8.64, "concepts": "英伟达概念,超聚变,大厂算力梳理", "industry": "IT服务"},
        {"code": "002821", "name": "凯莱英", "rank_change": 43, "total_gain": 7.94, "concepts": "合成生物,减肥药概念", "industry": "医疗服务"},
        {"code": "002437", "name": "誉衡药业", "rank_change": 36, "total_gain": 7.46, "concepts": "", "industry": "化学制药"},
    ]
    
    return pd.DataFrame(rising_stocks_data)

def analyze_rank_vs_gain_correlation(df):
    """分析排名变化与涨幅的相关性"""
    print("🔍 排名变化与涨幅相关性分析")
    print("=" * 50)
    
    correlation = df['rank_change'].corr(df['total_gain'])
    print(f"相关系数: {correlation:.3f}")
    
    if correlation > 0.3:
        print("✅ 正相关：排名上升幅度越大，涨幅越高")
    elif correlation < -0.3:
        print("❌ 负相关：排名上升幅度越大，涨幅越低")
    else:
        print("➡️ 弱相关：排名变化与涨幅关系不明显")
    
    print(f"\n📊 统计数据:")
    print(f"平均排名上升: {df['rank_change'].mean():.1f} 位")
    print(f"平均涨幅: {df['total_gain'].mean():.2f}%")
    print(f"最大排名上升: {df['rank_change'].max()} 位 ({df.loc[df['rank_change'].idxmax(), 'name']})")
    print(f"最大涨幅: {df['total_gain'].max():.2f}% ({df.loc[df['total_gain'].idxmax(), 'name']})")

def analyze_industry_patterns(df):
    """分析行业模式"""
    print("\n🏭 行业分布分析")
    print("=" * 50)
    
    industry_stats = df.groupby('industry').agg({
        'total_gain': ['mean', 'count'],
        'rank_change': 'mean'
    }).round(2)
    
    industry_stats.columns = ['平均涨幅', '股票数量', '平均排名上升']
    industry_stats = industry_stats.sort_values('平均涨幅', ascending=False)
    
    print(industry_stats)
    
    print(f"\n🎯 行业特点:")
    for industry, group in df.groupby('industry'):
        avg_gain = group['total_gain'].mean()
        count = len(group)
        if avg_gain > df['total_gain'].mean():
            print(f"🔥 {industry}: 平均涨幅 {avg_gain:.2f}% ({count}只) - 表现优异")
        else:
            print(f"📊 {industry}: 平均涨幅 {avg_gain:.2f}% ({count}只)")

def analyze_concept_patterns(df):
    """分析概念热点"""
    print("\n🎨 概念热点分析")
    print("=" * 50)
    
    # 提取所有概念
    all_concepts = []
    concept_gains = defaultdict(list)
    
    for _, row in df.iterrows():
        if row['concepts']:
            concepts = [c.strip() for c in row['concepts'].split(',')]
            all_concepts.extend(concepts)
            for concept in concepts:
                concept_gains[concept].append(row['total_gain'])
    
    # 统计概念频次和平均涨幅
    concept_stats = []
    for concept, gains in concept_gains.items():
        concept_stats.append({
            'concept': concept,
            'count': len(gains),
            'avg_gain': sum(gains) / len(gains),
            'max_gain': max(gains)
        })
    
    concept_df = pd.DataFrame(concept_stats)
    concept_df = concept_df.sort_values('avg_gain', ascending=False)
    
    print("🔥 热门概念 (按平均涨幅排序):")
    for _, row in concept_df.head(10).iterrows():
        print(f"  {row['concept']}: 平均涨幅 {row['avg_gain']:.2f}% ({row['count']}只股票)")
    
    # 识别超级概念
    print(f"\n⭐ 超级概念 (出现频次≥2):")
    super_concepts = concept_df[concept_df['count'] >= 2]
    for _, row in super_concepts.iterrows():
        print(f"  {row['concept']}: {row['count']}只股票, 平均涨幅 {row['avg_gain']:.2f}%")

def analyze_performance_tiers(df):
    """分析表现层级"""
    print("\n🏆 表现层级分析")
    print("=" * 50)
    
    # 定义层级
    df['performance_tier'] = pd.cut(df['total_gain'], 
                                   bins=[-float('inf'), 0, 5, 10, float('inf')],
                                   labels=['下跌', '小涨(0-5%)', '中涨(5-10%)', '大涨(≥10%)'])
    
    tier_stats = df.groupby('performance_tier').agg({
        'rank_change': 'mean',
        'total_gain': 'mean',
        'name': 'count'
    }).round(2)
    
    tier_stats.columns = ['平均排名上升', '平均涨幅', '股票数量']
    
    print(tier_stats)
    
    print(f"\n🎯 层级特征:")
    for tier, group in df.groupby('performance_tier'):
        stocks = ', '.join(group['name'].tolist())
        print(f"{tier}: {stocks}")

def generate_insights(df):
    """生成洞察和建议"""
    print("\n💡 关键洞察")
    print("=" * 50)
    
    # 计算一些关键指标
    high_performers = df[df['total_gain'] >= 10]
    tech_stocks = df[df['industry'].str.contains('计算机|IT|工程', na=False)]
    
    insights = []
    
    # 洞察1: 科技股表现
    if len(tech_stocks) > 0:
        tech_avg_gain = tech_stocks['total_gain'].mean()
        overall_avg = df['total_gain'].mean()
        if tech_avg_gain > overall_avg:
            insights.append(f"🚀 科技类股票表现突出，平均涨幅 {tech_avg_gain:.2f}% vs 整体 {overall_avg:.2f}%")
    
    # 洞察2: 排名与涨幅关系
    correlation = df['rank_change'].corr(df['total_gain'])
    if correlation > 0.3:
        insights.append(f"📈 排名上升幅度与涨幅呈正相关 (r={correlation:.3f})，排名变化是涨幅的先行指标")
    
    # 洞察3: 高涨幅股票特征
    if len(high_performers) > 0:
        avg_rank_change = high_performers['rank_change'].mean()
        insights.append(f"🎯 高涨幅股票(≥10%)平均排名上升 {avg_rank_change:.1f} 位，显示强劲上升动能")
    
    # 洞察4: 概念热点
    ai_related = df[df['concepts'].str.contains('AI|DeepSeek|华为|智能', na=False)]
    if len(ai_related) > 0:
        ai_avg_gain = ai_related['total_gain'].mean()
        insights.append(f"🤖 AI相关概念股表现优异，平均涨幅 {ai_avg_gain:.2f}%")
    
    for i, insight in enumerate(insights, 1):
        print(f"{i}. {insight}")
    
    print(f"\n📋 投资建议:")
    print("1. 🎯 关注排名快速上升的科技股，特别是AI、华为生态相关")
    print("2. 📊 排名上升30位以上的股票值得重点关注")
    print("3. 🔍 医药股中的创新药、合成生物概念表现较好")
    print("4. ⚠️ 注意风险控制，部分股票排名上升但涨幅为负")

def main():
    """主函数"""
    print("🎯 股票趋势深度分析报告")
    print("=" * 60)
    
    df = load_and_analyze_data()
    
    analyze_rank_vs_gain_correlation(df)
    analyze_industry_patterns(df)
    analyze_concept_patterns(df)
    analyze_performance_tiers(df)
    generate_insights(df)
    
    print(f"\n" + "=" * 60)
    print("📊 分析完成！")

if __name__ == "__main__":
    main()
