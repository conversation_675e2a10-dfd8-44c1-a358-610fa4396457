# Gemini API 股票分析工具使用指南

## 🚀 快速开始

### 1. 获取 Gemini API 密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的 Google 账户
3. 点击 "Create API Key" 创建新的 API 密钥
4. 复制生成的 API 密钥

### 2. 设置环境变量

#### macOS/Linux:
```bash
export GEMINI_API_KEY="your_api_key_here"
```

#### Windows:
```cmd
set GEMINI_API_KEY=your_api_key_here
```

#### 或者在 Python 脚本中直接使用:
```bash
python gemini_stock_analyzer.py --input_dir output --api_key "your_api_key_here"
```

### 3. 安装依赖

```bash
pip install requests pandas
```

### 4. 运行分析

```bash
# 使用环境变量中的 API 密钥
python gemini_stock_analyzer.py --input_dir output

# 或者直接提供 API 密钥
python gemini_stock_analyzer.py --input_dir output --api_key "your_api_key_here"
```

## 📊 功能特性

### 🔍 数据分析能力
- ✅ 自动加载多日股票数据
- ✅ 识别排名连续上升的股票
- ✅ 提取板块、概念、市值等关键信息
- ✅ 计算涨幅统计和排名变化

### 🤖 AI 分析维度
- **📈 涨幅特征分析**: 识别高涨幅股票的共同特点
- **🏭 板块效应分析**: 找出表现最优的申万板块
- **🎯 概念热点追踪**: 识别当前最热门的投资概念
- **💎 市值效应研究**: 分析不同市值股票的表现差异
- **📊 投资策略建议**: 提供具体的选股和操作建议
- **⚠️ 风险提示**: 识别潜在的投资风险点

### 📄 输出格式
- **控制台输出**: 实时显示分析进度和结果
- **Markdown 报告**: 自动生成带时间戳的分析报告文件
- **结构化数据**: JSON 格式的数据便于进一步处理

## 🎯 分析提示词模板

脚本使用的核心提示词包含以下分析维度：

```
## 📊 核心发现分析
1. 涨幅特征：分析涨幅分布规律，识别高涨幅股票的共同特点
2. 板块效应：分析申万板块的表现差异，找出表现最优的板块
3. 概念热点：识别当前市场最热门的概念，分析概念轮动趋势
4. 市值效应：分析不同市值股票的表现差异

## 🎯 投资洞察
1. 强势板块：哪些板块表现最突出？原因是什么？
2. 概念机会：哪些概念最值得关注？持续性如何？
3. 选股策略：基于数据总结出的选股规律和策略
4. 风险提示：需要注意的风险点和陷阱

## 📈 市场趋势判断
1. 资金偏好：当前资金更偏好什么类型的股票？
2. 热点轮动：概念和板块的轮动特征
3. 后市展望：基于当前数据对后市的判断

## 💡 实用建议
1. 优选标准：给出具体的选股标准和组合建议
2. 操作策略：提供具体的买卖时机和仓位建议
3. 风险控制：风险管理的具体措施
```

## 📝 使用示例

### 基本用法
```bash
# 设置 API 密钥
export GEMINI_API_KEY="your_gemini_api_key"

# 运行分析
python gemini_stock_analyzer.py --input_dir output
```

### 预期输出
```
🚀 开始股票趋势 AI 分析
============================================================
📊 正在加载股票数据...
  ✅ 20250529: 156 只股票
  ✅ 20250530: 123 只股票
  ✅ 20250603: 164 只股票
🔍 正在分析排名上升股票...
  📈 发现 33 只排名上升股票
🤖 正在调用 Gemini API 进行分析...

============================================================
🎯 Gemini AI 分析结果
============================================================
[AI 分析内容...]

📄 分析报告已保存到: gemini_analysis_20250115_143022.md
```

## ⚙️ 自定义配置

### 修改分析参数
您可以在 `gemini_stock_analyzer.py` 中修改以下参数：

```python
# API 配置
"temperature": 0.7,        # 创造性程度 (0-1)
"topK": 40,               # 候选词数量
"topP": 0.95,             # 核心采样概率
"maxOutputTokens": 2048,   # 最大输出长度

# 数据筛选
rising_stocks[:10]         # 分析前10名股票
```

### 自定义提示词
您可以修改 `create_analysis_prompt` 方法来自定义分析维度和要求。

## 🔧 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   错误: 请设置 GEMINI_API_KEY 环境变量或提供 API 密钥
   解决: 检查 API 密钥是否正确设置
   ```

2. **网络连接问题**
   ```
   错误: API 调用异常: timeout
   解决: 检查网络连接，可能需要使用代理
   ```

3. **数据文件不存在**
   ```
   错误: 没有找到有效的股票数据
   解决: 确认 input_dir 路径正确，包含 above_cloud_daily_change.csv 文件
   ```

### API 限制
- Gemini API 有请求频率限制
- 免费版本有每日请求次数限制
- 单次请求的 token 数量有限制

## 📈 进阶用法

### 批量分析
```bash
# 分析多个时间段
for dir in output_20250529 output_20250530 output_20250603; do
    python gemini_stock_analyzer.py --input_dir $dir
done
```

### 结果对比
生成的 Markdown 报告可以用于：
- 不同时期的分析结果对比
- 投资策略的回测验证
- 市场趋势的长期跟踪

## 🎯 最佳实践

1. **定期运行**: 建议每日或每周运行分析，跟踪市场变化
2. **结果验证**: 将 AI 分析结果与实际市场表现对比验证
3. **策略调整**: 根据分析结果调整投资策略和风险控制
4. **数据备份**: 定期备份分析报告，建立历史数据库

---

**注意**: 本工具仅供参考，投资有风险，决策需谨慎！
