"""
K线图绘制工具模块
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import pandas as pd

def plot_candlestick(ax, data, time_markers=None, add_hover=True):
    """
    在指定的坐标轴上绘制K线图
    
    参数:
    ax: 坐标轴对象
    data: 股票数据
    time_markers: 列表，包含需要标记的时间点和说明，格式为[{'time': '2024-03-21', 'text': '买入', 'color': 'red'}, ...]
    add_hover: 是否添加鼠标悬停效果
    
    返回:
    candle_data: K线数据列表，包含每根K线的位置和数据信息
    tooltip_annotation: 悬停提示框对象
    """
    # 设置x轴日期格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

    # 计算每个蜡烛的宽度
    width = 0.6
    
    # 存储K线数据和位置信息，用于鼠标悬停显示
    candle_data = []

    # 绘制K线
    for i, (idx, row) in enumerate(data.iterrows()):
        # 确定颜色（涨为红，跌为绿）- 中国市场习惯
        color = 'red' if row['Close'] >= row['Open'] else 'green'

        # 绘制实体
        rect = plt.Rectangle(
            (i - width/2, min(row['Open'], row['Close'])),
            width,
            abs(row['Close'] - row['Open']),
            facecolor=color,
            edgecolor=color,
            alpha=0.8
        )
        ax.add_patch(rect)

        # 绘制上下影线
        ax.plot([i, i], [row['Low'], min(row['Open'], row['Close'])],
                color=color, linewidth=1)
        ax.plot([i, i], [max(row['Open'], row['Close']),
                row['High']], color=color, linewidth=1)
        
        # 计算涨跌幅
        if i > 0:
            prev_close = data.iloc[i-1]['Close']
            change_pct = (row['Close'] - prev_close) / prev_close * 100
        else:
            change_pct = 0
            
        # 存储K线数据和位置信息
        candle_data.append({
            'index': i,
            'date': idx.strftime('%Y-%m-%d'),
            'open': row['Open'],
            'high': row['High'],
            'low': row['Low'],
            'close': row['Close'],
            'change_pct': change_pct,
            'volume': row['Volume'] if 'Volume' in row else 0,
            'x_range': (i - width/2, i + width/2),
            'y_range': (row['Low'], row['High'])
        })

    # 设置x轴刻度
    ax.set_xticks(range(0, len(data), max(1, len(data) // 10)))
    ax.set_xticklabels([data.index[i].strftime('%Y-%m-%d')
                       for i in ax.get_xticks()])

    # 旋转x轴标签以避免重叠
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.3)
    
    # 添加时间标记
    if time_markers:
        for marker in time_markers:
            try:
                # 找到对应日期的索引位置
                marker_date = pd.to_datetime(marker['time'])
                if marker_date in data.index:
                    idx = data.index.get_loc(marker_date)
                    price = data['Close'].iloc[idx]
                    color = marker.get('color', 'red')  # 默认使用红色
                    
                    # 绘制箭头
                    ax.annotate(
                        marker.get('text', ''),  # 标记文本
                        xy=(idx, price),  # 箭头指向的位置
                        xytext=(idx, price * 1.02),  # 文本位置（略高于价格点）
                        color=color,
                        weight='bold',
                        ha='center',
                        va='bottom',
                        arrowprops=dict(
                            arrowstyle='->',
                            color=color,
                            connectionstyle='arc3,rad=0'
                        )
                    )
            except (KeyError, ValueError) as e:
                print(f"无法添加时间标记 {marker['time']}: {str(e)}")
    
    tooltip_annotation = None
    if add_hover:
        # 添加鼠标悬停事件处理
        tooltip_annotation = ax.annotate('', 
                                        xy=(0, 0), 
                                        xytext=(10, 10),
                                        textcoords='offset points',
                                        bbox=dict(boxstyle='round,pad=0.5', fc='#e6f2ff', alpha=0.9, edgecolor='#99ccff'),
                                        visible=False)
    
    return candle_data, tooltip_annotation

def add_candlestick_hover(fig, ax, candle_data, tooltip_annotation, cost_price=None):
    """
    为K线图添加鼠标悬停效果
    
    参数:
    fig: 图形对象
    ax: 坐标轴对象
    candle_data: K线数据列表
    tooltip_annotation: 悬停提示框对象
    cost_price: 成本价，可选
    
    返回:
    事件连接ID
    """
    def hover(event):
        if event.inaxes == ax:
            for candle in candle_data:
                x_min, x_max = candle['x_range']
                y_min, y_max = candle['y_range']
                
                if x_min <= event.xdata <= x_max and y_min <= event.ydata <= y_max:
                    # 更新注释内容和位置
                    tooltip_text = (
                        f"日期: {candle['date']}\n"
                        f"开盘: {candle['open']:.2f}\n"
                        f"最高: {candle['high']:.2f}\n"
                        f"最低: {candle['low']:.2f}\n"
                        f"收盘: {candle['close']:.2f}\n"
                        f"涨跌幅: {candle['change_pct']:.2f}%\n"
                        f"成交量: {int(candle['volume'])}"
                    )
                    
                    # 如果有成本价，显示盈亏信息
                    if cost_price is not None and cost_price > 0:
                        profit_pct = (candle['close'] - cost_price) / cost_price * 100
                        tooltip_text += f"\n相对成本: {profit_pct:.2f}%"
                    
                    tooltip_annotation.set_text(tooltip_text)
                    
                    # 调整提示框位置，防止超出右侧边界
                    # 获取图表的边界
                    fig_width = fig.get_figwidth() * fig.dpi
                    # 计算鼠标在图表中的相对位置
                    transform = ax.transData.transform
                    x_display, y_display = transform((event.xdata, event.ydata))
                    
                    # 如果鼠标位置靠近右侧边界，将提示框显示在左侧
                    if x_display > fig_width * 0.8:
                        tooltip_annotation.xytext = (-120, 10)
                    else:
                        tooltip_annotation.xytext = (10, 10)
                        
                    tooltip_annotation.xy = (event.xdata, event.ydata)
                    tooltip_annotation.set_visible(True)
                    fig.canvas.draw_idle()
                    return
            
            # 如果鼠标不在任何K线上，隐藏注释
            if tooltip_annotation.get_visible():
                tooltip_annotation.set_visible(False)
                fig.canvas.draw_idle()
    
    def leave(event):
        if tooltip_annotation.get_visible():
            tooltip_annotation.set_visible(False)
            fig.canvas.draw_idle()
    
    # 连接事件处理函数
    hover_cid = fig.canvas.mpl_connect('motion_notify_event', hover)
    leave_cid = fig.canvas.mpl_connect('axes_leave_event', leave)
    
    return [hover_cid, leave_cid]

def plot_volume(ax, data):
    """
    在指定的坐标轴上绘制成交量图
    
    参数:
    ax: 坐标轴对象
    data: 股票数据
    """
    # 计算每个柱的宽度
    width = 0.6

    # 绘制成交量柱状图
    for i, (idx, row) in enumerate(data.iterrows()):
        # 确定颜色（涨为红，跌为绿）- 中国市场习惯
        color = 'red' if row['Close'] >= row['Open'] else 'green'

        # 绘制成交量柱
        ax.bar(i, row['Volume'], width=width, color=color, alpha=0.5)

    # 设置x轴刻度
    ax.set_xticks(range(0, len(data), max(1, len(data) // 10)))
    ax.set_xticklabels([data.index[i].strftime('%Y-%m-%d')
                       for i in ax.get_xticks()])

    # 旋转x轴标签以避免重叠
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.3)

def plot_cost_line(ax, data, cost_price):
    """
    在K线图上绘制成本线
    
    参数:
    ax: 坐标轴对象
    data: 股票数据
    cost_price: 成本价
    """
    if cost_price <= 0:
        return
        
    # 绘制成本线
    ax.axhline(y=cost_price, color='blue', linestyle='-', linewidth=1.5, alpha=0.7)
    
    # 添加成本线标签
    current_price = data['Close'].iloc[-1]
    profit_pct = (current_price - cost_price) / cost_price * 100
    label_color = 'red' if profit_pct >= 0 else 'green'
    label_text = f"成本线: {cost_price:.2f} (当前盈亏: {profit_pct:.2f}%)"
    
    # 在右侧显示成本线标签
    ax.text(len(data)*0.99, cost_price*1.002,
            label_text,
            color=label_color, fontsize=9, fontweight='bold',
            horizontalalignment='right', verticalalignment='bottom',
            bbox=dict(facecolor='white', alpha=0.8, edgecolor=label_color, pad=2))
    
    # 绘制成本区域底色 (盈利区域为淡红色，亏损区域为淡绿色)
    if profit_pct >= 0:  # 盈利
        ax.axhspan(cost_price, current_price, color='red', alpha=0.05)
    else:  # 亏损
        ax.axhspan(current_price, cost_price, color='green', alpha=0.05)