"""
股票数据分析工具主程序
"""

import tkinter as tk
import sys
import os
import argparse

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.ui.excel_viewer import ExcelViewerApp
from src.ui.stock_kline.app import StockKLineApp


def main():
    """主程序入口点"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="股票数据分析工具")
    parser.add_argument("--mode", choices=["excel", "kline"], default="excel", 
                        help="运行模式：excel(Excel查看器) 或 kline(K线图查看器)")
    parser.add_argument("symbol", nargs="?", help="股票代码(仅在K线图模式下有效)")
    parser.add_argument("--cost", type=float, help="成本价格(仅在K线图模式下有效)")
    args = parser.parse_args()
    
    # 创建主窗口
    root = tk.Tk()
    
    # 根据模式启动不同的应用
    if args.mode == "kline":
        app = StockKLineApp(root, args.symbol, cost_price=args.cost)
    else:
        root.title("股票数据Excel查看器")
        app = ExcelViewerApp(root)
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main() 