"""
图表形态识别模块
实现各种技术形态的识别，如双重底、头肩底等
"""
from typing import Dict, List, Tuple
import numpy as np
import pandas as pd
from ..base.signal_base import SignalBase

class ChartPatterns(SignalBase):
    """图表形态识别类"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化图表形态识别器
        
        Args:
            verbose: 是否打印详细日志
        """
        self.verbose = verbose
        self.pattern_weights = {
            'double_bottom': 0.6,  # 双重底权重
            'head_shoulders_bottom': 0.4  # 头肩底权重
        }
        
    def calculate(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        计算图表形态得分
        
        Args:
            data: DataFrame，包含OHLCV数据
            
        Returns:
            Tuple[float, Dict]: (总得分, 详细信息)
        """
        return self.analyze(data)
        
    def _find_significant_bottoms(self, data: pd.DataFrame, 
                                window: int = 20,
                                significance_threshold: float = 0.02) -> List[Tuple]:
        """
        寻找重要的底部点位
        
        Args:
            data: DataFrame，包含OHLCV数据
            window: 局部重要性的窗口大小
            significance_threshold: 显著性阈值
            
        Returns:
            List[Tuple]: 重要底部点位列表，每个元素为(日期, 价格, 索引)
        """
        if self.verbose:
            print("\n寻找重要底部点位...")
            print(f"- 窗口大小: {window}")
            print(f"- 显著性阈值: {significance_threshold}")
            
        lows = data['Low'].values
        dates = data.index
        bottoms = []
        
        for i in range(window, len(lows)-window):
            # 检查是否是局部最低点
            if lows[i] > min(lows[i-1:i+2]):
                continue
                
            # 计算前后窗口的最低价格
            pre_window_low = min(lows[i-window:i])
            post_window_low = min(lows[i+1:i+window+1])
            
            # 计算相对于窗口的显著性
            significance = min(
                (pre_window_low - lows[i]) / pre_window_low,
                (post_window_low - lows[i]) / post_window_low
            )
            
            if significance > significance_threshold:
                bottoms.append((dates[i], lows[i], i))
                if self.verbose:
                    print(f"  ✓ 发现底部点位: {dates[i].strftime('%Y-%m-%d')}, 价格: {lows[i]:.2f}, 显著性: {significance:.3f}")
                    
        if self.verbose:
            print(f"共找到 {len(bottoms)} 个重要底部点位")
                
        return bottoms
        
    def _check_volume_confirmation(self, data: pd.DataFrame, 
                                 breakout_index: int,
                                 window: int = 20) -> float:
        """
        检查成交量确认信号
        
        Args:
            data: DataFrame，包含OHLCV数据
            breakout_index: 突破点的索引
            window: 对比窗口大小
            
        Returns:
            float: 成交量确认得分 (0-1)
        """
        if breakout_index < window or breakout_index >= len(data):
            return 0
            
        # 计算突破日的成交量相对于前期的倍数
        pre_avg_volume = data['Volume'].iloc[breakout_index-window:breakout_index].mean()
        breakout_volume = data['Volume'].iloc[breakout_index]
        
        volume_ratio = breakout_volume / pre_avg_volume
        return min(volume_ratio / 2, 1)  # 成交量是前期均量2倍以上得满分
        
    def _check_trend_background(self, data: pd.DataFrame,
                              start_index: int,
                              window: int = 60) -> float:
        """
        检查趋势背景
        
        Args:
            data: DataFrame，包含OHLCV数据
            start_index: 形态起始位置
            window: 趋势窗口大小
            
        Returns:
            float: 趋势背景得分 (0-1)，下跌趋势得分高
        """
        if start_index < window:
            return 0
            
        # 计算前期趋势
        pre_prices = data['Close'].iloc[start_index-window:start_index]
        trend = (pre_prices.iloc[-1] - pre_prices.iloc[0]) / pre_prices.iloc[0]
        
        # 下跌趋势得分高
        return min(-trend / 0.2, 1) if trend < 0 else 0
        
    def _find_neckline(self, data: pd.DataFrame,
                      points: List[Tuple],
                      is_head_shoulders: bool = False) -> Tuple[float, int]:
        """
        寻找颈线并检查突破
        
        Args:
            data: DataFrame，包含OHLCV数据
            points: 形态的关键点位
            is_head_shoulders: 是否是头肩底形态
            
        Returns:
            Tuple[float, int]: (颈线高度, 突破点索引)
        """
        if is_head_shoulders:
            # 头肩底的颈线连接左右肩的反弹高点
            left_peak_idx = self._find_peak_between(data, points[0][2], points[1][2])
            right_peak_idx = self._find_peak_between(data, points[1][2], points[2][2])
            
            if left_peak_idx is None or right_peak_idx is None:
                return 0, -1
                
            neckline_height = (data['High'].iloc[left_peak_idx] + 
                             data['High'].iloc[right_peak_idx]) / 2
        else:
            # 双重底的颈线是中间反弹高点
            middle_peak_idx = self._find_peak_between(data, points[0][2], points[1][2])
            if middle_peak_idx is None:
                return 0, -1
            neckline_height = data['High'].iloc[middle_peak_idx]
            
        # 寻找突破点
        for i in range(points[-1][2], len(data)):
            if data['Close'].iloc[i] > neckline_height:
                return neckline_height, i
                
        return neckline_height, -1
        
    def _find_peak_between(self, data: pd.DataFrame, start_idx: int, end_idx: int) -> int:
        """在两点之间寻找高点"""
        if start_idx >= end_idx or start_idx < 0 or end_idx >= len(data):
            return None
            
        highs = data['High'].iloc[start_idx:end_idx]
        return start_idx + highs.argmax()
        
    def identify_double_bottom(self, data: pd.DataFrame, 
                             window: int = 60,
                             tolerance: float = 0.02) -> Tuple[bool, float, Dict]:
        """
        识别双重底形态
        
        Args:
            data: DataFrame，包含OHLCV数据
            window: 寻找底部的时间窗口
            tolerance: 两个底部价格差异的容忍度（百分比）
            
        Returns:
            Tuple[bool, float, Dict]: (是否形成双重底, 可信度得分, 详细信息)
        """
        if self.verbose:
            print("\n=== 开始识别双重底形态 ===")
            print(f"- 时间窗口: {window} 天")
            print(f"- 价格容忍度: {tolerance*100}%")
            
        # 寻找重要的底部点位
        bottoms = self._find_significant_bottoms(data)
        
        if len(bottoms) < 2:
            if self.verbose:
                print("未找到足够的底部点")
            return False, 0, {'message': '未找到足够的底部点'}
            
        # 分析可能的双重底
        for i in range(len(bottoms)-1):
            for j in range(i+1, len(bottoms)):
                bottom1, bottom2 = bottoms[i], bottoms[j]
                
                # 检查两个底部的时间间隔
                time_diff = (bottom2[0] - bottom1[0]).days
                if time_diff < 20 or time_diff > window:
                    continue
                    
                # 检查价格相似度
                price_diff = abs(bottom2[1] - bottom1[1]) / bottom1[1]
                if price_diff > tolerance:
                    continue
                    
                if self.verbose:
                    print(f"\n检查可能的双重底:")
                    print(f"- 第一底: {bottom1[0].strftime('%Y-%m-%d')}, 价格: {bottom1[1]:.2f}")
                    print(f"- 第二底: {bottom2[0].strftime('%Y-%m-%d')}, 价格: {bottom2[1]:.2f}")
                    print(f"- 时间间隔: {time_diff} 天")
                    print(f"- 价格差异: {price_diff*100:.2f}%")
                    
                # 寻找颈线和突破点
                neckline_height, breakout_idx = self._find_neckline(data, [bottom1, bottom2])
                if breakout_idx == -1:  # 未突破颈线
                    if self.verbose:
                        print("× 未突破颈线")
                    continue
                    
                # 计算各个维度的得分
                pattern_score = self._calculate_double_bottom_confidence(
                    price_diff, time_diff, window
                )
                volume_score = self._check_volume_confirmation(data, breakout_idx)
                trend_score = self._check_trend_background(data, bottom1[2])
                
                if self.verbose:
                    print("\n得分详情:")
                    print(f"- 形态得分: {pattern_score:.2f}")
                    print(f"- 成交量确认得分: {volume_score:.2f}")
                    print(f"- 趋势背景得分: {trend_score:.2f}")
                
                # 综合得分
                confidence = (pattern_score * 0.4 + 
                            volume_score * 0.3 +
                            trend_score * 0.3)
                
                if self.verbose:
                    print(f"综合可信度得分: {confidence:.2f}")
                
                if confidence > 0.6:
                    if self.verbose:
                        print("✓ 形成有效的双重底形态!")
                    return True, confidence, {
                        'pattern': 'double_bottom',
                        'bottom1': bottom1[0].strftime('%Y-%m-%d'),
                        'bottom2': bottom2[0].strftime('%Y-%m-%d'),
                        'breakout': data.index[breakout_idx].strftime('%Y-%m-%d'),
                        'price_diff': price_diff,
                        'volume_score': volume_score,
                        'trend_score': trend_score,
                        'neckline': neckline_height
                    }
                    
        if self.verbose:
            print("\n× 未找到符合条件的双重底形态")
        return False, 0, {'message': '未找到符合条件的双重底'}
        
    def identify_head_shoulders_bottom(self, data: pd.DataFrame,
                                     window: int = 120,
                                     tolerance: float = 0.03) -> Tuple[bool, float, Dict]:
        """
        识别头肩底形态
        
        Args:
            data: DataFrame，包含OHLCV数据
            window: 寻找形态的时间窗口
            tolerance: 左右肩价格差异的容忍度
            
        Returns:
            Tuple[bool, float, Dict]: (是否形成头肩底, 可信度得分, 详细信息)
        """
        # 寻找重要的底部点位
        bottoms = self._find_significant_bottoms(data)
        
        if len(bottoms) < 3:
            return False, 0, {'message': '未找到足够的底部点'}
            
        # 分析可能的头肩底
        for i in range(len(bottoms)-2):
            for j in range(i+1, len(bottoms)-1):
                for k in range(j+1, len(bottoms)):
                    left_shoulder = bottoms[i]
                    head = bottoms[j]
                    right_shoulder = bottoms[k]
                    
                    # 检查时间间隔
                    time_span = (right_shoulder[0] - left_shoulder[0]).days
                    if time_span > window:
                        continue
                        
                    # 检查头部是否最低
                    if head[1] > left_shoulder[1] or head[1] > right_shoulder[1]:
                        continue
                        
                    # 检查左右肩的对称性
                    shoulder_diff = abs(right_shoulder[1] - left_shoulder[1]) / left_shoulder[1]
                    if shoulder_diff > tolerance:
                        continue
                        
                    # 寻找颈线和突破点
                    neckline_height, breakout_idx = self._find_neckline(
                        data, [left_shoulder, head, right_shoulder], True
                    )
                    if breakout_idx == -1:  # 未突破颈线
                        continue
                        
                    # 计算各个维度的得分
                    pattern_score = self._calculate_hs_bottom_confidence(
                        shoulder_diff, 
                        (head[1] - left_shoulder[1]) / left_shoulder[1],
                        time_span,
                        window
                    )
                    volume_score = self._check_volume_confirmation(data, breakout_idx)
                    trend_score = self._check_trend_background(data, left_shoulder[2])
                    
                    # 综合得分
                    confidence = (pattern_score * 0.4 + 
                                volume_score * 0.3 +
                                trend_score * 0.3)
                    
                    if confidence > 0.6:
                        return True, confidence, {
                            'pattern': 'head_shoulders_bottom',
                            'left_shoulder': left_shoulder[0].strftime('%Y-%m-%d'),
                            'head': head[0].strftime('%Y-%m-%d'),
                            'right_shoulder': right_shoulder[0].strftime('%Y-%m-%d'),
                            'breakout': data.index[breakout_idx].strftime('%Y-%m-%d'),
                            'shoulder_diff': shoulder_diff,
                            'volume_score': volume_score,
                            'trend_score': trend_score,
                            'neckline': neckline_height
                        }
                        
        return False, 0, {'message': '未找到符合条件的头肩底'}
        
    def _calculate_double_bottom_confidence(self, price_diff: float,
                                          time_diff: int,
                                          window: int) -> float:
        """计算双重底可信度得分"""
        price_score = 1 - min(price_diff / 0.02, 1)  # 价格相似度得分
        time_score = 1 - abs(time_diff - window/2) / (window/2)  # 时间间隔得分
        
        return (price_score * 0.6 + time_score * 0.4)
        
    def _calculate_hs_bottom_confidence(self, shoulder_diff: float,
                                      head_depth: float,
                                      time_span: int,
                                      window: int) -> float:
        """计算头肩底可信度得分"""
        symmetry_score = 1 - min(shoulder_diff / 0.03, 1)  # 对称性得分
        depth_score = min(head_depth / 0.1, 1)  # 头部深度得分
        time_score = 1 - time_span / window  # 时间跨度得分
        
        return (symmetry_score * 0.4 + depth_score * 0.4 + time_score * 0.2)
        
    def analyze(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        分析所有图表形态并返回综合得分
        
        Args:
            data: DataFrame，包含OHLCV数据
            
        Returns:
            Tuple[float, Dict]: (总得分, 详细信息)
        """
        patterns_found = {}
        total_score = 0
        
        # 检查双重底
        is_db, db_confidence, db_details = self.identify_double_bottom(data)
        if is_db:
            patterns_found['double_bottom'] = {
                'confidence': db_confidence,
                'details': db_details
            }
            total_score += db_confidence * self.pattern_weights['double_bottom']
            
        # 检查头肩底
        is_hsb, hsb_confidence, hsb_details = self.identify_head_shoulders_bottom(data)
        if is_hsb:
            patterns_found['head_shoulders_bottom'] = {
                'confidence': hsb_confidence,
                'details': hsb_details
            }
            total_score += hsb_confidence * self.pattern_weights['head_shoulders_bottom']
            
        return total_score * 100, {
            'patterns_found': patterns_found,
            'total_score': total_score * 100
        } 