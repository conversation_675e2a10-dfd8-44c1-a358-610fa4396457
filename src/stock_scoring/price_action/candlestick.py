"""
K线形态识别模块
"""
from typing import Dict, Tuple
import pandas as pd
from ..base.signal_base import SignalBase

class CandlestickPatterns(SignalBase):
    """K线形态识别"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化K线形态识别器
        
        Args:
            verbose: 是否打印详细日志
        """
        self.verbose = verbose
        self.patterns = {
            'basic_patterns': {
                'bullish_engulfing': 2.0,    # 看涨吞没
                'morning_star': 2.5,         # 早晨之星
                'hammer': 1.5,               # 锤子线
                'inverted_hammer': 1.5,      # 倒锤头线
                'piercing_line': 1.5,        # 刺透形态
            },
            'complex_patterns': {
                'three_white_soldiers': 2.0,  # 三白兵
                'three_black_crows': 2.0,     # 三黑鸦
                'evening_star': 2.5,          # 黄昏之星
                'dragonfly_doji': 1.5         # 蜻蜓十字星
            }
        }
    
    def analyze(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        分析K线形态并返回得分
        
        Args:
            data: DataFrame，包含OHLCV数据
            
        Returns:
            Tuple[float, Dict]: (总得分, 详细信息)
        """
        return self.calculate(data)
    
    def calculate(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        计算K线形态得分
        
        Args:
            data: 股票数据
            
        Returns:
            Tuple[float, Dict]: K线形态得分和详细信息
        """
        if self.verbose:
            print("\n=== 开始K线形态分析 ===")
            print(f"分析数据范围: {data.index[0]} 到 {data.index[-1]}")
            
        score = 0
        details = {'patterns_found': []}
        
        # 检查基础形态
        if self.verbose:
            print("\n检查基础K线形态...")
        basic_patterns_score = self._check_basic_patterns(data)
        
        # 检查复杂形态
        if self.verbose:
            print("\n检查复杂K线形态...")
        complex_patterns_score = self._check_complex_patterns(data)
        
        # 计算总分
        total_score = (basic_patterns_score * 0.6 + 
                      complex_patterns_score * 0.4)
                      
        if self.verbose:
            print("\n=== K线形态分析结果 ===")
            print(f"基础形态得分: {basic_patterns_score:.2f} (权重: 0.6)")
            print(f"复杂形态得分: {complex_patterns_score:.2f} (权重: 0.4)")
            print(f"总得分: {total_score:.2f}")
        
        return self.normalize_score(total_score), details
    
    def _check_basic_patterns(self, data: pd.DataFrame) -> float:
        """检查基础K线形态"""
        score = 0
        if self.verbose:
            print("检查以下基础形态:")
            for pattern, weight in self.patterns['basic_patterns'].items():
                print(f"  - {pattern} (权重: {weight})")
                
        for pattern, weight in self.patterns['basic_patterns'].items():
            if getattr(self, f'_is_{pattern}')(data, len(data)-1):
                score += weight
                if self.verbose:
                    print(f"  ✓ 发现 {pattern} 形态，得分 +{weight}")
                    
        if self.verbose:
            print(f"基础形态总得分: {score:.2f}")
        return score
    
    def _check_complex_patterns(self, data: pd.DataFrame) -> float:
        """检查复杂K线形态"""
        score = 0
        if self.verbose:
            print("检查以下复杂形态:")
            for pattern, weight in self.patterns['complex_patterns'].items():
                print(f"  - {pattern} (权重: {weight})")
                
        for pattern, weight in self.patterns['complex_patterns'].items():
            if getattr(self, f'_is_{pattern}')(data, len(data)-1):
                score += weight
                if self.verbose:
                    print(f"  ✓ 发现 {pattern} 形态，得分 +{weight}")
                    
        if self.verbose:
            print(f"复杂形态总得分: {score:.2f}")
        return score
    
    def _is_bullish_engulfing(self, data: pd.DataFrame, idx: int) -> bool:
        """判断是否为看涨吞没形态"""
        if idx < 1:
            return False
            
        prev_open = data['Open'].iloc[idx-1]
        prev_close = data['Close'].iloc[idx-1]
        curr_open = data['Open'].iloc[idx]
        curr_close = data['Close'].iloc[idx]
        
        return (prev_close < prev_open and  # 前一天为阴线
                curr_close > curr_open and  # 当天为阳线
                curr_open < prev_close and  # 当天开盘价低于前一天收盘价
                curr_close > prev_open)     # 当天收盘价高于前一天开盘价
    
    def _is_morning_star(self, data: pd.DataFrame, idx: int) -> bool:
        """判断是否为早晨之星形态"""
        if idx < 2:
            return False
            
        day1_open = data['Open'].iloc[idx-2]
        day1_close = data['Close'].iloc[idx-2]
        day2_open = data['Open'].iloc[idx-1]
        day2_close = data['Close'].iloc[idx-1]
        day3_open = data['Open'].iloc[idx]
        day3_close = data['Close'].iloc[idx]
        
        body1 = abs(day1_close - day1_open)
        body2 = abs(day2_close - day2_open)
        
        return (day1_close < day1_open and  # 第一天为阴线
                body2 < body1 * 0.3 and     # 第二天为小实体
                day3_close > day3_open and  # 第三天为阳线
                day2_open < day1_close and  # 第二天开盘价低于第一天收盘价
                day3_close > (day1_open + day1_close) / 2)  # 第三天收盘价收复第一天实体的一半以上
    
    def _is_hammer(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为锤子线形态
        条件：
        1. 下影线长度至少为实体长度的2倍
        2. 上影线长度很短或没有
        3. 在下跌趋势中出现
        """
        if idx < 5 or idx >= len(data):  # 需要一些历史数据来判断趋势
            return False
            
        open_price = data['Open'].iloc[idx]
        high_price = data['High'].iloc[idx]
        low_price = data['Low'].iloc[idx]
        close_price = data['Close'].iloc[idx]
        
        # 计算实体、上影线和下影线
        body = abs(close_price - open_price)
        if close_price >= open_price:
            upper_shadow = high_price - close_price
            lower_shadow = open_price - low_price
        else:
            upper_shadow = high_price - open_price
            lower_shadow = close_price - low_price
            
        # 判断是否在下跌趋势中
        prev_prices = data['Close'].iloc[idx-5:idx]
        is_downtrend = prev_prices.iloc[0] > prev_prices.iloc[-1]
        
        # 判断锤子线条件
        return (lower_shadow >= 2 * body and  # 下影线至少为实体的2倍
                upper_shadow <= body * 0.1 and  # 上影线很短
                is_downtrend)  # 在下跌趋势中
    
    def _is_inverted_hammer(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为倒锤头线形态
        条件与锤子线相似，但上下影线相反
        """
        if idx < 5 or idx >= len(data):
            return False
            
        open_price = data['Open'].iloc[idx]
        high_price = data['High'].iloc[idx]
        low_price = data['Low'].iloc[idx]
        close_price = data['Close'].iloc[idx]
        
        # 计算实体、上影线和下影线
        body = abs(close_price - open_price)
        if close_price >= open_price:
            upper_shadow = high_price - close_price
            lower_shadow = open_price - low_price
        else:
            upper_shadow = high_price - open_price
            lower_shadow = close_price - low_price
            
        # 判断是否在下跌趋势中
        prev_prices = data['Close'].iloc[idx-5:idx]
        is_downtrend = prev_prices.iloc[0] > prev_prices.iloc[-1]
        
        # 判断倒锤头线条件
        return (upper_shadow >= 2 * body and  # 上影线至少为实体的2倍
                lower_shadow <= body * 0.1 and  # 下影线很短
                is_downtrend)  # 在下跌趋势中
    
    def _is_piercing_line(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为刺透形态
        条件：
        1. 第一天为阴线
        2. 第二天为阳线
        3. 第二天开盘价低于第一天最低价
        4. 第二天收盘价高于第一天实体中点
        """
        if idx < 1 or idx >= len(data):
            return False
            
        day1_open = data['Open'].iloc[idx-1]
        day1_close = data['Close'].iloc[idx-1]
        day1_low = data['Low'].iloc[idx-1]
        day2_open = data['Open'].iloc[idx]
        day2_close = data['Close'].iloc[idx]
        
        is_day1_bearish = day1_close < day1_open
        is_day2_bullish = day2_close > day2_open
        piercing_condition = (day2_open < day1_low and 
                            day2_close > (day1_open + day1_close) / 2)
        
        return is_day1_bearish and is_day2_bullish and piercing_condition
    
    def _is_three_white_soldiers(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为三白兵形态
        条件：
        1. 连续三天为阳线
        2. 每天的开盘价在前一天实体内
        3. 每天的收盘价创新高
        4. 三天实体长度相近
        """
        if idx < 2 or idx >= len(data):
            return False
            
        # 获取三天的数据
        opens = data['Open'].iloc[idx-2:idx+1].values
        closes = data['Close'].iloc[idx-2:idx+1].values
        
        # 判断是否都是阳线
        if not all(closes[i] > opens[i] for i in range(3)):
            return False
            
        # 判断开盘价是否在前一天实体内
        for i in range(1, 3):
            if not (opens[i] > opens[i-1] and opens[i] < closes[i-1]):
                return False
                
        # 判断收盘价是否连续走高
        if not (closes[0] < closes[1] < closes[2]):
            return False
            
        # 判断实体长度是否相近（允许20%的差异）
        bodies = [closes[i] - opens[i] for i in range(3)]
        avg_body = sum(bodies) / 3
        if not all(abs(body - avg_body) / avg_body < 0.2 for body in bodies):
            return False
            
        return True
    
    def _is_three_black_crows(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为三黑鸦形态
        条件：
        1. 连续三天为阴线
        2. 每天的开盘价在前一天实体内
        3. 每天的收盘价创新低
        4. 三天实体长度相近
        """
        if idx < 2 or idx >= len(data):
            return False
            
        # 获取三天的数据
        opens = data['Open'].iloc[idx-2:idx+1].values
        closes = data['Close'].iloc[idx-2:idx+1].values
        
        # 判断是否都是阴线
        if not all(closes[i] < opens[i] for i in range(3)):
            return False
            
        # 判断开盘价是否在前一天实体内
        for i in range(1, 3):
            if not (opens[i] < opens[i-1] and opens[i] > closes[i-1]):
                return False
                
        # 判断收盘价是否连续走低
        if not (closes[0] > closes[1] > closes[2]):
            return False
            
        # 判断实体长度是否相近（允许20%的差异）
        bodies = [abs(closes[i] - opens[i]) for i in range(3)]
        avg_body = sum(bodies) / 3
        if not all(abs(body - avg_body) / avg_body < 0.2 for body in bodies):
            return False
            
        return True
    
    def _is_dragonfly_doji(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为蜻蜓十字星形态
        条件：
        1. 开盘价和收盘价相同或非常接近
        2. 上影线很短或没有
        3. 下影线很长
        """
        if idx >= len(data):
            return False
            
        open_price = data['Open'].iloc[idx]
        high_price = data['High'].iloc[idx]
        low_price = data['Low'].iloc[idx]
        close_price = data['Close'].iloc[idx]
        
        # 计算实体和影线
        body = abs(close_price - open_price)
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        price_range = high_price - low_price
        
        # 判断条件
        is_doji = body <= price_range * 0.1  # 实体很小
        has_long_lower_shadow = lower_shadow >= price_range * 0.6  # 下影线占比大
        has_short_upper_shadow = upper_shadow <= price_range * 0.1  # 上影线很短
        
        return is_doji and has_long_lower_shadow and has_short_upper_shadow
    
    def _is_evening_star(self, data: pd.DataFrame, idx: int) -> bool:
        """
        判断是否为黄昏之星形态
        条件：
        1. 第一天为大阳线
        2. 第二天为小实体（十字星）
        3. 第三天为阴线，收盘价低于第一天中点
        4. 在上升趋势中出现
        """
        if idx < 2 or idx >= len(data):
            return False
            
        # 获取三天的数据
        day1_open = data['Open'].iloc[idx-2]
        day1_close = data['Close'].iloc[idx-2]
        day2_open = data['Open'].iloc[idx-1]
        day2_close = data['Close'].iloc[idx-1]
        day3_open = data['Open'].iloc[idx]
        day3_close = data['Close'].iloc[idx]
        
        # 计算实体大小
        body1 = abs(day1_close - day1_open)
        body2 = abs(day2_close - day2_open)
        
        # 判断是否在上升趋势中
        prev_prices = data['Close'].iloc[max(0, idx-7):idx-2]
        is_uptrend = prev_prices.iloc[0] < prev_prices.iloc[-1]
        
        # 判断条件
        is_day1_bullish = day1_close > day1_open  # 第一天为阳线
        is_day2_small_body = body2 < body1 * 0.3  # 第二天为小实体
        is_day3_bearish = day3_close < day3_open  # 第三天为阴线
        price_condition = day3_close < (day1_open + day1_close) / 2  # 第三天收盘价低于第一天中点
        gap_up = day2_open > day1_close  # 第二天跳空高开
        gap_down = day3_open < day2_close  # 第三天跳空低开
        
        return (is_day1_bullish and is_day2_small_body and 
                is_day3_bearish and price_condition and 
                is_uptrend and (gap_up or gap_down)) 