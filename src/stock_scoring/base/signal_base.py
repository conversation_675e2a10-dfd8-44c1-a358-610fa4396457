"""
基础信号类模块
"""
from abc import ABC, abstractmethod
from typing import Dict, Tuple
import pandas as pd

class SignalBase(ABC):
    """信号基类"""
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        计算信号得分
        
        Args:
            data: 股票数据，包含OHLCV数据
            
        Returns:
            Tuple[float, Dict]: 信号得分（0-100）和详细信息
        """
        pass
    
    def normalize_score(self, score: float) -> float:
        """
        标准化得分到0-100区间
        
        Args:
            score: 原始得分
            
        Returns:
            float: 标准化后的得分
        """
        return max(0, min(100, score)) 