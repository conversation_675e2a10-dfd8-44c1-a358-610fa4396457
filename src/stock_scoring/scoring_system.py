"""
股票技术分析打分系统
"""
from typing import Dict, Optional, Tuple
import pandas as pd
from .base.signal_base import SignalBase
from .price_action.candlestick import CandlestickPatterns
from .price_action.chart_patterns import ChartPatterns
from .quantitative.trend import TrendIndicators

class StockScorer:
    """股票技术分析打分系统"""
    
    def __init__(self, config: Optional[Dict] = None, verbose: bool = False):
        """
        初始化打分系统
        
        Args:
            config: 配置信息，包含各个指标的权重等
            verbose: 是否打印详细日志
        """
        self.config = config or self._get_default_config()
        self.verbose = verbose
        self._initialize_signals()
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'price_action': {
                'weight': 0.5,
                'components': {
                    'candlestick': 0.3,
                    'chart_pattern': 0.4,  # 提高图表形态的权重
                    'support_resistance': 0.3
                }
            },
            'quantitative': {
                'weight': 0.5,
                'components': {
                    'trend': 0.3,
                    'momentum': 0.3,
                    'volume': 0.2,
                    'volatility': 0.2
                }
            }
        }
        
    def _initialize_signals(self):
        """初始化所有信号系统"""
        self.signals = {
            'price_action': {
                'candlestick': CandlestickPatterns(verbose=self.verbose),
                'chart_pattern': ChartPatterns(verbose=self.verbose),
                # TODO: 添加其他价格行为信号
            },
            'quantitative': {
                'trend': TrendIndicators(),
                # TODO: 添加其他量化指标
            }
        }
        
    def get_total_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        计算股票的总体技术分析得分
        
        Args:
            data: DataFrame，包含OHLCV数据
            
        Returns:
            Tuple[float, Dict]: (总得分, 详细得分信息)
        """
        if self.verbose:
            print("\n=== 开始计算股票技术分析得分 ===")
            print(f"数据范围: {data.index[0]} 到 {data.index[-1]}")
            
        # 数据预处理和验证
        data = self._preprocess_data(data)
        self._validate_data(data)
        
        total_score = 0
        score_details = {}
        
        # 计算价格行为得分
        if self.verbose:
            print("\n--- 计算价格行为得分 ---")
        price_action_score, price_action_details = self._calculate_price_action_score(data)
        total_score += price_action_score * self.config['price_action']['weight']
        score_details.update(price_action_details)
        
        if self.verbose:
            print(f"价格行为总得分: {price_action_score:.2f}")
            print(f"权重调整后得分: {price_action_score * self.config['price_action']['weight']:.2f}")
        
        # 计算量化指标得分
        if self.verbose:
            print("\n--- 计算量化指标得分 ---")
        quantitative_score, quantitative_details = self._calculate_quantitative_score(data)
        total_score += quantitative_score * self.config['quantitative']['weight']
        score_details.update(quantitative_details)
        
        if self.verbose:
            print(f"量化指标总得分: {quantitative_score:.2f}")
            print(f"权重调整后得分: {quantitative_score * self.config['quantitative']['weight']:.2f}")
        
        # 确保总分在0-100之间
        total_score = max(0, min(100, total_score))
        score_details['total_score'] = total_score
        
        if self.verbose:
            print("\n=== 最终得分 ===")
            print(f"总得分: {total_score:.2f}")
            
        return total_score, score_details
        
    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预处理输入数据，统一列名格式
        """
        # 创建列名映射
        column_mapping = {
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }
        
        # 复制数据以避免修改原始数据
        processed_data = data.copy()
        
        # 统一列名格式
        for old_col, new_col in column_mapping.items():
            if old_col in processed_data.columns:
                processed_data[new_col] = processed_data[old_col]
            elif new_col not in processed_data.columns:
                raise ValueError(f"数据缺少必要的列: {old_col} 或 {new_col}")
                
        return processed_data
        
    def _validate_data(self, data: pd.DataFrame):
        """验证输入数据的完整性"""
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {', '.join(missing_columns)}")
            
        # 检查数据是否包含足够的历史数据
        if len(data) < 20:  # 至少需要20个交易日的数据
            raise ValueError("数据量不足，至少需要20个交易日的数据")
            
    def _calculate_price_action_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算价格行为相关的得分"""
        scores = {}
        details = {}
        
        # 计算K线形态得分
        if 'candlestick' in self.signals['price_action']:
            if self.verbose:
                print("\n计算K线形态得分...")
            candlestick_score, candlestick_details = self.signals['price_action']['candlestick'].analyze(data)
            scores['candlestick'] = candlestick_score
            details['candlestick'] = candlestick_details
            if self.verbose:
                print(f"K线形态得分: {candlestick_score:.2f}")
            
        # 计算图表形态得分
        if 'chart_pattern' in self.signals['price_action']:
            if self.verbose:
                print("\n计算图表形态得分...")
            chart_pattern_score, chart_pattern_details = self.signals['price_action']['chart_pattern'].analyze(data)
            scores['chart_pattern'] = chart_pattern_score
            details['chart_pattern'] = chart_pattern_details
            if self.verbose:
                print(f"图表形态得分: {chart_pattern_score:.2f}")
                if chart_pattern_details.get('patterns_found'):
                    print("发现的形态:")
                    for pattern, info in chart_pattern_details['patterns_found'].items():
                        print(f"  - {pattern}: 可信度 {info['confidence']:.2f}")
        
        # 计算加权总分
        total_score = sum(
            scores.get(component, 0) * weight
            for component, weight in self.config['price_action']['components'].items()
        )
        
        if self.verbose:
            print("\n价格行为各组件权重:")
            for component, weight in self.config['price_action']['components'].items():
                score = scores.get(component, 0)
                print(f"  - {component}: 得分={score:.2f}, 权重={weight}, 加权得分={score*weight:.2f}")
        
        return total_score, details
        
    def _calculate_quantitative_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算量化指标相关的得分"""
        scores = {}
        details = {}
        
        # 计算趋势指标得分
        if 'trend' in self.signals['quantitative']:
            if self.verbose:
                print("\n计算趋势指标得分...")
            trend_score, trend_details = self.signals['quantitative']['trend'].analyze(data)
            scores['trend'] = trend_score
            details['trend'] = trend_details
            if self.verbose:
                print(f"趋势指标得分: {trend_score:.2f}")
                if trend_details:
                    print("趋势指标详情:")
                    for indicator, value in trend_details.items():
                        if isinstance(value, (int, float)):
                            print(f"  - {indicator}: {value:.2f}")
        
        # 计算加权总分
        total_score = sum(
            scores.get(component, 0) * weight
            for component, weight in self.config['quantitative']['components'].items()
        )
        
        if self.verbose:
            print("\n量化指标各组件权重:")
            for component, weight in self.config['quantitative']['components'].items():
                score = scores.get(component, 0)
                print(f"  - {component}: 得分={score:.2f}, 权重={weight}, 加权得分={score*weight:.2f}")
        
        return total_score, details 