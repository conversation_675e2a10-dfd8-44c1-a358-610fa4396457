"""
趋势指标打分模块
"""
from typing import Dict, Optional, Tuple
import pandas as pd
import numpy as np
from ..base.signal_base import SignalBase

class TrendIndicators(SignalBase):
    """趋势指标打分"""
    
    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        初始化趋势指标打分
        
        Args:
            weights: 各个子信号的权重配置
        """
        self.default_weights = {
            'rsi': 0.3,           # RSI指标
            'macd': 0.3,          # MACD指标
            'kdj': 0.2,           # KDJ指标
            'bollinger': 0.2      # 布林带
        }
        
        self.weights = weights if weights is not None else self.default_weights
        
    def analyze(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """
        分析趋势指标并返回得分
        
        Args:
            data: 股票数据
            
        Returns:
            Tuple[float, Dict]: (总得分, 详细信息)
        """
        return self.calculate(data)
        
    def calculate(self, data: pd.DataFrame) -> <PERSON><PERSON>[float, Dict]:
        """
        计算趋势指标打分得分
        
        Args:
            data: 股票数据
            
        Returns:
            Tuple[float, Dict]: 趋势指标打分得分和详细信息
        """
        details = {}
        total_score = 0
        
        # 计算RSI得分
        rsi_score, rsi_details = self._calculate_rsi_score(data)
        total_score += rsi_score * self.weights['rsi']
        details['rsi'] = rsi_details
        
        # 计算MACD得分
        macd_score, macd_details = self._calculate_macd_score(data)
        total_score += macd_score * self.weights['macd']
        details['macd'] = macd_details
        
        # 计算KDJ得分
        kdj_score, kdj_details = self._calculate_kdj_score(data)
        total_score += kdj_score * self.weights['kdj']
        details['kdj'] = kdj_details
        
        # 计算布林带得分
        bollinger_score, bollinger_details = self._calculate_bollinger_score(data)
        total_score += bollinger_score * self.weights['bollinger']
        details['bollinger'] = bollinger_details
        
        return self.normalize_score(total_score), details
        
    def _calculate_rsi_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算RSI得分"""
        # 计算价格变化
        delta = data['Close'].diff()
        
        # 分别计算上涨和下跌
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        
        # 计算RS和RSI
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 获取最新的RSI值
        current_rsi = rsi.iloc[-1]
        
        # 计算得分
        if current_rsi < 30:
            score = 100  # 超卖
        elif current_rsi > 70:
            score = 0    # 超买
        else:
            score = 50   # 中性
            
        details = {
            'current_rsi': current_rsi,
            'score': score
        }
        
        return score, details
        
    def _calculate_macd_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算MACD得分"""
        # 计算MACD
        exp1 = data['Close'].ewm(span=12, adjust=False).mean()
        exp2 = data['Close'].ewm(span=26, adjust=False).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9, adjust=False).mean()
        hist = macd - signal
        
        # 获取最新的MACD值
        current_macd = macd.iloc[-1]
        current_signal = signal.iloc[-1]
        current_hist = hist.iloc[-1]
        
        # 计算MACD趋势
        macd_trend = (current_macd - macd.iloc[-2]) / abs(macd.iloc[-2]) if macd.iloc[-2] != 0 else 0
        
        # 计算得分
        if current_macd > current_signal and macd_trend > 0:
            score = 100  # 强势上涨
        elif current_macd < current_signal and macd_trend < 0:
            score = 0    # 强势下跌
        else:
            score = 50   # 中性
            
        details = {
            'current_macd': current_macd,
            'current_signal': current_signal,
            'current_hist': current_hist,
            'macd_trend': macd_trend,
            'score': score
        }
        
        return score, details
        
    def _calculate_kdj_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算KDJ得分"""
        # 计算RSV
        low_min = data['Low'].rolling(window=9).min()
        high_max = data['High'].rolling(window=9).max()
        rsv = (data['Close'] - low_min) / (high_max - low_min) * 100
        
        # 计算KDJ
        k = rsv.rolling(window=3).mean()
        d = k.rolling(window=3).mean()
        j = 3 * k - 2 * d
        
        # 获取最新的KDJ值
        current_k = k.iloc[-1]
        current_d = d.iloc[-1]
        current_j = j.iloc[-1]
        
        # 计算得分
        if current_k > current_d and current_j > current_k:
            score = 100  # 强势上涨
        elif current_k < current_d and current_j < current_k:
            score = 0    # 强势下跌
        else:
            score = 50   # 中性
            
        details = {
            'current_k': current_k,
            'current_d': current_d,
            'current_j': current_j,
            'score': score
        }
        
        return score, details
        
    def _calculate_bollinger_score(self, data: pd.DataFrame) -> Tuple[float, Dict]:
        """计算布林带得分"""
        # 计算布林带
        ma20 = data['Close'].rolling(window=20).mean()
        std20 = data['Close'].rolling(window=20).std()
        upper = ma20 + 2 * std20
        lower = ma20 - 2 * std20
        
        # 获取最新的价格和布林带值
        current_price = data['Close'].iloc[-1]
        current_upper = upper.iloc[-1]
        current_lower = lower.iloc[-1]
        
        # 计算价格在布林带中的位置
        band_width = current_upper - current_lower
        price_position = (current_price - current_lower) / band_width if band_width != 0 else 0.5
        
        # 计算得分
        if price_position > 0.8:
            score = 0    # 接近上轨，可能超买
        elif price_position < 0.2:
            score = 100  # 接近下轨，可能超卖
        else:
            score = 50   # 中性
            
        details = {
            'current_price': current_price,
            'current_upper': current_upper,
            'current_lower': current_lower,
            'price_position': price_position,
            'score': score
        }
        
        return score, details 