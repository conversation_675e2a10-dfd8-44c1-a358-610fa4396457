# src/backtest/low_vol_breakout_backtest.py
import pandas as pd
import numpy as np
import talib # Assuming TA-Lib is installed
import sys
import os

# Add the project root directory to the Python path BEFORE attempting src imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Assuming base class or necessary imports exist
# from .base_backtester import BaseBacktester # Example if using a base class
from src.utils.logger import Logger
from src.data_providers.stock_data_provider import StockDataProviderFactory # Import the factory instead of provider

class LowVolBreakoutBacktester:
    """
    Backtester for the 'Low Volume Breakout Start Model'.

    Factors:
    1. Low Volatility: 30-day std dev < 25th percentile of 60-day rolling std dev.
    2. Volume Surge: Volume > 2 * vol_ma_20 AND vol_ma_5 > vol_ma_20.
    3. MACD Status: macd_diff > 0 AND macd_diff > macd_diff[1].
    4. Price Breakout: Close > boll_upper OR Close > MA(Close, 60).
    5. Momentum (Optional): RSI(14) > 60.
    """
    def __init__(self, data_source='tushare', logger=None, index_code='000300.SH'): # Added index_code
        self.data_provider = StockDataProviderFactory.get_provider(data_source)
        self.logger = logger if logger else Logger.get_logger()
        self.df = None
        self.index_df = None # Added to store index data
        self.index_code = index_code # Store index code
        self.trades = []
        self.initial_capital = 100000
        self.position_sizing_ratio = 0.1 # Default, can be overridden

        # Default parameters for indicators (can be made configurable)
        self.params = {
            'volatility_period': 30,
            'volatility_history': 60,
            'volatility_quantile': 0.25,
            'volume_threshold_multiplier': 2,
            'volume_short_ma': 5,
            'volume_long_ma': 20,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bbands_period': 20,
            'bbands_stddev': 2,
            'ma_long_period': 60,
            'rsi_period': 14,
            'rsi_threshold': 60,
            'hold_days': 5, # Default hold period
            # New parameters for risk management and exit logic
            'stop_loss_pct': 0.05, # 5% stop loss, set to 0 or None to disable
            'take_profit_pct': 0.15, # 15% take profit, set to 0 or None to disable
            'atr_period': 14,      # ATR period for potential dynamic stop loss
            'use_atr_stop': False,  # Flag to use ATR based stop loss (requires implementation in run_backtest)
            'atr_multiplier': 2.0   # Multiplier for ATR stop loss (e.g., entry_price - atr_multiplier * atr)
        }

    def set_parameters(self, params):
        """Update parameters for optimization."""
        self.params.update(params)

    def get_history_data(self, stock_code, period='1y', interval='1d'):
        """Fetches historical data using the data provider."""
        try:
            # 根据 period 计算 start_date 和 end_date
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
            if period.endswith('y'):
                years = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=years)).strftime('%Y-%m-%d')
            elif period.endswith('m'):
                months = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(months=months)).strftime('%Y-%m-%d')
            elif period.endswith('d'):
                days = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(days=days)).strftime('%Y-%m-%d')
            else:
                # 默认一年
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=1)).strftime('%Y-%m-%d')

            self.df = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)
            if self.df is None or self.df.empty:
                self.logger.warning(f"No data fetched for {stock_code}")
                return False
            # Ensure columns are lowercase for consistency
            self.df.columns = [col.lower() for col in self.df.columns]
            # Ensure index is datetime
            if not isinstance(self.df.index, pd.DatetimeIndex):
                 self.df.index = pd.to_datetime(self.df.index)
            self.df.sort_index(inplace=True)

            # Fetch index data using the same period and interval
            try:
                self.index_df = self.data_provider.get_stock_data_by_date(self.index_code, start_date, end_date)
                if self.index_df is None or self.index_df.empty:
                     self.logger.warning(f"No index data fetched for {self.index_code}. Trend filter disabled.")
                     self.index_df = None # Ensure it's None if fetch fails
                else:
                     self.index_df.columns = [col.lower() for col in self.index_df.columns]
                     if not isinstance(self.index_df.index, pd.DatetimeIndex):
                         self.index_df.index = pd.to_datetime(self.index_df.index)
                     self.index_df.sort_index(inplace=True)
                     self.logger.info(f"Index data fetched successfully for {self.index_code}")
            except Exception as idx_e:
                 self.logger.error(f"Error fetching index data for {self.index_code}: {idx_e}")
                 self.index_df = None # Disable filter on error

            return True
        except Exception as e:
            self.logger.error(f"Error fetching stock data for {stock_code}: {e}")
            return False

    def calculate_indicators(self):
        """Calculates all necessary technical indicators."""
        if self.df is None or self.df.empty:
            self.logger.error("DataFrame is empty. Cannot calculate indicators.")
            return False

        try:
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in self.df.columns for col in required_cols):
                missing = [col for col in required_cols if col not in self.df.columns]
                self.logger.error(f"Missing required columns: {missing}")
                return False

            # 1. 前期波动率 (Volatility)
            std_dev = self.df['close'].rolling(window=self.params['volatility_period']).std()
            rolling_quantile = std_dev.rolling(window=self.params['volatility_history']).quantile(self.params['volatility_quantile'])
            self.df['low_volatility_cond'] = std_dev < rolling_quantile

            # 2. 成交量放大 (Volume Surge)
            self.df['vol_ma_short'] = talib.MA(self.df['volume'], timeperiod=self.params['volume_short_ma'])
            self.df['vol_ma_long'] = talib.MA(self.df['volume'], timeperiod=self.params['volume_long_ma'])
            vol_cond1 = self.df['volume'] > self.params['volume_threshold_multiplier'] * self.df['vol_ma_long']
            vol_cond2 = self.df['vol_ma_short'] > self.df['vol_ma_long']
            self.df['volume_surge_cond'] = vol_cond1 & vol_cond2

            # 3. MACD状态 (MACD Status)
            macd, macdsignal, macdhist = talib.MACD(self.df['close'],
                                                    fastperiod=self.params['macd_fast'],
                                                    slowperiod=self.params['macd_slow'],
                                                    signalperiod=self.params['macd_signal'])
            self.df['macd_diff'] = macd - macdsignal # Often referred to as MACD Histogram, but here means DIFF-DEA
            macd_cond1 = self.df['macd_diff'] > 0
            macd_cond2 = self.df['macd_diff'] > self.df['macd_diff'].shift(1) # Expanding difference
            self.df['macd_status_cond'] = macd_cond1 & macd_cond2

            # 4. 价格突破 (Price Breakout)
            upper, middle, lower = talib.BBANDS(self.df['close'],
                                                timeperiod=self.params['bbands_period'],
                                                nbdevup=self.params['bbands_stddev'],
                                                nbdevdn=self.params['bbands_stddev'],
                                                matype=0) # SMA
            self.df['ma_long'] = talib.MA(self.df['close'], timeperiod=self.params['ma_long_period'])
            price_break_boll = self.df['close'] > upper
            price_break_ma = self.df['close'] > self.df['ma_long']
            self.df['price_breakout_cond'] = price_break_boll | price_break_ma # OR condition

            # 5. 动能确认 (Momentum - RSI) (Optional, calculated but applied in signal generation)
            self.df['rsi'] = talib.RSI(self.df['close'], timeperiod=self.params['rsi_period'])
            self.df['rsi_cond'] = self.df['rsi'] > self.params['rsi_threshold']

            # 6. ATR (Average True Range) for potential stop-loss
            if self.params.get('atr_period', 0) > 0 and all(c in self.df.columns for c in ['high', 'low', 'close']):
                 self.df['atr'] = talib.ATR(self.df['high'], self.df['low'], self.df['close'], timeperiod=self.params['atr_period'])
            else:
                 self.df['atr'] = np.nan # Set to NaN if not calculated

            # 7. Calculate Index Trend (e.g., above MA60)
            if self.index_df is not None and 'close' in self.index_df.columns:
                 try:
                     index_ma_period = self.params.get('ma_long_period', 60) # Use same long MA period for index trend
                     self.index_df['index_ma'] = talib.MA(self.index_df['close'], timeperiod=index_ma_period)
                     self.index_df['index_trend_up'] = self.index_df['close'] > self.index_df['index_ma']
                     # Align index trend data with stock data by date
                     self.df = self.df.join(self.index_df[['index_trend_up']], how='left')
                     self.df['index_trend_up'].fillna(False, inplace=True) # Assume no trend if index data is missing for a date
                     self.logger.info(f"Index trend (MA{index_ma_period}) calculated and joined.")
                 except Exception as idx_calc_e:
                     self.logger.error(f"Error calculating index trend: {idx_calc_e}. Disabling trend filter.")
                     self.df['index_trend_up'] = True # Default to True (no filter) if calculation fails
            else:
                 self.logger.warning("Index data not available. Index trend filter disabled.")
                 self.df['index_trend_up'] = True # Default to True (no filter) if no index data

            # Define required indicators for dropna
            required_indicators = [
                'low_volatility_cond', 'volume_surge_cond', 'macd_status_cond',
                'price_breakout_cond', 'rsi_cond', 'index_trend_up'
            ]

            # Drop NA values created by rolling calculations AFTER joining index trend
            self.df.dropna(subset=[col for col in required_indicators if col != 'atr'], inplace=True) # Drop based on core indicators first
            # Optionally drop rows where ATR is NaN if ATR stop is used, or handle NaN in run_backtest
            # self.df.dropna(inplace=True) # Original drop all NaN

            self.logger.info("Indicators calculated successfully.")
            return True

        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def generate_signals(self, use_rsi_confirm=True):
        """
        Generates potential buy signals (1) based on the calculated indicators.
        Exit logic (sell) is handled dynamically in run_backtest.
        """
        required_indicators = [
            'low_volatility_cond', 'volume_surge_cond', 'macd_status_cond',
            'price_breakout_cond', 'rsi_cond', 'index_trend_up' # Added index_trend_up
        ]
        # Add 'atr' only if it's expected to be used for stop loss calculation later
        # ... (rest of ATR check remains the same)
        if self.params.get('use_atr_stop', False) and self.params.get('atr_period', 0) > 0:
             # Ensure 'atr' column exists if ATR stop is enabled
             if 'atr' not in self.df.columns:
                 self.logger.error("ATR column missing but use_atr_stop is True. Cannot generate signals.")
                 return False
             # required_indicators.append('atr') # Not strictly needed for buy signal itself

        # Check for required columns including the index trend
        if self.df is None or not all(col in self.df.columns for col in required_indicators):
            missing = [col for col in required_indicators if col not in self.df.columns]
            # Handle case where index trend might be missing but defaulted to True
            if 'index_trend_up' in missing and 'index_trend_up' in self.df.columns and self.df['index_trend_up'].all():
                 pass # Allow proceeding if index filter was disabled and defaulted to True
            else:
                 self.logger.error(f"Missing required indicator columns: {missing}. Cannot generate signals.")
                 return False

        # Combine mandatory conditions for potential entry
        buy_conditions = (
            self.df['low_volatility_cond'] &
            self.df['volume_surge_cond'] &
            self.df['macd_status_cond'] &
            self.df['price_breakout_cond'] &
            self.df['index_trend_up'] # Added index trend condition
        )

        # Add optional RSI condition
        if use_rsi_confirm:
            buy_conditions &= self.df['rsi_cond']

        self.df['signal'] = 0 # 0: No action, 1: Potential Buy Entry
        # Generate potential buy signals where all conditions are met
        # We only mark potential entry points here. Actual entry depends on cash and position status in run_backtest.
        self.df.loc[buy_conditions, 'signal'] = 1

        # --- Sell Signal Logic Removed ---
        # Sell signals (-1) are no longer generated here.
        # Exit decisions (stop-loss, take-profit, hold_days) are made dynamically
        # during the backtest simulation in run_backtest.

        self.logger.info("Potential buy signals generated.")
        return True


    # Removed hold_days from signature, it's accessed via self.params now
    def run_backtest(self, initial_capital=100000, position_sizing_ratio=0.1):
        """
        Runs the backtest simulation based on generated signals.
        """
        if self.df is None or 'signal' not in self.df.columns:
            self.logger.error("DataFrame or signals missing. Cannot run backtest.")
            return None # Return None to indicate failure

        self.initial_capital = initial_capital
        self.position_sizing_ratio = position_sizing_ratio
        # hold_days is now accessed via self.params, no need for this check/override here
        # Ensure generate_signals was called before run_backtest if params changed externally.
        cash = self.initial_capital
        position = 0 # Shares held
        portfolio_value = cash
        self.trades = []
        entry_price = 0
        entry_date = None # Track entry date for hold days calculation
 
        # Add columns to track portfolio value
        self.df['position'] = 0.0
        self.df['cash'] = float(initial_capital)
        self.df['portfolio_value'] = float(initial_capital)


        for i in range(len(self.df)):
            current_date = self.df.index[i]
            current_price = self.df['close'].iloc[i] # Use close price for transactions
            signal = self.df['signal'].iloc[i]

            # Update portfolio value based on current price if holding position
            if position > 0:
                portfolio_value = cash + position * current_price
            else:
                portfolio_value = cash # If no position, portfolio value is just cash

            # --- Buy Signal ---
            if signal == 1 and cash > 0:
                if position > 0: # If already holding, ignore new buy signal (or implement pyramiding)
                    pass # Or log this event
                else:
                    investment_amount = portfolio_value * self.position_sizing_ratio # Use portfolio value for sizing
                    shares_to_buy = int(investment_amount / current_price)
                    cost = shares_to_buy * current_price

                    if shares_to_buy > 0 and cash >= cost:
                        position += shares_to_buy
                        cash -= cost
                        entry_price = current_price
                        entry_date = current_date # Record entry date
                        entry_atr = self.df['atr'].iloc[i] if 'atr' in self.df.columns and not pd.isna(self.df['atr'].iloc[i]) else None # Get ATR at entry
                        self.trades.append({
                            'entry_date': entry_date,
                            'entry_price': entry_price,
                            'shares': shares_to_buy,
                            'entry_atr': entry_atr, # Store entry ATR
                            'exit_date': None,
                            'exit_price': None,
                            'profit': None,
                            'return_pct': None,
                            'exit_reason': None # Add field to record exit reason
                        })
                        atr_display = f"{entry_atr:.4f}" if entry_atr is not None else "N/A"
                        self.logger.debug(f"{current_date}: BUY {shares_to_buy} shares at {current_price:.2f} (ATR: {atr_display}), Cash: {cash:.2f}")
                    # else: # Log insufficient cash or shares=0?
 
            # --- Exit Logic (Replaces the old signal == -1 logic) ---
            elif position > 0: # Only check exit conditions if holding a position
                exit_reason = None
                # 1. Check Hold Days
                days_held = (current_date - entry_date).days
                if days_held >= self.params['hold_days']:
                    exit_reason = f"Hold Days ({self.params['hold_days']})"
 
                # 2. Check Stop Loss (Percentage) - Use current day's low for check? Using close for simplicity here.
                if exit_reason is None and self.params.get('stop_loss_pct', 0) > 0:
                    stop_loss_price = entry_price * (1 - self.params['stop_loss_pct'])
                    # Check against low price for more realistic stop loss triggering
                    current_low = self.df['low'].iloc[i]
                    if current_low <= stop_loss_price:
                        exit_reason = f"Stop Loss ({self.params['stop_loss_pct']*100:.1f}%)"
                        current_price = stop_loss_price # Exit at stop loss price for calculation
 
                # 3. Check Take Profit (Percentage) - Use current day's high for check? Using close for simplicity here.
                if exit_reason is None and self.params.get('take_profit_pct', 0) > 0:
                    take_profit_price = entry_price * (1 + self.params['take_profit_pct'])
                    # Check against high price for more realistic take profit triggering
                    current_high = self.df['high'].iloc[i]
                    if current_high >= take_profit_price:
                        exit_reason = f"Take Profit ({self.params['take_profit_pct']*100:.1f}%)"
                        current_price = take_profit_price # Exit at take profit price for calculation
 
                # 4. Check ATR Stop Loss (Optional - Requires ATR calculation and entry ATR tracking)
                # 4. Check ATR Stop Loss (Optional - Requires ATR calculation and entry ATR tracking)
                if exit_reason is None and self.params.get('use_atr_stop', False):
                    # Find the entry ATR for the current position
                    current_trade = next((trade for trade in reversed(self.trades) if trade['exit_date'] is None), None)
                    if current_trade and current_trade.get('entry_atr') is not None:
                        entry_atr_value = current_trade['entry_atr']
                        atr_stop_price = entry_price - self.params['atr_multiplier'] * entry_atr_value
                        current_low = self.df['low'].iloc[i]
                        if current_low <= atr_stop_price:
                            exit_reason = f"ATR Stop ({self.params['atr_multiplier']}x, Entry ATR: {entry_atr_value:.4f})"
                            current_price = atr_stop_price # Exit at ATR stop price
                    # else: # Log missing entry ATR?
                        # self.logger.warning(f"{current_date}: ATR stop enabled but entry ATR not found for current position.")
 
                # --- Execute Sell if Exit Condition Met ---
                if exit_reason is not None:
                    proceeds = position * current_price # Sell at current_price (which might be adjusted by SL/TP)
                    cash += proceeds
                    self.logger.debug(f"{current_date}: SELL {position} shares at {current_price:.2f} due to {exit_reason}. Cash: {cash:.2f}")
 
                    # Update the last open trade record
                    open_trade_index = -1
                    for j in range(len(self.trades) - 1, -1, -1):
                        if self.trades[j]['exit_date'] is None:
                            open_trade_index = j
                            break
 
                    if open_trade_index != -1:
                        last_trade = self.trades[open_trade_index]
                        last_trade['exit_date'] = current_date
                        last_trade['exit_price'] = current_price
                        last_trade['profit'] = (current_price - last_trade['entry_price']) * last_trade['shares']
                        last_trade['return_pct'] = ((current_price / last_trade['entry_price']) - 1) * 100 if last_trade['entry_price'] != 0 else 0
                        last_trade['exit_reason'] = exit_reason # Record exit reason
                    else:
                        # This case should ideally not happen if position > 0
                        self.logger.warning(f"{current_date}: Exit condition '{exit_reason}' met, but no corresponding open trade found in list.")
 
                    position = 0
                    entry_price = 0
                    entry_date = None # Reset entry date
 
            # Update daily portfolio tracking (needs to happen regardless of buy/sell action)
            self.df.iloc[i, self.df.columns.get_loc('position')] = position
            self.df.iloc[i, self.df.columns.get_loc('cash')] = cash
            # Recalculate portfolio value at the end of the day
            self.df.iloc[i, self.df.columns.get_loc('portfolio_value')] = cash + position * current_price


        # If position still held at the end, close it using the last price
        if position > 0:
            last_date = self.df.index[-1]
            last_price = self.df['close'].iloc[-1]
            cash += position * last_price
            self.logger.debug(f"{last_date}: Close final position ({position} shares) at {last_price:.2f}, Cash: {cash:.2f}")

            # Find the last open trade and close it
            open_trade_index = -1
            for j in range(len(self.trades) - 1, -1, -1):
                 if self.trades[j]['exit_date'] is None:
                     open_trade_index = j
                     break

            if open_trade_index != -1:
                 last_trade = self.trades[open_trade_index]
                 last_trade['exit_date'] = last_date
                 last_trade['exit_price'] = last_price
                 last_trade['profit'] = (last_price - last_trade['entry_price']) * last_trade['shares']
                 last_trade['return_pct'] = ((last_price / last_trade['entry_price']) - 1) * 100 if last_trade['entry_price'] != 0 else 0
                 last_trade['exit_reason'] = 'End of Backtest' # Mark exit reason
            else:
                 # This case should ideally not happen if position > 0 unless trades list is empty/corrupt
                 self.logger.warning(f"{last_date}: Position held at end, but no corresponding open trade found in list.")

            position = 0 # Reset position after closing

        self.logger.info("Backtest simulation finished.")
        # Filter out any trades that might not have been closed properly (shouldn't happen with end-of-data closing)
        closed_trades = [t for t in self.trades if t['exit_date'] is not None]
        return closed_trades # Return only completed trades

    def calculate_performance(self, initial_capital=None):
        """
        Calculates performance metrics based on the backtest results.
        """
        if initial_capital is None:
            initial_capital = self.initial_capital

        # Use the completed trades list returned by run_backtest
        completed_trades = self.trades # Assuming self.trades now only contains completed trades

        if not completed_trades:
            self.logger.warning("No completed trades executed. Cannot calculate performance.")
            # Return default metrics indicating no activity
            return {
                'total_trades': 0,
                'total_return_pct': 0.0,
                'win_rate_pct': 0.0,
                'profit_factor': 0.0,
                'max_drawdown_pct': 0.0,
                'sharpe_ratio': 0.0, # Requires risk-free rate, simplified here
                'average_trade_return_pct': 0.0,
                'average_win_return_pct': 0.0,
                'average_loss_return_pct': 0.0,
                'final_portfolio_value': initial_capital # No change if no trades
            }


        trades_df = pd.DataFrame(completed_trades)
        if 'profit' not in trades_df.columns or trades_df['profit'].isnull().all():
             self.logger.warning("Profit column missing or all NaN in trades. Cannot calculate performance.")
             # Return defaults or indicate error
             return {
                'total_trades': len(trades_df),
                'error': 'Profit calculation failed for trades',
                'total_return_pct': 0.0, # Or calculate based on portfolio value if available
                'win_rate_pct': 0.0,
                'profit_factor': 0.0,
                'max_drawdown_pct': 0.0, # Calculate drawdown separately
                'sharpe_ratio': 0.0,
                'average_trade_return_pct': 0.0,
                'average_win_return_pct': 0.0,
                'average_loss_return_pct': 0.0,
                'final_portfolio_value': self.df['portfolio_value'].iloc[-1] if self.df is not None and not self.df.empty else initial_capital
            }


        # --- Basic Metrics ---
        total_trades = len(trades_df)
        final_portfolio_value = self.df['portfolio_value'].iloc[-1] if self.df is not None and not self.df.empty else initial_capital
        total_return = final_portfolio_value - initial_capital
        total_return_pct = (total_return / initial_capital) * 100 if initial_capital != 0 else 0.0

        # --- Trade-based Metrics ---
        winning_trades = trades_df[trades_df['profit'] > 0]
        losing_trades = trades_df[trades_df['profit'] <= 0] # Include zero profit as non-winning

        num_winning_trades = len(winning_trades)
        num_losing_trades = len(losing_trades)

        win_rate_pct = (num_winning_trades / total_trades) * 100 if total_trades > 0 else 0

        gross_profit = winning_trades['profit'].sum()
        gross_loss = abs(losing_trades['profit'].sum()) # Absolute value of losses

        profit_factor = gross_profit / gross_loss if gross_loss > 0 else np.inf # Handle division by zero

        average_trade_return_pct = trades_df['return_pct'].mean() if total_trades > 0 else 0
        average_win_return_pct = winning_trades['return_pct'].mean() if num_winning_trades > 0 else 0
        average_loss_return_pct = losing_trades['return_pct'].mean() if num_losing_trades > 0 else 0


        # --- Risk Metrics ---
        max_drawdown_pct = 0.0
        sharpe_ratio = 0.0
        if self.df is not None and not self.df.empty and 'portfolio_value' in self.df.columns:
            # Max Drawdown
            portfolio_values = self.df['portfolio_value']
            rolling_max = portfolio_values.cummax()
            drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan) # Avoid division by zero if max is 0
            max_drawdown_pct = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0

            # Sharpe Ratio (Simplified: using daily returns, assuming 0 risk-free rate)
            daily_returns = portfolio_values.pct_change().dropna()
            if len(daily_returns) > 1:
                 avg_daily_return = daily_returns.mean()
                 std_daily_return = daily_returns.std()
                 # Annualized Sharpe Ratio (assuming 252 trading days)
                 sharpe_ratio = (avg_daily_return / std_daily_return) * np.sqrt(252) if std_daily_return != 0 and not np.isnan(std_daily_return) else 0.0


        performance = {
            'total_trades': total_trades,
            'total_return_pct': Logger.format_number(total_return_pct),
            'win_rate_pct': Logger.format_number(win_rate_pct),
            'profit_factor': Logger.format_number(profit_factor),
            'max_drawdown_pct': Logger.format_number(max_drawdown_pct),
            'sharpe_ratio': Logger.format_number(sharpe_ratio),
            'average_trade_return_pct': Logger.format_number(average_trade_return_pct),
            'average_win_return_pct': Logger.format_number(average_win_return_pct),
            'average_loss_return_pct': Logger.format_number(average_loss_return_pct),
            'gross_profit': Logger.format_number(gross_profit),
            'gross_loss': Logger.format_number(gross_loss),
            'final_portfolio_value': Logger.format_number(final_portfolio_value)
        }

        self.logger.info(f"Performance calculated: {performance}")
        return performance

    def run_single_stock_backtest(self, stock_code, period='1y', interval='1d',
                                  initial_capital=100000, position_sizing_ratio=0.1,
                                  hold_days=5, use_rsi_confirm=True, params=None):
        """
        Convenience method to run the full backtest process for a single stock.
        """
        self.logger.info(f"开始回测股票 {stock_code}")
        if params:
            self.set_parameters(params)
        else:
            self.params['hold_days'] = hold_days

        # 1. 获取数据
        if not self.get_history_data(stock_code, period, interval):
            return {'stock_code': stock_code, 'error': '数据获取失败'}

        # 2. 计算指标
        if not self.calculate_indicators():
             return {'stock_code': stock_code, 'error': '指标计算失败'}

        # 3. 生成信号
        if not self.generate_signals(use_rsi_confirm=use_rsi_confirm):
             return {'stock_code': stock_code, 'error': '信号生成失败'}

        # 4. 运行回测
        self.trades = self.run_backtest(initial_capital=initial_capital,
                                        position_sizing_ratio=position_sizing_ratio)

        if self.trades is None:
             max_drawdown = 0.0
             if self.df is not None and 'portfolio_value' in self.df.columns:
                 portfolio_values = self.df['portfolio_value']
                 rolling_max = portfolio_values.cummax()
                 drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan)
                 max_drawdown = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0

             return {
                 'stock_code': stock_code,
                 'error': '回测执行失败',
                 'total_trades': 0,
                 'total_return_pct': (self.df['portfolio_value'].iloc[-1] / initial_capital - 1) * 100 if self.df is not None and not self.df.empty else 0.0,
                 'max_drawdown_pct': max_drawdown,
                 'win_rate_pct': 0.0,
                 'profit_factor': 0.0,
                 'sharpe_ratio': 0.0
             }

        # 5. 计算性能指标
        performance = self.calculate_performance(initial_capital=initial_capital)

        if performance is None:
             max_drawdown = 0.0
             if self.df is not None and 'portfolio_value' in self.df.columns:
                 portfolio_values = self.df['portfolio_value']
                 rolling_max = portfolio_values.cummax()
                 drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan)
                 max_drawdown = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0

             return {
                 'stock_code': stock_code,
                 'error': '性能计算失败',
                 'total_trades': len(self.trades) if self.trades else 0,
                 'total_return_pct': (self.df['portfolio_value'].iloc[-1] / initial_capital - 1) * 100 if self.df is not None and not self.df.empty else 0.0,
                 'max_drawdown_pct': max_drawdown,
                 'win_rate_pct': 0.0,
                 'profit_factor': 0.0,
                 'sharpe_ratio': 0.0
             }

        self.logger.info(f"完成回测 {stock_code}")
        
        # 合并结果
        result = {'stock_code': stock_code, **performance}
        return result

# Example Usage (for testing within this file)
if __name__ == '__main__':
    # 配置日志
    try:
        Logger.setup_logging(log_level='INFO')
    except AttributeError:
        import logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

    test_logger = Logger.get_logger()

    # 实例化回测器
    backtester = LowVolBreakoutBacktester(logger=test_logger, data_source='tushare')

    # 定义回测参数
    stock_to_test = '600505.SH'
    run_config = {
        'period': '3y',
        'interval': '1d',
        'initial_capital': 100000,
        'position_sizing_ratio': 0.2,
        'use_rsi_confirm': True,
        'params': {
             'volatility_period': 25,
             'volume_long_ma': 25,
             'rsi_threshold': 65,
             'bbands_period': 25,
             'atr_period': 14,
             'hold_days': 5,
             'use_atr_stop': False,
             'atr_multiplier': 2.0,
             'stop_loss_pct': 0,
             'take_profit_pct': 0
        }
    }

    # 运行回测
    results = backtester.run_single_stock_backtest(
        stock_code=stock_to_test,
        period=run_config['period'],
        interval=run_config['interval'],
        initial_capital=run_config['initial_capital'],
        position_sizing_ratio=run_config['position_sizing_ratio'],
        use_rsi_confirm=run_config['use_rsi_confirm'],
        params=run_config['params']
    )

    # 打印结果
    print("\n=== 回测结果 ===")
    if 'error' in results:
        print(f"错误 - {stock_to_test}: {results['error']}")
        if backtester.df is not None:
             print("\n--- 数据预览（最后几行） ---")
             print(backtester.df.tail())
    else:
        # 使用pandas Series格式化输出
        result_series = pd.Series({
            '股票代码': results['stock_code'],
            '总交易次数': results['total_trades'],
            '总收益率': f"{results['total_return_pct']}%",
            '胜率': f"{results['win_rate_pct']}%",
            '盈亏比': results['profit_factor'],
            '最大回撤': f"{results['max_drawdown_pct']}%",
            '夏普比率': results['sharpe_ratio'],
            '平均交易收益率': f"{results['average_trade_return_pct']}%",
            '平均盈利': f"{results['average_win_return_pct']}%",
            '平均亏损': f"{results['average_loss_return_pct']}%",
            '总盈利': results['gross_profit'],
            '总亏损': results['gross_loss'],
            '最终资金': results['final_portfolio_value']
        })
        print(result_series)

        # 打印交易记录
        if backtester.trades:
            print("\n=== 交易记录 ===")
            trades_output_df = pd.DataFrame(backtester.trades)
            
            # 格式化日期和数值
            trades_output_df['entry_date'] = pd.to_datetime(trades_output_df['entry_date']).dt.strftime('%Y-%m-%d')
            trades_output_df['exit_date'] = pd.to_datetime(trades_output_df['exit_date']).dt.strftime('%Y-%m-%d')
            trades_output_df['entry_price'] = trades_output_df['entry_price'].map('{:.2f}'.format)
            trades_output_df['exit_price'] = trades_output_df['exit_price'].map('{:.2f}'.format)
            trades_output_df['profit'] = trades_output_df['profit'].map('{:.2f}'.format)
            trades_output_df['return_pct'] = trades_output_df['return_pct'].map('{:.2f}%'.format)
            trades_output_df['entry_atr'] = trades_output_df['entry_atr'].map(lambda x: '{:.4f}'.format(x) if pd.notnull(x) else 'N/A')
            
            # 设置列顺序和中文列名
            column_order = {
                'entry_date': '买入日期',
                'exit_date': '卖出日期',
                'entry_price': '买入价格',
                'exit_price': '卖出价格',
                'shares': '交易数量',
                'profit': '盈亏金额',
                'return_pct': '收益率',
                'exit_reason': '卖出原因',
                'entry_atr': '入场ATR'
            }
            
            trades_output_df = trades_output_df[list(column_order.keys())].rename(columns=column_order)
            
            # 设置pandas显示选项
            pd.set_option('display.max_rows', None)
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.unicode.ambiguous_as_wide', True)
            pd.set_option('display.unicode.east_asian_width', True)
            
            print(trades_output_df.to_string(index=False))
        else:
            print("\n无交易记录")

        # 打印信号和投资组合数据
        if backtester.df is not None:
            print("\n=== 信号和投资组合数据（最后90天） ===")
            display_columns = {
                'close': '收盘价',
                'volume': '成交量',
                'vol_ma_short': '成交量短均线',
                'vol_ma_long': '成交量长均线',
                'low_volatility_cond': '低波动条件',
                'volume_surge_cond': '放量条件',
                'macd_status_cond': 'MACD条件',
                'price_breakout_cond': '突破条件',
                'rsi': 'RSI',
                'rsi_cond': 'RSI条件',
                'signal': '信号',
                'position': '持仓',
                'cash': '现金',
                'portfolio_value': '组合价值'
            }
            
            df_display = backtester.df[list(display_columns.keys())].tail(90).copy()
            df_display.index = pd.to_datetime(df_display.index).strftime('%Y-%m-%d')
            df_display = df_display.rename(columns=display_columns)
            
            # 格式化数值
            for col in df_display.columns:
                if col in ['收盘价', '成交量', '成交量短均线', '成交量长均线', 'RSI', '现金', '组合价值']:
                    df_display[col] = df_display[col].map(lambda x: f"{x:.2f}")
                elif col in ['持仓']:
                    df_display[col] = df_display[col].map(lambda x: f"{int(x)}")
                elif col in ['低波动条件', '放量条件', 'MACD条件', '突破条件', 'RSI条件']:
                    df_display[col] = df_display[col].map(lambda x: '是' if x else '否')
                elif col == '信号':
                    df_display[col] = df_display[col].map(lambda x: '买入' if x == 1 else ('卖出' if x == -1 else '持有'))
            
            print(df_display)
