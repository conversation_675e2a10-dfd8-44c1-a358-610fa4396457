import pandas as pd
import numpy as np
from src.data_providers.stock_data_provider import StockDataProviderFactory
from src.utils.logger import Logger
from src.ui.indicators.ichimoku import calculate_ichimoku
from src.config.config_manager import config_manager

class IchimokuBacktester:
    """一目均衡表回测器"""
    def __init__(self, data_source='tushare'):
        self.data_source = data_source
        self.data_provider = StockDataProviderFactory.get_provider(data_source)
        self.df = None

        self.ichimoku_params = config_manager.get_config('ichimoku')
        if not self.ichimoku_params:
             Logger.warning("无法从配置管理器获取Ichimoku参数，使用默认值")
             # 提供默认值以防配置失败
             self.ichimoku_params = {'conversion': 9, 'base': 26, 'span_b': 52}
        Logger.info(f"使用的Ichimoku参数: {self.ichimoku_params}")


    def get_history_data(self, stock_code, start_date=None, end_date=None, period=None, interval='1d'):
        """
        获取指定股票的历史数据。优先使用 start_date 和 end_date。
        如果未提供 start_date 或 end_date，则使用 period。
        :param stock_code: 股票代码
        :param start_date: 开始日期 (YYYY-MM-DD)，可选
        :param end_date: 结束日期 (YYYY-MM-DD)，可选
        :param period: 周期 (如 '1y', '2y')，当 start_date/end_date 未提供时使用，可选
        :param interval: 间隔，默认1天
        :return: DataFrame 或 None
        """
        try:
            if start_date and end_date:
                Logger.info(f"获取股票历史数据: {stock_code}, 开始日期: {start_date}, 结束日期: {end_date}, 间隔: {interval}, 来源: {self.data_source}")
                data = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)
            elif period:
                # 根据 period 计算 start_date 和 end_date
                end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
                if period.endswith('y'):
                    years = int(period[:-1])
                    start_date = (pd.Timestamp.now() - pd.DateOffset(years=years)).strftime('%Y-%m-%d')
                elif period.endswith('m'):
                    months = int(period[:-1])
                    start_date = (pd.Timestamp.now() - pd.DateOffset(months=months)).strftime('%Y-%m-%d')
                elif period.endswith('d'):
                    days = int(period[:-1])
                    start_date = (pd.Timestamp.now() - pd.DateOffset(days=days)).strftime('%Y-%m-%d')
                else:
                    # 默认一年
                    start_date = (pd.Timestamp.now() - pd.DateOffset(years=1)).strftime('%Y-%m-%d')
                
                Logger.info(f"获取股票历史数据: {stock_code}, 开始日期: {start_date}, 结束日期: {end_date}, 来源: {self.data_source}")
                data = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)
            else:
                # 如果都没有提供，默认获取最近一年的数据
                end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=1)).strftime('%Y-%m-%d')
                Logger.warning(f"未提供日期范围或周期，将使用默认时间范围: {start_date} 到 {end_date}")
                Logger.info(f"获取股票历史数据: {stock_code}, 开始日期: {start_date}, 结束日期: {end_date}, 来源: {self.data_source}")
                data = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)

            if data is None or data.empty:
                Logger.error(f"未能获取到股票 {stock_code} 的历史数据（参数: start={start_date}, end={end_date}, period={period}, interval={interval}）")
                self.df = None
                return None

            # --- 标准化列名 (非常重要) ---
            # 确保有 'Open', 'High', 'Low', 'Close', 'Volume'
            # 你可能需要根据 data_provider 返回的具体列名进行映射
            column_mapping = {
                'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume',
                # 添加其他可能的列名映射，例如中文列名
                '开盘': 'Open', '最高': 'High', '最低': 'Low', '收盘': 'Close', '成交量': 'Volume'
            }
            data.rename(columns=lambda c: column_mapping.get(c.lower(), c), inplace=True)

            # 检查必要列是否存在
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                Logger.error(f"获取的数据缺少必要列: {missing_cols}")
                self.df = None
                return None

            # 确保索引是 DatetimeIndex (很多库会自动处理，但检查一下更安全)
            if not isinstance(data.index, pd.DatetimeIndex):
                # 尝试将索引转换为日期时间格式，如果失败则报错
                try:
                    data.index = pd.to_datetime(data.index)
                except Exception as e:
                     Logger.error(f"无法将索引转换为日期时间格式: {e}")
                     # 查找可能的日期列并设置为索引
                     date_col_candidates = ['date', '日期', '交易日期', 'time']
                     found_date_col = None
                     for col in date_col_candidates:
                         if col in data.columns:
                             try:
                                 data[col] = pd.to_datetime(data[col])
                                 data.set_index(col, inplace=True)
                                 found_date_col = col
                                 Logger.info(f"使用列 '{col}' 作为日期索引")
                                 break
                             except Exception as date_conv_e:
                                 Logger.warning(f"尝试将列 '{col}' 转为日期索引失败: {date_conv_e}")
                     if not found_date_col:
                         Logger.error("找不到合适的日期列或无法转换索引为日期时间格式。")
                         self.df = None
                         return None

            # 按日期升序排序
            data.sort_index(inplace=True)

            # 删除重复的索引
            if data.index.duplicated().any():
                Logger.warning(f"发现重复的日期索引，保留最后一个值")
                data = data[~data.index.duplicated(keep='last')]

            # 检查数据质量
            for col in required_cols:
                null_count = data[col].isnull().sum()
                if null_count > 0:
                    Logger.warning(f"列 {col} 包含 {null_count} 个空值")

            self.df = data
            Logger.info(f"成功获取并标准化 {len(data)} 行历史数据")
            return self.df # 返回 self.df
        except Exception as e:
            Logger.error(f"获取或处理历史数据失败: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            self.df = None
            return None

    def calculate_indicators(self):
        """为 self.df 计算一目均衡表指标"""
        if self.df is None or self.df.empty:
            Logger.error("没有历史数据可用于计算指标")
            return False

        # 再次检查必要列，因为 df 可能被外部修改
        required_cols = ['High', 'Low', 'Close']
        missing_cols = [col for col in required_cols if col not in self.df.columns]
        if missing_cols:
            Logger.error(f"数据缺少用于计算指标的列: {missing_cols}")
            return False

        try:
            Logger.info("开始计算一目均衡表指标...")
            # 调用现有的计算函数
            # 假设 calculate_ichimoku 返回一个包含指标的新DataFrame
            ichimoku_df = calculate_ichimoku(
                self.df, # 传递包含 High, Low, Close 的 DataFrame
                short_period=self.ichimoku_params['conversion'],
                mid_period=self.ichimoku_params['base'],
                long_period=self.ichimoku_params['span_b']
            )

            # 将计算出的指标合并回主DataFrame (使用 join 确保按索引对齐)
            self.df = self.df.join(ichimoku_df, how='left')

            # 检查合并后的结果
            if ichimoku_df.empty:
                 Logger.warning("calculate_ichimoku 返回了空的 DataFrame")
            elif 'conversion_line' not in self.df.columns: # 检查一个关键指标列是否成功加入
                 Logger.error("指标未能成功合并到主 DataFrame")
                 return False

            Logger.info("一目均衡表指标计算完成并已合并到主数据")
            Logger.debug(f"合并后数据列: {', '.join(self.df.columns)}")
            # 打印最后几行看看结果
            Logger.debug(f"最后5行数据（含指标）:\n{self.df.tail().to_string()}")

            return True

        except Exception as e:
            Logger.error(f"计算一目均衡表指标失败: {str(e)}")
            # 可以考虑打印更详细的 traceback
            import traceback
            Logger.error(traceback.format_exc())
            return False

    def generate_signals(self, days=30):
        """
        为历史数据生成信号序列

        Args:
            days: 用于判断的历史窗口大小，默认30天

        Returns:
            bool: 是否成功生成信号
        """
        try:
            if self.df is None or self.df.empty:
                Logger.error("没有历史数据可用于生成信号")
                return False

            required_cols = ['Close', 'leading_span_a', 'leading_span_b', 'conversion_line', 'base_line']
            missing_cols = [col for col in required_cols if col not in self.df.columns]
            if missing_cols:
                Logger.error(f"数据缺少用于生成信号的列: {missing_cols}")
                return False

            Logger.info(f"开始生成历史信号序列（向量化优化），使用{days}天窗口...")

            # --- 预计算列 ---
            price_col = 'Close'
            df = self.df # 创建一个引用，避免反复写 self.df
            chikou_period = self.ichimoku_params['base']

            # 云顶和云底
            cloud_top = df[['leading_span_a', 'leading_span_b']].max(axis=1)
            cloud_bottom = df[['leading_span_a', 'leading_span_b']].min(axis=1)
            cloud_diff = cloud_top - cloud_bottom

            # 价格是否在云层上方
            price_above_cloud = df[price_col] > cloud_top

            # 计算过去 N 天价格在云层上方的天数比例 (N = days + 1)
            # 使用 rolling 计算窗口内的和，然后除以窗口大小
            # 注意：rolling 默认包含当前行，所以窗口大小是 days + 1
            # min_periods=1 确保在数据开始部分也能计算
            window_size = days + 1
            days_above_cloud_series = price_above_cloud.rolling(window=window_size, min_periods=1).sum()
            # 计算实际窗口大小，处理数据开头窗口不足的情况
            actual_window_size = df.index.to_series().rolling(window=window_size, min_periods=1).count()
            true_above_ratio_series = (days_above_cloud_series / actual_window_size).fillna(0) # 用0填充NaN

            # --- 计算各维度得分 (向量化) ---

            # 1. 云层位置得分
            conditions_cloud = [
                df[price_col] > cloud_top,
                df[price_col] < cloud_bottom,
                cloud_diff <= 0 # 处理云层重合或反转的情况
            ]
            choices_cloud = [
                100,
                -100,
                0 # 云层重合或反转时得分为0
            ]
            # 计算价格在云层内部的相对位置得分
            # 使用 np.where 避免除以零
            relative_pos_score = np.where(
                cloud_diff > 0,
                ((df[price_col] - cloud_bottom) / cloud_diff * 100) - 50,
                0 # 如果云层宽度为0，则内部得分为0
            )
            cloud_score = np.select(conditions_cloud, choices_cloud, default=relative_pos_score)
            cloud_score = np.nan_to_num(cloud_score, nan=0) # 将计算中可能产生的NaN替换为0

            # 2. 转换线与基准线关系得分
            conditions_conv_base = [
                df['conversion_line'] > df['base_line'],
                df['conversion_line'] < df['base_line']
            ]
            choices_conv_base = [100, -100]
            conversion_base_score = np.select(conditions_conv_base, choices_conv_base, default=0)
            conversion_base_score = np.where(df['conversion_line'].isna() | df['base_line'].isna(), 0, conversion_base_score) # 处理NaN

            # 3. 价格与基准线关系得分
            conditions_price_base = [
                df[price_col] > df['base_line'],
                df[price_col] < df['base_line']
            ]
            choices_price_base = [100, -100]
            price_base_score = np.select(conditions_price_base, choices_price_base, default=0)
            price_base_score = np.where(df[price_col].isna() | df['base_line'].isna(), 0, price_base_score) # 处理NaN

            # 4. 迟行带位置得分
            # 获取 chikou_period 天前的云顶和云底
            past_cloud_top = cloud_top.shift(chikou_period)
            past_cloud_bottom = cloud_bottom.shift(chikou_period)

            conditions_chikou = [
                df[price_col] > past_cloud_top,
                df[price_col] < past_cloud_bottom
            ]
            choices_chikou = [100, -100]
            chikou_score = np.select(conditions_chikou, choices_chikou, default=0)
            # 如果当前价格或过去的云数据为NaN，则得分为0
            chikou_score = np.where(df[price_col].isna() | past_cloud_top.isna() | past_cloud_bottom.isna(), 0, chikou_score)

            # 5. 未来云层形态得分
            conditions_future_cloud = [
                df['leading_span_a'] > df['leading_span_b'],
                df['leading_span_a'] < df['leading_span_b']
            ]
            choices_future_cloud = [100, -100]
            future_cloud_score = np.select(conditions_future_cloud, choices_future_cloud, default=0)
            future_cloud_score = np.where(df['leading_span_a'].isna() | df['leading_span_b'].isna(), 0, future_cloud_score) # 处理NaN

            # 权重配置
            weights = {
                'cloud': 0.40,
                'conversion_base': 0.20,
                'price_base': 0.20,
                'chikou': 0.15,
                'future_cloud': 0.05
            }

            # 计算总分 (向量化)
            total_score = (
                cloud_score * weights['cloud'] +
                conversion_base_score * weights['conversion_base'] +
                price_base_score * weights['price_base'] +
                chikou_score * weights['chikou'] +
                future_cloud_score * weights['future_cloud']
            )

            # 判断是否满足买入条件 (向量化)
            meets_criteria = (
                (total_score > 30) &          # 总分为正且显著
                (cloud_score > 50) &          # 价格明显在云层上方 (使用计算出的得分)
                (true_above_ratio_series >= 0.6) # 保持至少60%时间在云层上方
            )

            # 设置原始信号列
            self.df['Signal_Raw'] = np.where(meets_criteria, 1, 0)

            # 创建实际交易信号列（向后移动一天，因为信号在当天收盘后产生）
            self.df['Signal'] = self.df['Signal_Raw'].shift(1).fillna(0).astype(int) # 确保为整数

            Logger.info("历史信号序列生成完成 (向量化)")
            Logger.info(f"生成了 {int(self.df['Signal'].sum())} 个买入信号")
            Logger.debug(f"最后10天的信号:\n{self.df[['Close', 'Signal_Raw', 'Signal']].tail(10)}") # 显示原始信号和最终信号

            return True

        except Exception as e:
            Logger.error(f"生成历史信号序列失败 (向量化): {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            return False

    def run_backtest(self, initial_capital=100000, position_sizing_ratio=0.1, exit_on_base_cross=True, stop_loss_pct=None):
        """
        执行回测，生成交易日志 (使用动态退出信号和可选的固定百分比止损)。

        Args:
            initial_capital (float): 初始资金。
            position_sizing_ratio (float): 每次交易投入当前总权益的比例（0-1之间）。
            exit_on_base_cross (bool): 是否在价格跌破基准线时退出。
            stop_loss_pct (float, optional): 固定百分比止损。例如，8 表示 8% 止损。默认为 None (不启用)。

        Returns:
            list: 包含交易记录的列表，如果失败则返回 None。
        """
        try:
            if self.df is None or self.df.empty:
                Logger.error("没有数据可用于回测")
                return None

            if 'Signal' not in self.df.columns:
                Logger.error("未找到交易信号列，请先运行 generate_signals()")
                return None

            required_cols = ['Open', 'Low', 'Close', 'base_line'] # 确保退出条件所需的列存在 (增加了 Low 用于止损)
            missing_cols = [col for col in required_cols if col not in self.df.columns]
            if missing_cols:
                Logger.error(f"数据缺少用于回测或退出条件的列: {missing_cols}")
                return None

            exit_conditions_desc = []
            if exit_on_base_cross:
                exit_conditions_desc.append("价格跌破基准线")
            if stop_loss_pct is not None and stop_loss_pct > 0:
                 exit_conditions_desc.append(f"{stop_loss_pct}% 固定止损")
            if not exit_conditions_desc:
                 exit_conditions_desc.append("持有至结束")

            Logger.info(f"开始动态退出回测，初始资金: {initial_capital:,.2f}, 仓位比例: {position_sizing_ratio:.1%}, 退出条件: {', '.join(exit_conditions_desc)}")

            df = self.df.copy() # 使用副本进行操作
            n = len(df)

            # --- 1. 识别潜在入场点 ---
            # 信号为1，且开盘价有效
            potential_entry_indices = df.index[ (df['Signal'] == 1) & (~df['Open'].isna()) ]
            if potential_entry_indices.empty:
                Logger.info("回测期间没有有效的买入信号")
                self.trade_log = []
                self.final_equity = initial_capital
                return []

            # --- 2. 模拟交易和计算权益 (迭代处理动态仓位和退出) ---
            trades = []
            current_equity = initial_capital
            final_equity_calculated = initial_capital
            last_exit_loc = -1 # 记录上一次退出的整数位置
            in_position = False # 是否持有仓位
            entry_loc = -1 # 当前持仓的入场位置
            shares_held = 0 # 当前持有的股数
            entry_equity_at_trade = 0 # 入场时的权益
            stop_loss_price = np.nan # 止损价格

            # 将索引转换为整数位置以便于计算
            potential_entry_locs = df.index.get_indexer_for(potential_entry_indices)
            all_locs = np.arange(n) # 所有日期的位置

            for current_loc in all_locs:
                current_date = df.index[current_loc]

                # --- 检查是否可以入场 ---
                # 条件：不在持仓中，当前位置是潜在入场点，且当前位置在上一次退出之后
                can_enter = (not in_position) and (current_loc in potential_entry_locs) and (current_loc > last_exit_loc)

                if can_enter:
                    entry_price = df['Open'].iloc[current_loc]
                    if pd.isna(entry_price):
                        Logger.debug(f"日期={current_date} 开盘价无效，无法入场")
                        continue # 跳过无效开盘价的入场

                    # 计算仓位
                    capital_to_invest = current_equity * position_sizing_ratio
                    shares_to_buy = int(capital_to_invest / entry_price // 100 * 100)

                    if shares_to_buy <= 0:
                        Logger.debug(f"日期={current_date} 计算买入股数为0 (价格过高或比例资金不足)，跳过入场")
                        continue # 跳过买不起的交易

                    # 标记入场并记录信息
                    in_position = True
                    entry_loc = current_loc
                    shares_held = shares_to_buy
                    entry_equity_at_trade = current_equity # 记录入场时的权益

                    # 计算止损价格
                    if stop_loss_pct is not None and stop_loss_pct > 0:
                        stop_loss_price = entry_price * (1 - stop_loss_pct / 100)
                        Logger.debug(f"入场: 日期={current_date}, 价格={entry_price:.2f}, 股数={shares_held}, 止损价={stop_loss_price:.2f}")
                    else:
                        stop_loss_price = np.nan # 确保未启用时为 NaN
                        Logger.debug(f"入场: 日期={current_date}, 价格={entry_price:.2f}, 股数={shares_held}")

                # --- 检查是否需要退出 ---
                # 条件：持有仓位
                elif in_position:
                    exit_signal_triggered = False
                    exit_reason = ""

                    # --- 检查退出条件 (止损优先) ---
                    # 1. 检查固定百分比止损 (如果启用)
                    if not pd.isna(stop_loss_price): # 检查止损是否已设置
                        low_price = df['Low'].iloc[current_loc]
                        if not pd.isna(low_price) and low_price <= stop_loss_price:
                            exit_signal_triggered = True
                            exit_reason = f"止损触发 ({stop_loss_pct}%)"
                            Logger.debug(f"退出信号触发: 日期={current_date}, 原因={exit_reason}, 最低价={low_price:.2f}, 止损价={stop_loss_price:.2f}")

                    # 2. 检查价格跌破基准线 (如果启用且止损未触发)
                    if not exit_signal_triggered and exit_on_base_cross:
                        close_price = df['Close'].iloc[current_loc]
                        base_line = df['base_line'].iloc[current_loc]
                        if not pd.isna(close_price) and not pd.isna(base_line) and close_price < base_line:
                            exit_signal_triggered = True
                            exit_reason = "价格跌破基准线"
                            Logger.debug(f"退出信号触发: 日期={current_date}, 原因={exit_reason}, 收盘价={close_price:.2f}, 基准线={base_line:.2f}")

                    # 检查是否到达回测最后一天
                    is_last_day = (current_loc == n - 1)
                    if is_last_day and not exit_signal_triggered:
                        exit_signal_triggered = True
                        exit_reason = "回测结束"
                        Logger.debug(f"退出信号触发: 日期={current_date}, 原因={exit_reason}")

                    # 如果触发退出信号
                    if exit_signal_triggered:
                        # 确定退出日期和价格
                        exit_loc_planned = current_loc + 1
                        exit_date = None
                        exit_price = np.nan
                        exit_loc_actual = -1

                        if is_last_day: # 如果是最后一天触发（因为回测结束）
                            # 使用最后一天的收盘价退出
                            exit_price = df['Close'].iloc[current_loc]
                            exit_date = current_date
                            exit_loc_actual = current_loc
                            if pd.isna(exit_price):
                                last_valid_close_idx = df['Close'].iloc[:current_loc+1].last_valid_index()
                                if last_valid_close_idx is not None:
                                    exit_price = df.loc[last_valid_close_idx, 'Close']
                                    exit_date = last_valid_close_idx
                                    exit_loc_actual = df.index.get_loc(exit_date)
                                else:
                                     Logger.warning(f"无法为交易 (入场 {df.index[entry_loc]}) 找到最后有效的退出价格，权益可能不准确")
                                     in_position = False
                                     last_exit_loc = current_loc
                                     continue # 不记录这笔交易

                        elif exit_loc_planned < n: # 如果不是最后一天触发，计划在下一天开盘退出
                            exit_price = df['Open'].iloc[exit_loc_planned]
                            exit_date = df.index[exit_loc_planned]
                            exit_loc_actual = exit_loc_planned
                            if pd.isna(exit_price):
                                Logger.warning(f"退出信号在 {current_date} 触发，但下一天 {exit_date} 开盘价无效。尝试使用信号当天 {current_date} 收盘价退出。")
                                exit_price = df['Close'].iloc[current_loc] # 改用当天收盘价
                                exit_date = current_date
                                exit_loc_actual = current_loc
                                if pd.isna(exit_price):
                                    last_valid_close_idx = df['Close'].iloc[:current_loc+1].last_valid_index()
                                    if last_valid_close_idx is not None:
                                        exit_price = df.loc[last_valid_close_idx, 'Close']
                                        exit_date = last_valid_close_idx
                                        exit_loc_actual = df.index.get_loc(exit_date)
                                        Logger.warning(f"当天收盘价也无效，使用之前最近的有效收盘价 {exit_date} ({exit_price:.2f}) 退出。")
                                    else:
                                        Logger.error(f"无法为交易 (入场 {df.index[entry_loc]}) 找到有效的退出价格，跳过记录，权益可能不准确")
                                        in_position = False
                                        last_exit_loc = current_loc
                                        continue
                        else: # 计划退出日超出范围
                             Logger.error(f"逻辑错误：计划退出位置 {exit_loc_planned} 超出范围，但不是最后一天。")
                             in_position = False # 强制退出避免死循环
                             last_exit_loc = current_loc
                             continue

                        # 获取入场信息
                        entry_date_actual = df.index[entry_loc]
                        entry_price_actual = df['Open'].iloc[entry_loc] # 假设入场价总是有效的

                        # 计算盈亏
                        profit_amount = (exit_price - entry_price_actual) * shares_held
                        profit_pct = ((exit_price - entry_price_actual) / entry_price_actual) * 100 if entry_price_actual != 0 else 0
                        days_held = exit_loc_actual - entry_loc # 实际持仓天数

                        trade_info = {
                            'entry_date': entry_date_actual,
                            'entry_price': entry_price_actual,
                            'exit_date': exit_date,
                            'exit_price': exit_price,
                            'hold_days': days_held, # 注意：这里的 hold_days 是实际持仓天数
                            'shares': shares_held,
                            'profit_amount': profit_amount,
                            'profit_pct': profit_pct,
                            'entry_equity': entry_equity_at_trade, # 使用入场时记录的权益
                            'exit_reason': exit_reason
                        }
                        trades.append(trade_info)

                        # 更新当前权益
                        current_equity += profit_amount
                        final_equity_calculated = current_equity # 实时更新计算出的最终权益

                        Logger.debug(f"退出: 日期={exit_date}, 价格={exit_price:.2f}, 原因={exit_reason}. "
                                   f"盈亏={profit_amount:.2f} ({profit_pct:.2f}%), "
                                   f"当前权益={current_equity:.2f}")

                        # 标记退出完成
                        in_position = False
                        last_exit_loc = exit_loc_actual # 更新最后退出位置
                        shares_held = 0 # 清理持仓股数变量
                        entry_equity_at_trade = 0 # 清理入场权益变量
                        stop_loss_price = np.nan # 清理止损价格


            # --- 3. 保存结果 ---
            self.trade_log = trades
            if in_position:
                 Logger.warning("回测结束时仍有未平仓位，这不应该发生。请检查逻辑。")
                 # 可以考虑强制平仓或忽略

            self.final_equity = final_equity_calculated # 使用迭代计算得到的最终权益

            Logger.info(f"动态退出回测执行完毕，共完成 {len(trades)} 笔有效交易")
            Logger.info(f"初始资金: {initial_capital:,.2f}, 最终权益: {self.final_equity:,.2f}, "
                       f"总收益率: {((self.final_equity-initial_capital)/initial_capital*100):.2f}%")

            if not trades:
                Logger.info("回测期间没有产生任何有效交易")
                return []

            return self.trade_log

        except Exception as e:
            Logger.error(f"动态退出回测执行失败: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            self.trade_log = None # 标记失败
            self.final_equity = initial_capital # 重置为初始值
            return None # 返回 None 表示失败
            self.trade_log = None
            self.final_equity = initial_capital # 失败时重置为初始资金
            return None

    def calculate_equity_and_drawdown(self, initial_capital=100000):
        """
        计算权益曲线和最大回撤。

        Args:
            initial_capital (float): 初始资金。

        Returns:
            tuple: (权益曲线DataFrame, 最大回撤百分比)
        """
        try:
            if not hasattr(self, 'trade_log') or self.trade_log is None:
                Logger.error("没有交易日志可供分析")
                return None, None

            if not self.trade_log:  # 空列表
                Logger.warning("交易日志为空，无法计算权益曲线和回撤")
                return None, None

            # 按日期排序交易记录
            sorted_trades = sorted(self.trade_log, key=lambda x: x['exit_date'])

            # 获取回测的起止日期
            start_date = min(trade['entry_date'] for trade in self.trade_log)
            end_date = max(trade['exit_date'] for trade in self.trade_log)

            # 创建完整的日期范围的DataFrame
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')

            # 创建每日盈亏DataFrame
            daily_pnl = pd.DataFrame(index=date_range, columns=['PnL'])
            daily_pnl['PnL'] = 0.0

            # 将每笔交易的盈亏记录到对应的日期
            for trade in sorted_trades:
                exit_date = trade['exit_date']
                if exit_date in daily_pnl.index:  # 确保日期存在
                    daily_pnl.loc[exit_date, 'PnL'] += trade['profit_amount']

            # 计算累计权益
            daily_pnl['Equity'] = initial_capital + daily_pnl['PnL'].cumsum()

            # 计算历史最高点
            daily_pnl['Peak'] = daily_pnl['Equity'].expanding().max()

            # 计算回撤金额和百分比
            daily_pnl['Drawdown'] = daily_pnl['Peak'] - daily_pnl['Equity']
            daily_pnl['DrawdownPct'] = (daily_pnl['Drawdown'] / daily_pnl['Peak']) * 100

            # 计算最大回撤
            max_drawdown_pct = daily_pnl['DrawdownPct'].max()

            # 验证最终权益是否与 run_backtest 的结果一致
            if hasattr(self, 'final_equity'):
                final_equity = daily_pnl['Equity'].iloc[-1]
                if abs(final_equity - self.final_equity) > 0.01:  # 允许0.01的误差
                    Logger.warning(f"权益计算不一致！run_backtest最终权益: {self.final_equity:.2f}, "
                                 f"重建的最终权益: {final_equity:.2f}")

            Logger.info("\n=== 权益曲线和回撤分析 ===")
            Logger.info(f"初始资金: {initial_capital:,.2f}")
            Logger.info(f"最终权益: {daily_pnl['Equity'].iloc[-1]:,.2f}")
            Logger.info(f"最大回撤: {max_drawdown_pct:.2f}%")
            Logger.info(f"最大回撤发生于: {daily_pnl['DrawdownPct'].idxmax().strftime('%Y-%m-%d')}")

            return daily_pnl, max_drawdown_pct

        except Exception as e:
            Logger.error(f"计算权益曲线和回撤时发生错误: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            return None, None

    def calculate_performance(self, initial_capital=100000):
        """
        根据 self.trade_log 计算详细的回测绩效指标。

        Args:
            initial_capital (float): 用于计算总回报率和权益曲线的初始资金。

        Returns:
            dict: 包含绩效指标的字典，如果失败则返回 None。
        """
        if not hasattr(self, 'trade_log') or self.trade_log is None:
            Logger.error("没有交易日志可供分析，请先运行 run_backtest()")
            return None
        if not self.trade_log:  # 如果 trade_log 是空列表
            Logger.warning("交易日志为空，无法计算绩效指标。")
            return {}

        Logger.info("开始计算回测绩效指标...")

        trades = self.trade_log
        trade_df = pd.DataFrame(trades)  # 将列表转换为 DataFrame 更方便计算

        # --- 基本统计 ---
        total_trades = len(trade_df)
        winning_trades_df = trade_df[trade_df['profit_amount'] > 0]
        losing_trades_df = trade_df[trade_df['profit_amount'] <= 0]  # 包括盈亏为0的

        num_winning_trades = len(winning_trades_df)
        num_losing_trades = len(losing_trades_df)
        win_rate = (num_winning_trades / total_trades) * 100 if total_trades > 0 else 0

        # --- 盈亏统计 ---
        gross_profit = winning_trades_df['profit_amount'].sum() if not winning_trades_df.empty else 0
        gross_loss = losing_trades_df['profit_amount'].sum() if not losing_trades_df.empty else 0  # 亏损值为负数
        total_net_profit = gross_profit + gross_loss  # 即 sum(trade_df['profit_amount'])

        profit_factor = abs(gross_profit / gross_loss) if gross_loss != 0 else float('inf')  # 无穷大表示没有亏损

        avg_profit_per_trade = total_net_profit / total_trades if total_trades > 0 else 0
        avg_profit_pct_per_trade = trade_df['profit_pct'].mean() if total_trades > 0 else 0

        avg_win_amount = winning_trades_df['profit_amount'].mean() if num_winning_trades > 0 else 0
        avg_win_pct = winning_trades_df['profit_pct'].mean() if num_winning_trades > 0 else 0
        avg_loss_amount = losing_trades_df['profit_amount'].mean() if num_losing_trades > 0 else 0  # 值为负
        avg_loss_pct = losing_trades_df['profit_pct'].mean() if num_losing_trades > 0 else 0  # 值为负

        avg_win_loss_ratio = abs(avg_win_amount / avg_loss_amount) if avg_loss_amount != 0 else float('inf')

        # --- 持仓期 ---
        avg_hold_days = trade_df['hold_days'].mean() if total_trades > 0 else 0
        min_hold_days = trade_df['hold_days'].min() if total_trades > 0 else 0
        max_hold_days = trade_df['hold_days'].max() if total_trades > 0 else 0

        # --- 极值统计 ---
        max_win_pct = trade_df['profit_pct'].max() if total_trades > 0 else 0
        max_loss_pct = trade_df['profit_pct'].min() if total_trades > 0 else 0  # 值为负

        # --- 连续盈亏统计 ---
        profit_streaks = []
        loss_streaks = []
        current_streak = 0
        current_type = None  # 'win' or 'loss'

        for _, trade in trade_df.iterrows():
            if trade['profit_amount'] > 0:
                if current_type == 'win':
                    current_streak += 1
                else:
                    if current_type == 'loss' and current_streak > 0:
                        loss_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'win'
            else:
                if current_type == 'loss':
                    current_streak += 1
                else:
                    if current_type == 'win' and current_streak > 0:
                        profit_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'loss'

        # 添加最后一个streak
        if current_type == 'win':
            profit_streaks.append(current_streak)
        elif current_type == 'loss':
            loss_streaks.append(current_streak)

        max_consecutive_wins = max(profit_streaks) if profit_streaks else 0
        max_consecutive_losses = max(loss_streaks) if loss_streaks else 0

        # --- 计算权益曲线和最大回撤 ---
        equity_df, max_drawdown_pct = self.calculate_equity_and_drawdown(initial_capital)
        if equity_df is not None:
            final_capital = equity_df['Equity'].iloc[-1]
            total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100
        else:
            final_capital = initial_capital + total_net_profit
            total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100
            max_drawdown_pct = None

        # --- 存储结果 ---
        performance_summary = {
            'total_trades': total_trades,
            'winning_trades': num_winning_trades,
            'losing_trades': num_losing_trades,
            'win_rate_pct': win_rate,
            'total_net_profit': total_net_profit,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,  # 值为负
            'profit_factor': profit_factor,
            'avg_profit_per_trade': avg_profit_per_trade,
            'avg_profit_pct_per_trade': avg_profit_pct_per_trade,
            'avg_win_amount': avg_win_amount,
            'avg_win_pct': avg_win_pct,
            'avg_loss_amount': avg_loss_amount,  # 值为负
            'avg_loss_pct': avg_loss_pct,  # 值为负
            'avg_win_loss_ratio': avg_win_loss_ratio,
            'avg_hold_days': avg_hold_days,
            'min_hold_days': min_hold_days,
            'max_hold_days': max_hold_days,
            'max_win_pct': max_win_pct,
            'max_loss_pct': max_loss_pct,  # 值为负
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'equity_curve': equity_df if equity_df is not None else None
        }

        self.performance_summary = performance_summary

        # --- 打印结果 ---
        Logger.info("\n=== 回测绩效指标 ===")

        # 交易统计
        Logger.info("\n--- 交易统计 ---")
        Logger.info(f"总交易次数: {total_trades}")
        Logger.info(f"盈利交易: {num_winning_trades}")
        Logger.info(f"亏损交易: {num_losing_trades}")
        Logger.info(f"胜率: {win_rate:.2f}%")

        # 盈亏统计
        Logger.info("\n--- 盈亏统计 ---")
        Logger.info(f"总净利润: {total_net_profit:,.2f}")
        Logger.info(f"总盈利: {gross_profit:,.2f}")
        Logger.info(f"总亏损: {gross_loss:,.2f}")
        Logger.info(f"盈利因子: {profit_factor:.2f}")
        Logger.info(f"平均每笔盈亏: {avg_profit_per_trade:,.2f}")
        Logger.info(f"平均每笔收益率: {avg_profit_pct_per_trade:.2f}%")

        # 盈亏比
        Logger.info("\n--- 盈亏比 ---")
        Logger.info(f"平均盈利金额: {avg_win_amount:,.2f} ({avg_win_pct:.2f}%)")
        Logger.info(f"平均亏损金额: {avg_loss_amount:,.2f} ({avg_loss_pct:.2f}%)")
        Logger.info(f"盈亏比: {avg_win_loss_ratio:.2f}")

        # 持仓统计
        Logger.info("\n--- 持仓统计 ---")
        Logger.info(f"平均持仓天数: {avg_hold_days:.1f}")
        Logger.info(f"最短持仓天数: {min_hold_days}")
        Logger.info(f"最长持仓天数: {max_hold_days}")

        # 极值统计
        Logger.info("\n--- 极值统计 ---")
        Logger.info(f"最大单笔盈利: {max_win_pct:.2f}%")
        Logger.info(f"最大单笔亏损: {max_loss_pct:.2f}%")
        Logger.info(f"最大连续盈利次数: {max_consecutive_wins}")
        Logger.info(f"最大连续亏损次数: {max_consecutive_losses}")

        # 资金统计
        Logger.info("\n--- 资金统计 ---")
        Logger.info(f"初始资金: {initial_capital:,.2f}")
        Logger.info(f"期末资金: {final_capital:,.2f}")
        Logger.info(f"总收益率: {total_return_pct:.2f}%")
        if max_drawdown_pct is not None:
            Logger.info(f"最大回撤: {max_drawdown_pct:.2f}%")

        return self.performance_summary

if __name__ == "__main__":
    # 测试用例：获取平安银行2年日线数据
    stock_code = '600360.SH'  # 你可以替换为其他股票代码
    backtester = IchimokuBacktester(data_source='tushare')

    # 第一步：获取历史数据
    Logger.info("="*50)
    Logger.info("第一步：获取历史数据")
    # 使用指定的日期范围获取数据
    start_date = '2025-01-01'
    end_date = '2025-04-25'
    Logger.info(f"测试获取日期范围: {start_date} 到 {end_date}")
    df = backtester.get_history_data(stock_code, start_date=start_date, end_date=end_date)
    if df is None:
        Logger.error(f"获取 {stock_code} 在 {start_date} 到 {end_date} 的历史数据失败")
        exit(1)

    Logger.info(f"成功获取 {stock_code} 的历史数据，共 {len(df)} 行")
    Logger.info(f"原始数据列: {df.columns.tolist()}")

    # 第二步：计算指标
    Logger.info("="*50)
    Logger.info("第二步：计算一目均衡表指标")
    success = backtester.calculate_indicators()

    if not success:
        Logger.error("指标计算失败！")
        exit(1)

    Logger.info("指标计算成功！")
    # 显示计算后的数据信息
    all_columns = backtester.df.columns.tolist()
    ichimoku_columns = [col for col in all_columns if col not in ['High', 'Low', 'Close', 'Open', 'Volume']]

    Logger.info("一目均衡表指标列:")
    for col in ichimoku_columns:
        Logger.info(f"- {col}")

    # 第三步：生成历史信号
    Logger.info("="*50)
    Logger.info("第三步：生成历史信号")
    success = backtester.generate_signals(days=30)

    if not success:
        Logger.error("信号生成失败！")
        exit(1)

    Logger.info("信号生成成功！")

    # 显示信号统计信息
    total_days = len(backtester.df)
    signal_days = backtester.df['Signal'].sum()
    signal_ratio = signal_days / total_days * 100

    Logger.info("\n信号统计:")
    Logger.info(f"- 总交易日数: {total_days}")
    Logger.info(f"- 买入信号天数: {signal_days}")
    Logger.info(f"- 信号比例: {signal_ratio:.2f}%")

    # 第四步：执行回测
    Logger.info("="*50)
    Logger.info("第四步：执行回测")
    # 调用更新后的回测方法，移除 hold_days，添加 stop_loss_pct
    trades = backtester.run_backtest(
        initial_capital=100000,
        position_sizing_ratio=0.1,
        exit_on_base_cross=True, # 启用基准线退出
        stop_loss_pct=8 # 测试 8% 固定止损
    )

    if trades is None:
        Logger.error("回测执行失败！")
        exit(1)

    # 第五步：计算绩效指标
    Logger.info("="*50)
    Logger.info("第五步：计算绩效指标")
    performance = backtester.calculate_performance(initial_capital=100000)  # 设置初始资金10万

    if performance is None:
        Logger.error("绩效指标计算失败！")
        exit(1)
    elif not performance:
        Logger.warning("没有交易记录，无法计算绩效指标。")

    # 显示详细的交易记录
    if trades:
        Logger.info("\n=== 详细交易记录 ===")
        for i, trade in enumerate(trades, 1):
            Logger.info(f"\n交易 #{i}:")
            Logger.info(f"买入: {trade['entry_date']} @ {trade['entry_price']:.2f}")
            Logger.info(f"卖出: {trade['exit_date']} @ {trade['exit_price']:.2f}")
            Logger.info(f"持仓天数: {trade['hold_days']}")
            Logger.info(f"交易股数: {trade['shares']}")
            Logger.info(f"盈亏: {trade['profit_amount']:.2f} ({trade['profit_pct']:.2f}%)")
            if 'note' in trade:
                Logger.info(f"备注: {trade['note']}")
    else:
        Logger.info("\n没有交易记录")
