# src/backtest/low_vol_breakout_backtest_v2.py
import pandas as pd
import numpy as np
import talib # Assuming TA-Lib is installed
import sys
import os

# Add the project root directory to the Python path BEFORE attempting src imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Assuming base class or necessary imports exist
# from .base_backtester import BaseBacktester # Example if using a base class
# Use absolute imports from the project root ('src') - should work now
from src.utils.logger import Logger
from src.data_providers.stock_data_provider import StockDataProviderFactory

class LowVolBreakoutBacktesterV2:
    """
    Backtester for the '通用横盘放量突破因子模板 V2.1'.

    Factors based on the provided JSON definition:
    - Core Prerequisites: PRE_VOLATILITY_LOW, PRE_VOLUME_LOW
    - Core Breakout Signals: SIG_VOLUME_BREAK, SIG_PRICE_BREAK
    - Confirmation Signals: CONF_MACD_SIGNAL, CONF_RSI_STRONG
    - Optional Confirmation: OPT_MA_CONFIRMATION

    The combination logic (AND/OR, minimum confirmations) is configurable via parameters.
    """
    def __init__(self, data_source='tushare', logger=None, index_code='002165.SZ'):
        self.data_provider = StockDataProviderFactory.get_provider(data_source)
        self.logger = logger if logger else Logger.get_logger()
        self.df = None
        self.index_df = None
        self.index_code = index_code
        self.trades = []
        self.initial_capital = 100000
        self.position_sizing_ratio = 0.1 # Default, can be overridden

        # Default parameters based on V2 JSON definition
        self.params = {
            # --- General Backtest Settings ---
            'hold_days': 10, # Default hold period (adjust as needed)
            'stop_loss_pct': 0.07, # Example stop loss
            'take_profit_pct': 0.20, # Example take profit
            'atr_period': 14,
            'use_atr_stop': False,
            'atr_multiplier': 2.0,
            'index_trend_ma_period': 60, # MA period for index trend filter

            # --- V2 Factor Configuration ---
            'factor_logic': {
                'prerequisites': ['PRE_VOLATILITY_LOW', 'PRE_VOLUME_LOW'], # List of IDs, applied with AND
                'core_signals': ['SIG_VOLUME_BREAK', 'SIG_PRICE_BREAK'], # List of IDs, applied with AND
                'confirmation_signals': ['CONF_MACD_SIGNAL', 'CONF_RSI_STRONG'], # List of IDs
                'min_confirmation_signals': 1, # Minimum number of confirmation signals required
                'optional_signals': ['OPT_MA_CONFIRMATION'], # List of IDs, applied with AND if enabled
                'use_optional_signals': True, # Whether to include optional signals in the final AND condition
                'use_index_trend_filter': True # Whether to apply index trend filter
            },

            # --- PRE_VOLATILITY_LOW ---
            'pre_volatility_low': {
                'enabled': True,
                'method': 'Option A', # 'Option A', 'Option B', 'Option C'
                # Option A (BBWidth)
                'bb_period': 20,
                'bb_stddev': 2,
                'bb_history': 60,
                'bb_percentile': 0.25,
                # Option B (Relative StdDev - Mid)
                'rel_std_period': 20,
                'rel_std_ma_period': 20,
                'rel_std_history': 120,
                'rel_std_percentile': 0.20,
                # Option C (StdDev - Long)
                'std_period': 30,
                'std_history': 252,
                'std_percentile': 0.20
            },
            # --- PRE_VOLUME_LOW ---
            'pre_volume_low': {
                'enabled': True,
                'period_short': 5,
                'period_long': 60,
                'multiplier': 0.7
            },
            # --- SIG_VOLUME_BREAK ---
            'sig_volume_break': {
                'enabled': True,
                'period': 20,
                'multiplier': 2.2,
                'period_short': 5,
                'period_long': 20
            },
            # --- SIG_PRICE_BREAK ---
            'sig_price_break': {
                'enabled': True,
                'use_hhv': True,
                'hhv_period': 20,
                'use_bbands_upper': True,
                'bbands_period': 20,
                'bbands_stddev': 2,
                'use_ma_break': True,
                'ma_period': 60
            },
            # --- CONF_MACD_SIGNAL ---
            'conf_macd_signal': {
                'enabled': True,
                'method': 'Option A', # 'Option A', 'Option B'
                'fast': 12,
                'slow': 26,
                'signal': 9
            },
            # --- CONF_RSI_STRONG ---
            'conf_rsi_strong': {
                'enabled': True,
                'period': 14,
                'threshold': 60
            },
            # --- OPT_MA_CONFIRMATION ---
            'opt_ma_confirmation': {
                'enabled': True, # Note: Actual use depends on factor_logic['use_optional_signals']
                'period_short': 5,
                'period_mid': 20
            }
        }

    def set_parameters(self, params):
        """
        Update parameters for optimization. Deep update for nested dicts.
        """
        for key, value in params.items():
            if isinstance(value, dict) and key in self.params and isinstance(self.params[key], dict):
                # Recursively update nested dictionaries
                def _deep_update(target, source):
                    for k, v in source.items():
                        if isinstance(v, dict) and k in target and isinstance(target[k], dict):
                            _deep_update(target[k], v)
                        else:
                            target[k] = v
                _deep_update(self.params[key], value)
            else:
                self.params[key] = value
        self.logger.info(f"Parameters updated: {self.params}") # Log updated params


    def get_history_data(self, stock_code, period='1y', interval='1d'):
        """Fetches historical data using the data provider."""
        try:
            # 根据 period 计算 start_date 和 end_date
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
            if period.endswith('y'):
                years = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=years)).strftime('%Y-%m-%d')
            elif period.endswith('m'):
                months = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(months=months)).strftime('%Y-%m-%d')
            elif period.endswith('d'):
                days = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(days=days)).strftime('%Y-%m-%d')
            else:
                # 默认一年
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=1)).strftime('%Y-%m-%d')

            self.df = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)
            if self.df is None or self.df.empty:
                self.logger.warning(f"No data fetched for {stock_code}")
                return False
            self.df.columns = [col.lower() for col in self.df.columns]
            if not isinstance(self.df.index, pd.DatetimeIndex):
                 self.df.index = pd.to_datetime(self.df.index)
            self.df.sort_index(inplace=True)

            # Fetch index data only if the filter is enabled in params
            if self.params.get('factor_logic', {}).get('use_index_trend_filter', False):
                try:
                    self.index_df = self.data_provider.get_stock_data_by_date(self.index_code, start_date, end_date)
                    if self.index_df is None or self.index_df.empty:
                         self.logger.warning(f"No index data fetched for {self.index_code}. Trend filter disabled.")
                         self.index_df = None
                    else:
                         self.index_df.columns = [col.lower() for col in self.index_df.columns]
                         if not isinstance(self.index_df.index, pd.DatetimeIndex):
                             self.index_df.index = pd.to_datetime(self.index_df.index)
                         self.index_df.sort_index(inplace=True)
                         self.logger.info(f"Index data fetched successfully for {self.index_code}")
                except Exception as idx_e:
                     self.logger.error(f"Error fetching index data for {self.index_code}: {idx_e}")
                     self.index_df = None
            else:
                self.index_df = None # Ensure it's None if filter is disabled
                self.logger.info("Index trend filter is disabled by parameters.")

            return True
        except Exception as e:
            self.logger.error(f"Error fetching stock data for {stock_code}: {e}")
            return False

    def calculate_indicators(self):
        """Calculates all necessary technical indicators based on V2 factors."""
        if self.df is None or self.df.empty:
            self.logger.error("DataFrame is empty. Cannot calculate indicators.")
            return False

        try:
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in self.df.columns for col in required_cols):
                missing = [col for col in required_cols if col not in self.df.columns]
                self.logger.error(f"Missing required columns: {missing}")
                return False

            # --- Calculate V2 Factors ---
            p = self.params # Shorthand for parameters

            # --- PRE_VOLATILITY_LOW ---
            factor_id = 'PRE_VOLATILITY_LOW'
            cfg = p['pre_volatility_low']
            if cfg['enabled']:
                if cfg['method'] == 'Option A':
                    upper, middle, lower = talib.BBANDS(self.df['close'], timeperiod=cfg['bb_period'], nbdevup=cfg['bb_stddev'], nbdevdn=cfg['bb_stddev'], matype=0)
                    bb_width = (upper - lower) / middle.replace(0, np.nan) # Avoid division by zero
                    rolling_quantile = bb_width.rolling(window=cfg['bb_history']).quantile(cfg['bb_percentile'])
                    self.df[factor_id] = bb_width < rolling_quantile
                elif cfg['method'] == 'Option B':
                    std_dev = self.df['close'].rolling(window=cfg['rel_std_period']).std()
                    ma_close = talib.MA(self.df['close'], timeperiod=cfg['rel_std_ma_period'])
                    rel_std_dev = std_dev / ma_close.replace(0, np.nan)
                    rolling_quantile = rel_std_dev.rolling(window=cfg['rel_std_history']).quantile(cfg['rel_std_percentile'])
                    self.df[factor_id] = rel_std_dev < rolling_quantile
                elif cfg['method'] == 'Option C':
                    std_dev = self.df['close'].rolling(window=cfg['std_period']).std()
                    rolling_quantile = std_dev.rolling(window=cfg['std_history']).quantile(cfg['std_percentile'])
                    self.df[factor_id] = std_dev < rolling_quantile
                else:
                    self.logger.warning(f"Invalid method '{cfg['method']}' for {factor_id}. Factor disabled.")
                    self.df[factor_id] = False # Default to False if method is invalid
            else:
                 self.df[factor_id] = True # If disabled, condition is always met (doesn't filter)

            # --- PRE_VOLUME_LOW ---
            factor_id = 'PRE_VOLUME_LOW'
            cfg = p['pre_volume_low']
            if cfg['enabled']:
                vol_ma_short = talib.MA(self.df['volume'], timeperiod=cfg['period_short'])
                vol_ma_long = talib.MA(self.df['volume'], timeperiod=cfg['period_long'])
                self.df[factor_id] = vol_ma_short < (vol_ma_long * cfg['multiplier'])
            else:
                self.df[factor_id] = True

            # --- SIG_VOLUME_BREAK ---
            factor_id = 'SIG_VOLUME_BREAK'
            cfg = p['sig_volume_break']
            if cfg['enabled']:
                vol_ma_base = talib.MA(self.df['volume'], timeperiod=cfg['period'])
                vol_ma_short = talib.MA(self.df['volume'], timeperiod=cfg['period_short'])
                vol_ma_long = talib.MA(self.df['volume'], timeperiod=cfg['period_long'])
                cond1 = self.df['volume'] > (vol_ma_base * cfg['multiplier'])
                cond2 = vol_ma_short > vol_ma_long
                self.df[factor_id] = cond1 & cond2
            else:
                self.df[factor_id] = True # If disabled, consider it met for logic purposes

            # --- SIG_PRICE_BREAK ---
            factor_id = 'SIG_PRICE_BREAK'
            cfg = p['sig_price_break']
            if cfg['enabled']:
                conditions = []
                if cfg['use_hhv']:
                    hhv = self.df['high'].rolling(window=cfg['hhv_period']).max()
                    conditions.append(self.df['close'] > hhv.shift(1)) # Break above *previous* high
                if cfg['use_bbands_upper']:
                    upper, _, _ = talib.BBANDS(self.df['close'], timeperiod=cfg['bbands_period'], nbdevup=cfg['bbands_stddev'], nbdevdn=cfg['bbands_stddev'], matype=0)
                    conditions.append(self.df['close'] > upper)
                if cfg['use_ma_break']:
                    ma_long = talib.MA(self.df['close'], timeperiod=cfg['ma_period'])
                    conditions.append(self.df['close'] > ma_long)

                if conditions:
                    # Combine conditions with OR logic
                    combined_cond = pd.DataFrame(conditions).T.any(axis=1)
                    self.df[factor_id] = combined_cond
                else:
                    self.logger.warning(f"No price break methods enabled for {factor_id}. Factor disabled.")
                    self.df[factor_id] = False # If no methods enabled, it fails
            else:
                self.df[factor_id] = True

            # --- CONF_MACD_SIGNAL ---
            factor_id = 'CONF_MACD_SIGNAL'
            cfg = p['conf_macd_signal']
            if cfg['enabled']:
                macd, macdsignal, macdhist = talib.MACD(self.df['close'],
                                                        fastperiod=cfg['fast'],
                                                        slowperiod=cfg['slow'],
                                                        signalperiod=cfg['signal'])
                if cfg['method'] == 'Option A':
                    # Diff is typically macdhist in talib
                    cond1 = macdhist > 0
                    cond2 = macdhist > macdhist.shift(1)
                    self.df[factor_id] = cond1 & cond2
                elif cfg['method'] == 'Option B':
                    # DIF is macd, DEA is macdsignal in talib
                    cond1 = macd > macdsignal
                    cond2 = macd > 0
                    self.df[factor_id] = cond1 & cond2
                else:
                    self.logger.warning(f"Invalid method '{cfg['method']}' for {factor_id}. Factor disabled.")
                    self.df[factor_id] = False
            else:
                self.df[factor_id] = False # Confirmation signals default to False if disabled

            # --- CONF_RSI_STRONG ---
            factor_id = 'CONF_RSI_STRONG'
            cfg = p['conf_rsi_strong']
            if cfg['enabled']:
                rsi = talib.RSI(self.df['close'], timeperiod=cfg['period'])
                self.df[factor_id] = rsi > cfg['threshold']
            else:
                self.df[factor_id] = False

            # --- OPT_MA_CONFIRMATION ---
            factor_id = 'OPT_MA_CONFIRMATION'
            cfg = p['opt_ma_confirmation']
            if cfg['enabled']: # Calculate even if not used in final logic, might be useful for analysis
                ma_short = talib.MA(self.df['close'], timeperiod=cfg['period_short'])
                ma_mid = talib.MA(self.df['close'], timeperiod=cfg['period_mid'])
                cond1 = self.df['close'] > ma_mid
                cond2 = ma_short > ma_mid
                self.df[factor_id] = cond1 & cond2
            else:
                self.df[factor_id] = False # Optional signals default to False if disabled

            # --- ATR Calculation (for stop-loss) ---
            if p.get('atr_period', 0) > 0 and all(c in self.df.columns for c in ['high', 'low', 'close']):
                 self.df['atr'] = talib.ATR(self.df['high'], self.df['low'], self.df['close'], timeperiod=p['atr_period'])
            else:
                 self.df['atr'] = np.nan

            # --- Index Trend Filter ---
            factor_id = 'INDEX_TREND_UP'
            if p.get('factor_logic', {}).get('use_index_trend_filter', False) and self.index_df is not None:
                 try:
                     index_ma_period = p.get('index_trend_ma_period', 60)
                     self.index_df['index_ma'] = talib.MA(self.index_df['close'], timeperiod=index_ma_period)
                     self.index_df[factor_id] = self.index_df['close'] > self.index_df['index_ma']
                     # Align index trend data with stock data by date
                     self.df = self.df.join(self.index_df[[factor_id]], how='left')
                     self.df[factor_id].fillna(False, inplace=True) # Assume no trend if index data is missing
                     self.logger.info(f"Index trend (MA{index_ma_period}) calculated and joined.")
                 except Exception as idx_calc_e:
                     self.logger.error(f"Error calculating index trend: {idx_calc_e}. Disabling trend filter.")
                     self.df[factor_id] = True # Default to True (no filter) if calculation fails
            else:
                 self.logger.info("Index trend filter is disabled or index data unavailable.")
                 self.df[factor_id] = True # Default to True (no filter)

            # --- Drop NA values ---
            # Identify all calculated factor columns (excluding ATR and Index Trend for initial drop)
            factor_cols = [
                'PRE_VOLATILITY_LOW', 'PRE_VOLUME_LOW', 'SIG_VOLUME_BREAK', 'SIG_PRICE_BREAK',
                'CONF_MACD_SIGNAL', 'CONF_RSI_STRONG', 'OPT_MA_CONFIRMATION'
            ]
            # Only consider enabled factors for dropping NA
            cols_to_check_na = [f for f in factor_cols if f in self.df.columns and self.params.get(f.lower(), {}).get('enabled', False)]

            # Drop rows where essential calculated indicators are NaN
            self.df.dropna(subset=cols_to_check_na, inplace=True)
            # Handle potential NaNs in ATR later if ATR stop is used

            self.logger.info("V2 Indicators calculated successfully.")
            return True

        except KeyError as ke:
             self.logger.error(f"Missing parameter key: {ke}. Check parameter structure.")
             import traceback
             self.logger.error(traceback.format_exc())
             return False
        except Exception as e:
            self.logger.error(f"Error calculating V2 indicators: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def generate_signals(self):
        """
        Generates potential buy signals (1) based on the V2 factor logic.
        Exit logic (sell) is handled dynamically in run_backtest.
        """
        if self.df is None or self.df.empty:
            self.logger.error("DataFrame is empty. Cannot generate signals.")
            return False

        try:
            p_logic = self.params['factor_logic']
            required_factors = []

            # --- Combine Prerequisites (AND) ---
            prereq_cond = pd.Series(True, index=self.df.index) # Start with True
            for factor_id in p_logic.get('prerequisites', []):
                if factor_id in self.df.columns:
                    prereq_cond &= self.df[factor_id]
                    required_factors.append(factor_id)
                else:
                    self.logger.warning(f"Prerequisite factor '{factor_id}' not found in DataFrame. Skipping.")

            # --- Combine Core Signals (AND) ---
            core_sig_cond = pd.Series(True, index=self.df.index)
            for factor_id in p_logic.get('core_signals', []):
                 if factor_id in self.df.columns:
                    core_sig_cond &= self.df[factor_id]
                    required_factors.append(factor_id)
                 else:
                    self.logger.warning(f"Core signal factor '{factor_id}' not found in DataFrame. Skipping.")

            # --- Combine Confirmation Signals (Count >= Threshold) ---
            conf_sig_count = pd.Series(0, index=self.df.index)
            for factor_id in p_logic.get('confirmation_signals', []):
                 if factor_id in self.df.columns:
                    # Ensure boolean conversion before summing
                    conf_sig_count += self.df[factor_id].astype(bool)
                    required_factors.append(factor_id)
                 else:
                    self.logger.warning(f"Confirmation signal factor '{factor_id}' not found in DataFrame. Skipping.")
            conf_sig_cond = conf_sig_count >= p_logic.get('min_confirmation_signals', 1)

            # --- Combine Optional Signals (AND, if enabled) ---
            opt_sig_cond = pd.Series(True, index=self.df.index)
            if p_logic.get('use_optional_signals', False):
                for factor_id in p_logic.get('optional_signals', []):
                    if factor_id in self.df.columns:
                        opt_sig_cond &= self.df[factor_id]
                        required_factors.append(factor_id)
                    else:
                        self.logger.warning(f"Optional signal factor '{factor_id}' not found in DataFrame. Skipping.")

            # --- Index Trend Filter ---
            index_trend_cond = pd.Series(True, index=self.df.index)
            if p_logic.get('use_index_trend_filter', False):
                if 'INDEX_TREND_UP' in self.df.columns:
                    index_trend_cond = self.df['INDEX_TREND_UP']
                    required_factors.append('INDEX_TREND_UP')
                else:
                    self.logger.warning("Index trend filter enabled but 'INDEX_TREND_UP' column not found. Filter disabled.")

            # --- Final Buy Condition ---
            # Combine all parts using AND logic
            buy_conditions = (
                prereq_cond &
                core_sig_cond &
                conf_sig_cond &
                opt_sig_cond & # Will be True if optional signals are not used
                index_trend_cond # Will be True if index filter is not used
            )

            # Check if all required factor columns exist before assigning signal
            required_factors = list(set(required_factors)) # Unique factors used
            if not all(col in self.df.columns for col in required_factors):
                 missing = [col for col in required_factors if col not in self.df.columns]
                 self.logger.error(f"Missing required factor columns for signal generation: {missing}. Cannot generate signals.")
                 return False


            self.df['signal'] = 0 # 0: No action, 1: Potential Buy Entry
            self.df.loc[buy_conditions, 'signal'] = 1

            self.logger.info(f"Potential V2 buy signals generated. Found {self.df['signal'].sum()} potential entries.")
            return True

        except KeyError as ke:
             self.logger.error(f"Missing parameter key in factor_logic: {ke}. Check parameter structure.")
             import traceback
             self.logger.error(traceback.format_exc())
             return False
        except Exception as e:
            self.logger.error(f"Error generating V2 signals: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False


    # ==========================================================================
    # run_backtest, calculate_performance, run_single_stock_backtest
    # These methods are largely unchanged from V1, as they operate on the
    # 'signal' column and portfolio logic. Minor adjustments might be needed
    # for parameter access or logging, but the core simulation remains similar.
    # We will copy them over, ensuring they use self.params correctly.
    # ==========================================================================

    def run_backtest(self, initial_capital=None, position_sizing_ratio=None):
        """
        Runs the backtest simulation based on generated signals.
        Uses parameters from self.params if arguments are None.
        """
        if self.df is None or 'signal' not in self.df.columns:
            self.logger.error("DataFrame or signals missing. Cannot run backtest.")
            return None

        # Use instance defaults if arguments are not provided
        current_initial_capital = initial_capital if initial_capital is not None else self.initial_capital
        current_position_sizing_ratio = position_sizing_ratio if position_sizing_ratio is not None else self.position_sizing_ratio

        # Access exit logic parameters from self.params
        hold_days = self.params.get('hold_days', 10) # Default if missing
        stop_loss_pct = self.params.get('stop_loss_pct', 0)
        take_profit_pct = self.params.get('take_profit_pct', 0)
        use_atr_stop = self.params.get('use_atr_stop', False)
        atr_multiplier = self.params.get('atr_multiplier', 2.0)
        atr_available = 'atr' in self.df.columns and not self.df['atr'].isnull().all()

        if use_atr_stop and not atr_available:
            self.logger.warning("ATR stop loss enabled, but 'atr' column is missing or all NaN. Disabling ATR stop.")
            use_atr_stop = False


        cash = current_initial_capital
        position = 0 # Shares held
        portfolio_value = cash
        self.trades = []
        entry_price = 0
        entry_date = None
        entry_atr = np.nan # Store ATR at entry if using ATR stop

        # Add columns to track portfolio value
        self.df['position'] = 0.0
        self.df['cash'] = float(current_initial_capital)
        self.df['portfolio_value'] = float(current_initial_capital)


        for i in range(len(self.df)):
            current_date = self.df.index[i]
            current_price = self.df['close'].iloc[i] # Use close price for transactions
            current_low = self.df['low'].iloc[i]
            current_high = self.df['high'].iloc[i]
            current_atr = self.df['atr'].iloc[i] if atr_available else np.nan
            signal = self.df['signal'].iloc[i]

            # Update portfolio value based on current price if holding position
            if position > 0:
                portfolio_value = cash + position * current_price
            else:
                portfolio_value = cash

            # --- Buy Signal ---
            if signal == 1 and cash > 0 and position == 0: # Only buy if not already in position
                investment_amount = portfolio_value * current_position_sizing_ratio
                shares_to_buy = int(investment_amount / current_price)
                cost = shares_to_buy * current_price

                if shares_to_buy > 0 and cash >= cost:
                    position += shares_to_buy
                    cash -= cost
                    entry_price = current_price
                    entry_date = current_date
                    entry_atr = current_atr # Store ATR at entry
                    self.trades.append({
                        'entry_date': entry_date,
                        'entry_price': entry_price,
                        'shares': shares_to_buy,
                        'exit_date': None,
                        'exit_price': None,
                        'profit': None,
                        'return_pct': None,
                        'exit_reason': None
                    })
                    self.logger.debug(f"{current_date}: BUY {shares_to_buy} shares at {current_price:.2f}, Cash: {cash:.2f}")

            # --- Exit Logic ---
            elif position > 0:
                exit_reason = None
                exit_price = current_price # Default exit price is current close

                # 1. Check Hold Days
                if entry_date is not None: # Ensure entry_date is set
                    days_held = (current_date - entry_date).days
                    if days_held >= hold_days:
                        exit_reason = f"Hold Days ({hold_days})"

                # 2. Check Stop Loss (Percentage) - Triggered if low hits stop price
                if exit_reason is None and stop_loss_pct > 0:
                    stop_loss_price = entry_price * (1 - stop_loss_pct)
                    if current_low <= stop_loss_price:
                        exit_reason = f"Stop Loss ({stop_loss_pct*100:.1f}%)"
                        exit_price = stop_loss_price # Exit at stop loss price

                # 3. Check Take Profit (Percentage) - Triggered if high hits profit target
                if exit_reason is None and take_profit_pct > 0:
                    take_profit_price = entry_price * (1 + take_profit_pct)
                    if current_high >= take_profit_price:
                        exit_reason = f"Take Profit ({take_profit_pct*100:.1f}%)"
                        exit_price = take_profit_price # Exit at take profit price

                # 4. Check ATR Stop Loss
                if exit_reason is None and use_atr_stop and not np.isnan(entry_atr):
                    atr_stop_price = entry_price - atr_multiplier * entry_atr
                    if current_low <= atr_stop_price:
                         exit_reason = f"ATR Stop ({atr_multiplier}x)"
                         exit_price = atr_stop_price # Exit at ATR stop price


                # --- Execute Sell if Exit Condition Met ---
                if exit_reason is not None:
                    proceeds = position * exit_price # Use determined exit_price
                    cash += proceeds
                    self.logger.debug(f"{current_date}: SELL {position} shares at {exit_price:.2f} due to {exit_reason}. Cash: {cash:.2f}")

                    # Update the last open trade record
                    open_trade_index = -1
                    for j in range(len(self.trades) - 1, -1, -1):
                        if self.trades[j]['exit_date'] is None:
                            open_trade_index = j
                            break

                    if open_trade_index != -1:
                        last_trade = self.trades[open_trade_index]
                        last_trade['exit_date'] = current_date
                        last_trade['exit_price'] = exit_price
                        last_trade['profit'] = (exit_price - last_trade['entry_price']) * last_trade['shares']
                        last_trade['return_pct'] = ((exit_price / last_trade['entry_price']) - 1) * 100 if last_trade['entry_price'] != 0 else 0
                        last_trade['exit_reason'] = exit_reason
                    else:
                        self.logger.warning(f"{current_date}: Exit condition '{exit_reason}' met, but no corresponding open trade found.")

                    position = 0
                    entry_price = 0
                    entry_date = None
                    entry_atr = np.nan

            # Update daily portfolio tracking
            self.df.iloc[i, self.df.columns.get_loc('position')] = position
            self.df.iloc[i, self.df.columns.get_loc('cash')] = cash
            # Recalculate portfolio value at the end of the day using the closing price
            self.df.iloc[i, self.df.columns.get_loc('portfolio_value')] = cash + position * self.df['close'].iloc[i]


        # If position still held at the end, close it using the last price
        if position > 0:
            last_date = self.df.index[-1]
            last_price = self.df['close'].iloc[-1]
            cash += position * last_price
            self.logger.debug(f"{last_date}: Close final position ({position} shares) at {last_price:.2f}, Cash: {cash:.2f}")

            open_trade_index = -1
            for j in range(len(self.trades) - 1, -1, -1):
                 if self.trades[j]['exit_date'] is None:
                     open_trade_index = j
                     break

            if open_trade_index != -1:
                 last_trade = self.trades[open_trade_index]
                 last_trade['exit_date'] = last_date
                 last_trade['exit_price'] = last_price
                 last_trade['profit'] = (last_price - last_trade['entry_price']) * last_trade['shares']
                 last_trade['return_pct'] = ((last_price / last_trade['entry_price']) - 1) * 100 if last_trade['entry_price'] != 0 else 0
                 last_trade['exit_reason'] = 'End of Backtest'
            else:
                 self.logger.warning(f"{last_date}: Position held at end, but no corresponding open trade found.")

            position = 0

        self.logger.info("Backtest simulation finished.")
        closed_trades = [t for t in self.trades if t['exit_date'] is not None]
        return closed_trades

    def calculate_performance(self, initial_capital=None):
        """
        Calculates performance metrics based on the backtest results.
        Uses self.initial_capital if argument is None.
        """
        current_initial_capital = initial_capital if initial_capital is not None else self.initial_capital

        # Use the completed trades list returned by run_backtest
        completed_trades = self.trades # Assumes self.trades contains completed trades after run_backtest

        # Default metrics structure
        default_metrics = {
            'total_trades': 0,
            'total_return_pct': 0.0,
            'win_rate_pct': 0.0,
            'profit_factor': 0.0,
            'max_drawdown_pct': 0.0,
            'sharpe_ratio': 0.0,
            'average_trade_return_pct': 0.0,
            'average_win_return_pct': 0.0,
            'average_loss_return_pct': 0.0,
            'gross_profit': 0.0,
            'gross_loss': 0.0,
            'final_portfolio_value': current_initial_capital,
            'error': None # Add error field
        }

        # Calculate Max Drawdown and Final Portfolio Value from DataFrame if possible
        max_drawdown_pct = 0.0
        final_portfolio_value = current_initial_capital
        if self.df is not None and not self.df.empty and 'portfolio_value' in self.df.columns:
            portfolio_values = self.df['portfolio_value']
            final_portfolio_value = portfolio_values.iloc[-1]
            rolling_max = portfolio_values.cummax()
            drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan)
            max_drawdown_pct = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0

            # Sharpe Ratio (Simplified)
            daily_returns = portfolio_values.pct_change().dropna()
            if len(daily_returns) > 1:
                 avg_daily_return = daily_returns.mean()
                 std_daily_return = daily_returns.std()
                 sharpe_ratio = (avg_daily_return / std_daily_return) * np.sqrt(252) if std_daily_return != 0 and not np.isnan(std_daily_return) else 0.0
                 default_metrics['sharpe_ratio'] = sharpe_ratio


        default_metrics['max_drawdown_pct'] = max_drawdown_pct
        default_metrics['final_portfolio_value'] = final_portfolio_value
        default_metrics['total_return_pct'] = ((final_portfolio_value / current_initial_capital) - 1) * 100 if current_initial_capital != 0 else 0.0


        if not completed_trades:
            self.logger.warning("No completed trades executed. Cannot calculate detailed performance.")
            default_metrics['error'] = "No completed trades"
            return default_metrics

        trades_df = pd.DataFrame(completed_trades)
        if 'profit' not in trades_df.columns or trades_df['profit'].isnull().all():
             self.logger.warning("Profit column missing or all NaN in trades. Cannot calculate trade-based performance.")
             default_metrics['error'] = "Profit calculation failed for trades"
             default_metrics['total_trades'] = len(trades_df) # Still report number of attempted trades
             return default_metrics


        # --- Calculate Trade-based Metrics ---
        total_trades = len(trades_df)
        winning_trades = trades_df[trades_df['profit'] > 0]
        losing_trades = trades_df[trades_df['profit'] <= 0]

        num_winning_trades = len(winning_trades)
        num_losing_trades = len(losing_trades)

        win_rate_pct = (num_winning_trades / total_trades) * 100 if total_trades > 0 else 0

        gross_profit = winning_trades['profit'].sum()
        gross_loss = abs(losing_trades['profit'].sum())

        profit_factor = gross_profit / gross_loss if gross_loss > 0 else np.inf

        average_trade_return_pct = trades_df['return_pct'].mean() if total_trades > 0 else 0
        average_win_return_pct = winning_trades['return_pct'].mean() if num_winning_trades > 0 else 0
        average_loss_return_pct = losing_trades['return_pct'].mean() if num_losing_trades > 0 else 0

        # Update the metrics dictionary
        performance = default_metrics.copy() # Start with defaults (includes drawdown, sharpe, final value)
        performance.update({
            'total_trades': total_trades,
            'win_rate_pct': win_rate_pct,
            'profit_factor': profit_factor,
            'average_trade_return_pct': average_trade_return_pct,
            'average_win_return_pct': average_win_return_pct,
            'average_loss_return_pct': average_loss_return_pct,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
        })
        performance.pop('error') # Remove error if calculation succeeded

        self.logger.info(f"Performance calculated: {performance}")
        return performance

    def run_single_stock_backtest(self, stock_code, period='1y', interval='1d',
                                  initial_capital=100000, position_sizing_ratio=0.1,
                                  params=None):
        """
        Convenience method to run the full V2 backtest process for a single stock.
        Uses parameters set in self.params, optionally updated by the 'params' argument.
        """
        self.logger.info(f"--- Starting V2 Backtest for {stock_code} ---")
        if params:
            self.set_parameters(params) # Apply specific parameters if provided

        # Use current instance parameters for capital and sizing if not overridden
        current_initial_capital = initial_capital if initial_capital is not None else self.initial_capital
        current_position_sizing_ratio = position_sizing_ratio if position_sizing_ratio is not None else self.position_sizing_ratio


        # 1. Fetch Data
        if not self.get_history_data(stock_code, period, interval):
            return {'stock_code': stock_code, 'error': 'Data fetching failed'}

        # 2. Calculate Indicators
        if not self.calculate_indicators():
             # Try to return partial results like drawdown if possible
             partial_perf = self.calculate_performance(initial_capital=current_initial_capital)
             return {'stock_code': stock_code, 'error': 'Indicator calculation failed', **partial_perf}


        # 3. Generate Signals
        if not self.generate_signals():
             partial_perf = self.calculate_performance(initial_capital=current_initial_capital)
             return {'stock_code': stock_code, 'error': 'Signal generation failed', **partial_perf}

        # 4. Run Backtest Simulation
        self.trades = self.run_backtest(initial_capital=current_initial_capital,
                                        position_sizing_ratio=current_position_sizing_ratio)

        if self.trades is None: # Check if run_backtest indicated failure
             partial_perf = self.calculate_performance(initial_capital=current_initial_capital)
             return {'stock_code': stock_code, 'error': 'Backtest execution failed', **partial_perf}


        # 5. Calculate Performance
        performance = self.calculate_performance(initial_capital=current_initial_capital)

        # Performance calculation itself returns a dict with potential 'error' key
        result = {'stock_code': stock_code, **performance}

        self.logger.info(f"--- Finished V2 Backtest for {stock_code} ---")
        return result


# Example Usage (for testing within this file)
if __name__ == '__main__':
    import sys
    import os
    import pandas as pd # Add pandas import here as it's used later
    import numpy as np # Add numpy import here as it's used later

    # The sys.path modification is now handled at the top of the file.
    # The top-level absolute imports should work when running the script directly.

    # The sys.path modification above allows the top-level absolute imports to work
    # when running the script directly. No need to re-import here.

    # Configure logger for testing
    try:
        Logger.setup_logging(log_level='DEBUG') # Use DEBUG for detailed logs during testing
    except AttributeError:
        import logging
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    test_logger = Logger.get_logger()

    # Instantiate the backtester
    backtester_v2 = LowVolBreakoutBacktesterV2(logger=test_logger, data_source='tushare', index_code='000300.SH')

    # Define parameters for a single run (using the new structure)
    stock_to_test = '600505.SH' # Example stock
    test_run_params = {
        'period': '2y',
        'interval': '1d',
        'initial_capital': 100000,
        'position_sizing_ratio': 0.15,
        # Override specific V2 parameters
        'params': {
            'hold_days': 12,
            'stop_loss_pct': 0.08,
            'take_profit_pct': 0.25,
            'factor_logic': {
                'prerequisites': ['PRE_VOLATILITY_LOW'], # Only use volatility prerequisite
                'core_signals': ['SIG_VOLUME_BREAK', 'SIG_PRICE_BREAK'],
                'confirmation_signals': ['CONF_MACD_SIGNAL', 'CONF_RSI_STRONG'],
                'min_confirmation_signals': 1, # Require at least 1 confirmation
                'use_optional_signals': False, # Don't use OPT_MA_CONFIRMATION
                'use_index_trend_filter': True
            },
            'pre_volatility_low': {
                'method': 'Option C', # Use long-term std dev
                'std_period': 30,
                'std_history': 252,
                'std_percentile': 0.20
            },
            'sig_volume_break': {
                'multiplier': 2.5 # Require higher volume multiplier
            },
            'sig_price_break': {
                 'use_hhv': True, 'hhv_period': 30, # Break 30-day high
                 'use_bbands_upper': False, # Don't use BBands break
                 'use_ma_break': True, 'ma_period': 50 # Break 50-day MA
            },
            'conf_macd_signal': {
                'method': 'Option B' # Use DIF > DEA and DIF > 0
            },
            'conf_rsi_strong': {
                'threshold': 55 # Lower RSI threshold slightly
            }
        }
    }

    # Run the backtest
    results = backtester_v2.run_single_stock_backtest(stock_to_test, **test_run_params)

    # Print results
    print("\n--- V2 Backtest Results ---")
    if results.get('error'):
        print(f"Error for {stock_to_test}: {results['error']}")
        # Print relevant part of dataframe if available
        if backtester_v2.df is not None:
             print("\n--- DataFrame Tail (for debugging) ---")
             # Select columns relevant to the V2 factors used in the test
             debug_cols = ['close', 'PRE_VOLATILITY_LOW', 'SIG_VOLUME_BREAK', 'SIG_PRICE_BREAK',
                           'CONF_MACD_SIGNAL', 'CONF_RSI_STRONG', 'INDEX_TREND_UP', 'signal', 'portfolio_value']
             print(backtester_v2.df[[col for col in debug_cols if col in backtester_v2.df.columns]].tail())

    else:
        result_series = pd.Series(results)
        float_cols = [k for k, v in results.items() if isinstance(v, (float, np.floating))]
        for col in float_cols:
            if col in result_series.index:
                 result_series[col] = f"{result_series[col]:.4f}"

        print(result_series)

        # Optionally print the dataframe with signals and portfolio value
        if backtester_v2.df is not None:
            print("\n--- DataFrame with V2 Signals (Last 30 days) ---")
            factor_cols_to_print = [
                'PRE_VOLATILITY_LOW', 'PRE_VOLUME_LOW', 'SIG_VOLUME_BREAK', 'SIG_PRICE_BREAK',
                'CONF_MACD_SIGNAL', 'CONF_RSI_STRONG', 'OPT_MA_CONFIRMATION', 'INDEX_TREND_UP'
            ]
            cols_exist = [col for col in factor_cols_to_print if col in backtester_v2.df.columns]
            print(backtester_v2.df[['close'] + cols_exist + ['signal', 'portfolio_value']].tail(30))


        # Optionally print trades
        if backtester_v2.trades:
            print("\n--- V2 Trades ---")
            trades_output_df = pd.DataFrame(backtester_v2.trades)
            # Format dates and floats
            trades_output_df['entry_date'] = pd.to_datetime(trades_output_df['entry_date']).dt.strftime('%Y-%m-%d')
            trades_output_df['exit_date'] = pd.to_datetime(trades_output_df['exit_date']).dt.strftime('%Y-%m-%d')
            trades_output_df['entry_price'] = trades_output_df['entry_price'].map('{:.2f}'.format)
            trades_output_df['exit_price'] = trades_output_df['exit_price'].map('{:.2f}'.format)
            trades_output_df['profit'] = trades_output_df['profit'].map('{:.2f}'.format)
            trades_output_df['return_pct'] = trades_output_df['return_pct'].map('{:.2f}%'.format)

            print(trades_output_df.to_string(index=False))
        else:
             print("\n--- No V2 Trades Executed ---")