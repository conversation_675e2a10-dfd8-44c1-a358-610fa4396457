import pandas as pd
import numpy as np
from src.data_providers.stock_data_provider import StockDataProviderFactory
from src.utils.logger import Logger
from src.analysis.technical_analyzer import TechnicalAnalyzer

class MA10PullbackBacktester:
    """MA10回踩支撑策略回测器"""
    def __init__(self, data_source='tushare'):
        self.data_source = data_source
        self.data_provider = StockDataProviderFactory.get_provider(data_source)
        self.df = None
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 默认参数
        self.strategy_params = {
            'lookback_days': 30,
            'check_last_n_days': 3,
            'upper_proximity': 1.0,
            'allowed_negative_proximity': -0.3,
            'approaching_proximity': 3.0,
            'min_prev_distance': 1.0,
            'min_ma10_slope': 0.0,
            'min_lower_shadow_ratio': 0.4,
            'min_volume_increase_ratio': 1.1,
            'volume_avg_period': 5
        }
        Logger.info(f"使用的MA10回踩策略参数: {self.strategy_params}")

    def get_history_data(self, stock_code, period='2y', interval='1d'):
        """
        获取指定股票的历史数据（默认2年日线）
        :param stock_code: 股票代码
        :param period: 周期，默认2年
        :param interval: 间隔，默认1天
        :return: DataFrame 或 None
        """
        try:
            # 根据 period 计算 start_date 和 end_date
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
            if period.endswith('y'):
                years = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=years)).strftime('%Y-%m-%d')
            elif period.endswith('m'):
                months = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(months=months)).strftime('%Y-%m-%d')
            elif period.endswith('d'):
                days = int(period[:-1])
                start_date = (pd.Timestamp.now() - pd.DateOffset(days=days)).strftime('%Y-%m-%d')
            else:
                # 默认两年
                start_date = (pd.Timestamp.now() - pd.DateOffset(years=2)).strftime('%Y-%m-%d')

            Logger.info(f"获取股票历史数据: {stock_code}, 开始日期: {start_date}, 结束日期: {end_date}, 来源: {self.data_source}")
            data = self.data_provider.get_stock_data_by_date(stock_code, start_date, end_date)
            if data is None or data.empty:
                Logger.error(f"未能获取到股票 {stock_code} 的历史数据")
                self.df = None
                return None

            # 标准化列名
            column_mapping = {
                'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume',
                '开盘': 'Open', '最高': 'High', '最低': 'Low', '收盘': 'Close', '成交量': 'Volume'
            }
            data.rename(columns=lambda c: column_mapping.get(c.lower(), c), inplace=True)

            # 检查必要列是否存在
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                Logger.error(f"获取的数据缺少必要列: {missing_cols}")
                self.df = None
                return None

            # 确保索引是 DatetimeIndex
            if not isinstance(data.index, pd.DatetimeIndex):
                try:
                    data.index = pd.to_datetime(data.index)
                except Exception as e:
                    Logger.error(f"无法将索引转换为日期时间格式: {e}")
                    date_col_candidates = ['date', '日期', '交易日期', 'time']
                    found_date_col = None
                    for col in date_col_candidates:
                        if col in data.columns:
                            try:
                                data[col] = pd.to_datetime(data[col])
                                data.set_index(col, inplace=True)
                                found_date_col = col
                                Logger.info(f"使用列 '{col}' 作为日期索引")
                                break
                            except Exception as date_conv_e:
                                Logger.warning(f"尝试将列 '{col}' 转为日期索引失败: {date_conv_e}")
                    if not found_date_col:
                        Logger.error("找不到合适的日期列或无法转换索引为日期时间格式。")
                        self.df = None
                        return None

            # 按日期升序排序
            data.sort_index(inplace=True)
            
            # 删除重复的索引
            if data.index.duplicated().any():
                Logger.warning(f"发现重复的日期索引，保留最后一个值")
                data = data[~data.index.duplicated(keep='last')]

            # 检查数据质量
            for col in required_cols:
                null_count = data[col].isnull().sum()
                if null_count > 0:
                    Logger.warning(f"列 {col} 包含 {null_count} 个空值")

            self.df = data
            Logger.info(f"成功获取并标准化 {len(data)} 行历史数据")
            return self.df
            
        except Exception as e:
            Logger.error(f"获取或处理历史数据失败: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            self.df = None
            return None

    def generate_signals(self, window_size=60):
        """
        生成交易信号
        
        Args:
            window_size (int): 信号生成的窗口大小，默认60天
            
        Returns:
            bool: 是否成功生成信号
        """
        if self.df is None or len(self.df) < window_size:
            Logger.error(f"数据不足，需要至少 {window_size} 天的数据")
            return False
            
        try:
            Logger.info("开始生成交易信号")
            Logger.info(f"使用窗口大小: {window_size} 天")
            
            # 初始化信号列
            self.df['Signal_Raw'] = 0
            
            # 设置技术分析器的数据
            self.technical_analyzer.set_data(self.df)
            
            # 生成信号
            total_days = len(self.df)
            signal_count = 0
            
            for i in range(window_size, total_days):
                try:
                    # 检查MA10回踩信号
                    meets_criteria, result = self.technical_analyzer.check_ma10_pullback(
                        lookback_days=self.strategy_params['lookback_days'],
                        check_last_n_days=self.strategy_params['check_last_n_days'],
                        upper_proximity=self.strategy_params['upper_proximity'],
                        allowed_negative_proximity=self.strategy_params['allowed_negative_proximity'],
                        approaching_proximity=self.strategy_params['approaching_proximity'],
                        min_prev_distance=self.strategy_params['min_prev_distance'],
                        min_ma10_slope=self.strategy_params['min_ma10_slope'],
                        min_lower_shadow_ratio=self.strategy_params['min_lower_shadow_ratio'],
                        min_volume_increase_ratio=self.strategy_params['min_volume_increase_ratio'],
                        volume_avg_period=self.strategy_params['volume_avg_period']
                    )
                    
                    if meets_criteria:
                        self.df.iloc[i, self.df.columns.get_loc('Signal_Raw')] = 1
                        signal_count += 1
                        Logger.debug(f"在 {self.df.index[i]} 检测到买入信号")
                except Exception as e:
                    Logger.warning(f"在 {self.df.index[i]} 检查信号时发生错误: {str(e)}")
                    continue
            
            # 创建实际信号列（向后移动一天，因为我们在收盘后才能确认信号）
            self.df['Signal'] = self.df['Signal_Raw'].shift(1)
            
            # 计算信号统计
            total_trading_days = len(self.df) - window_size
            signal_ratio = signal_count / total_trading_days * 100
            
            Logger.info(f"信号生成完成")
            Logger.info(f"总交易天数: {total_trading_days}")
            Logger.info(f"生成信号数量: {signal_count}")
            Logger.info(f"信号比率: {signal_ratio:.2f}%")
            
            # 显示最后10天的信号
            last_10_days = self.df.tail(10)[['Close', 'Signal']].copy()
            Logger.info("最后10天的信号情况:")
            Logger.info(last_10_days)
            
            return True
            
        except Exception as e:
            Logger.error(f"生成信号时发生错误: {str(e)}")
            return False

    def run_backtest(self, hold_days=5, initial_capital=100000, position_sizing_ratio=0.8, stop_loss_pct=0.05):
        """
        执行回测
        
        Args:
            hold_days (int): 持仓天数
            initial_capital (float): 初始资金
            position_sizing_ratio (float): 仓位比例
            stop_loss_pct (float): 止损比例
            
        Returns:
            bool: 回测是否成功完成
        """
        if self.df is None or 'Signal' not in self.df.columns:
            Logger.error("没有可用的信号数据")
            return False
            
        try:
            Logger.info("开始执行回测")
            Logger.info(f"初始资金: {initial_capital:,.2f}")
            Logger.info(f"持仓天数: {hold_days}")
            Logger.info(f"仓位比例: {position_sizing_ratio:.2%}")
            Logger.info(f"止损比例: {stop_loss_pct:.2%}")
            
            # 初始化交易记录
            self.trade_log = []
            self.current_capital = initial_capital
            self.equity_curve = pd.Series(index=self.df.index, dtype=float)
            self.equity_curve.iloc[0] = initial_capital
            
            # 交易状态变量
            in_position = False
            entry_price = 0
            entry_date = None
            current_shares = 0
            stop_loss_price = 0
            
            # 遍历每个交易日
            for i in range(1, len(self.df)):
                current_date = self.df.index[i]
                current_price = self.df['Close'].iloc[i]
                
                # 更新权益曲线
                if not in_position:
                    self.equity_curve.iloc[i] = self.current_capital
                else:
                    self.equity_curve.iloc[i] = self.current_capital + current_shares * current_price
                
                # 检查止损
                if in_position and current_price <= stop_loss_price:
                    # 执行止损
                    profit = (current_price - entry_price) * current_shares
                    self.current_capital += current_shares * current_price
                    
                    self.trade_log.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'shares': current_shares,
                        'profit': profit,
                        'hold_days': (current_date - entry_date).days,
                        'exit_type': 'stop_loss'
                    })
                    
                    Logger.info(f"触发止损: 日期={current_date}, 价格={current_price:.2f}, 收益={profit:.2f}")
                    in_position = False
                    continue
                
                # 检查是否需要平仓
                if in_position and (current_date - entry_date).days >= hold_days:
                    # 执行平仓
                    profit = (current_price - entry_price) * current_shares
                    self.current_capital += current_shares * current_price
                    
                    self.trade_log.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'shares': current_shares,
                        'profit': profit,
                        'hold_days': (current_date - entry_date).days,
                        'exit_type': 'time_exit'
                    })
                    
                    Logger.info(f"持仓时间到期平仓: 日期={current_date}, 价格={current_price:.2f}, 收益={profit:.2f}")
                    in_position = False
                    continue
                
                # 检查是否有新的买入信号
                if not in_position and self.df['Signal'].iloc[i] == 1:
                    # 计算可用资金和股数
                    available_capital = self.current_capital * position_sizing_ratio
                    current_shares = int(available_capital / current_price)
                    
                    if current_shares > 0:
                        entry_price = current_price
                        entry_date = current_date
                        stop_loss_price = entry_price * (1 - stop_loss_pct)
                        self.current_capital -= current_shares * entry_price
                        in_position = True
                        
                        Logger.info(f"执行买入: 日期={entry_date}, 价格={entry_price:.2f}, 数量={current_shares}")
            
            # 处理回测结束时的持仓
            if in_position:
                last_price = self.df['Close'].iloc[-1]
                profit = (last_price - entry_price) * current_shares
                self.current_capital += current_shares * last_price
                
                self.trade_log.append({
                    'entry_date': entry_date,
                    'exit_date': self.df.index[-1],
                    'entry_price': entry_price,
                    'exit_price': last_price,
                    'shares': current_shares,
                    'profit': profit,
                    'hold_days': (self.df.index[-1] - entry_date).days,
                    'exit_type': 'end_of_test'
                })
                
                Logger.info(f"回测结束平仓: 日期={self.df.index[-1]}, 价格={last_price:.2f}, 收益={profit:.2f}")
            
            # 转换交易记录为DataFrame
            if self.trade_log:
                self.trade_log = pd.DataFrame(self.trade_log)
                self.trade_log.set_index('entry_date', inplace=True)
                
                total_trades = len(self.trade_log)
                total_profit = self.trade_log['profit'].sum()
                win_rate = (self.trade_log['profit'] > 0).mean() * 100
                
                Logger.info("回测完成")
                Logger.info(f"总交易次数: {total_trades}")
                Logger.info(f"总收益: {total_profit:,.2f}")
                Logger.info(f"胜率: {win_rate:.2f}%")
                Logger.info(f"最终资金: {self.current_capital:,.2f}")
                
                return True
            else:
                Logger.warning("回测期间没有产生任何交易")
                return False
                
        except Exception as e:
            Logger.error(f"回测执行失败: {str(e)}")
            return False

    def calculate_equity_and_drawdown(self, initial_capital=100000):
        """
        计算权益曲线和最大回撤。

        Args:
            initial_capital (float): 初始资金。

        Returns:
            tuple: (权益曲线DataFrame, 最大回撤百分比)
        """
        try:
            if not hasattr(self, 'trade_log') or self.trade_log is None:
                Logger.error("没有交易日志可供分析")
                return None, None

            if not self.trade_log:  # 空列表
                Logger.warning("交易日志为空，无法计算权益曲线和回撤")
                return None, None

            # 按日期排序交易记录
            sorted_trades = sorted(self.trade_log, key=lambda x: x['exit_date'])

            # 获取回测的起止日期
            start_date = min(trade['entry_date'] for trade in self.trade_log)
            end_date = max(trade['exit_date'] for trade in self.trade_log)

            # 创建完整的日期范围的DataFrame
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # 创建每日盈亏DataFrame
            daily_pnl = pd.DataFrame(index=date_range, columns=['PnL'])
            daily_pnl['PnL'] = 0.0

            # 将每笔交易的盈亏记录到对应的日期
            for trade in sorted_trades:
                exit_date = trade['exit_date']
                if exit_date in daily_pnl.index:  # 确保日期存在
                    daily_pnl.loc[exit_date, 'PnL'] += trade['profit']

            # 计算累计权益
            daily_pnl['Equity'] = initial_capital + daily_pnl['PnL'].cumsum()

            # 计算历史最高点
            daily_pnl['Peak'] = daily_pnl['Equity'].expanding().max()
            
            # 计算回撤金额和百分比
            daily_pnl['Drawdown'] = daily_pnl['Peak'] - daily_pnl['Equity']
            daily_pnl['DrawdownPct'] = (daily_pnl['Drawdown'] / daily_pnl['Peak']) * 100

            # 计算最大回撤
            max_drawdown_pct = daily_pnl['DrawdownPct'].max()

            # 验证最终权益是否与 run_backtest 的结果一致
            if hasattr(self, 'current_capital'):
                final_equity = daily_pnl['Equity'].iloc[-1]
                if abs(final_equity - self.current_capital) > 0.01:  # 允许0.01的误差
                    Logger.warning(f"权益计算不一致！run_backtest最终权益: {self.current_capital:.2f}, "
                                 f"重建的最终权益: {final_equity:.2f}")

            Logger.info("\n=== 权益曲线和回撤分析 ===")
            Logger.info(f"初始资金: {initial_capital:,.2f}")
            Logger.info(f"最终权益: {daily_pnl['Equity'].iloc[-1]:,.2f}")
            Logger.info(f"最大回撤: {max_drawdown_pct:.2f}%")
            Logger.info(f"最大回撤发生于: {daily_pnl['DrawdownPct'].idxmax().strftime('%Y-%m-%d')}")

            return daily_pnl, max_drawdown_pct

        except Exception as e:
            Logger.error(f"计算权益曲线和回撤时发生错误: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
            return None, None 

    def calculate_performance(self, initial_capital=100000):
        """
        根据 self.trade_log 计算详细的回测绩效指标。

        Args:
            initial_capital (float): 用于计算总回报率和权益曲线的初始资金。

        Returns:
            dict: 包含绩效指标的字典，如果失败则返回 None。
        """
        if not hasattr(self, 'trade_log') or self.trade_log is None:
            Logger.error("没有交易日志可供分析，请先运行 run_backtest()")
            return None
        if not self.trade_log:  # 如果 trade_log 是空列表
            Logger.warning("交易日志为空，无法计算绩效指标。")
            return {}

        Logger.info("开始计算回测绩效指标...")

        trades = self.trade_log
        trade_df = pd.DataFrame(trades)  # 将列表转换为 DataFrame 更方便计算

        # --- 基本统计 ---
        total_trades = len(trade_df)
        winning_trades_df = trade_df[trade_df['profit'] > 0]
        losing_trades_df = trade_df[trade_df['profit'] <= 0]  # 包括盈亏为0的

        num_winning_trades = len(winning_trades_df)
        num_losing_trades = len(losing_trades_df)
        win_rate = (num_winning_trades / total_trades) * 100 if total_trades > 0 else 0

        # --- 盈亏统计 ---
        gross_profit = winning_trades_df['profit'].sum() if not winning_trades_df.empty else 0
        gross_loss = losing_trades_df['profit'].sum() if not losing_trades_df.empty else 0  # 亏损值为负数
        total_net_profit = gross_profit + gross_loss  # 即 sum(trade_df['profit'])

        profit_factor = abs(gross_profit / gross_loss) if gross_loss != 0 else float('inf')  # 无穷大表示没有亏损

        avg_profit_per_trade = total_net_profit / total_trades if total_trades > 0 else 0
        avg_profit_pct_per_trade = trade_df['profit'].mean() if total_trades > 0 else 0

        avg_win_amount = winning_trades_df['profit'].mean() if num_winning_trades > 0 else 0
        avg_win_pct = winning_trades_df['profit'].mean() if num_winning_trades > 0 else 0
        avg_loss_amount = losing_trades_df['profit'].mean() if num_losing_trades > 0 else 0  # 值为负
        avg_loss_pct = losing_trades_df['profit'].mean() if num_losing_trades > 0 else 0  # 值为负

        avg_win_loss_ratio = abs(avg_win_amount / avg_loss_amount) if avg_loss_amount != 0 else float('inf')

        # --- 持仓期 ---
        avg_hold_days = trade_df['hold_days'].mean() if total_trades > 0 else 0
        min_hold_days = trade_df['hold_days'].min() if total_trades > 0 else 0
        max_hold_days = trade_df['hold_days'].max() if total_trades > 0 else 0

        # --- 极值统计 ---
        max_win_pct = trade_df['profit'].max() if total_trades > 0 else 0
        max_loss_pct = trade_df['profit'].min() if total_trades > 0 else 0  # 值为负

        # --- 连续盈亏统计 ---
        profit_streaks = []
        loss_streaks = []
        current_streak = 0
        current_type = None  # 'win' or 'loss'

        for _, trade in trade_df.iterrows():
            if trade['profit'] > 0:
                if current_type == 'win':
                    current_streak += 1
                else:
                    if current_type == 'loss' and current_streak > 0:
                        loss_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'win'
            else:
                if current_type == 'loss':
                    current_streak += 1
                else:
                    if current_type == 'win' and current_streak > 0:
                        profit_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'loss'

        # 添加最后一个streak
        if current_type == 'win':
            profit_streaks.append(current_streak)
        elif current_type == 'loss':
            loss_streaks.append(current_streak)

        max_consecutive_wins = max(profit_streaks) if profit_streaks else 0
        max_consecutive_losses = max(loss_streaks) if loss_streaks else 0

        # --- 计算权益曲线和最大回撤 ---
        equity_df, max_drawdown_pct = self.calculate_equity_and_drawdown(initial_capital)
        if equity_df is not None:
            final_capital = equity_df['Equity'].iloc[-1]
            total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100
        else:
            final_capital = initial_capital + total_net_profit
            total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100
            max_drawdown_pct = None

        # --- 存储结果 ---
        performance_summary = {
            'total_trades': total_trades,
            'winning_trades': num_winning_trades,
            'losing_trades': num_losing_trades,
            'win_rate_pct': win_rate,
            'total_net_profit': total_net_profit,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,  # 值为负
            'profit_factor': profit_factor,
            'avg_profit_per_trade': avg_profit_per_trade,
            'avg_profit_pct_per_trade': avg_profit_pct_per_trade,
            'avg_win_amount': avg_win_amount,
            'avg_win_pct': avg_win_pct,
            'avg_loss_amount': avg_loss_amount,  # 值为负
            'avg_loss_pct': avg_loss_pct,  # 值为负
            'avg_win_loss_ratio': avg_win_loss_ratio,
            'avg_hold_days': avg_hold_days,
            'min_hold_days': min_hold_days,
            'max_hold_days': max_hold_days,
            'max_win_pct': max_win_pct,
            'max_loss_pct': max_loss_pct,  # 值为负
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'equity_curve': equity_df if equity_df is not None else None
        }

        self.performance_summary = performance_summary

        # --- 打印结果 ---
        Logger.info("\n=== 回测绩效指标 ===")
        
        # 交易统计
        Logger.info("\n--- 交易统计 ---")
        Logger.info(f"总交易次数: {total_trades}")
        Logger.info(f"盈利交易: {num_winning_trades}")
        Logger.info(f"亏损交易: {num_losing_trades}")
        Logger.info(f"胜率: {win_rate:.2f}%")
        
        # 盈亏统计
        Logger.info("\n--- 盈亏统计 ---")
        Logger.info(f"总净利润: {total_net_profit:,.2f}")
        Logger.info(f"总盈利: {gross_profit:,.2f}")
        Logger.info(f"总亏损: {gross_loss:,.2f}")
        Logger.info(f"盈利因子: {profit_factor:.2f}")
        Logger.info(f"平均每笔盈亏: {avg_profit_per_trade:,.2f}")
        Logger.info(f"平均每笔收益率: {avg_profit_pct_per_trade:.2f}%")
        
        # 盈亏比
        Logger.info("\n--- 盈亏比 ---")
        Logger.info(f"平均盈利金额: {avg_win_amount:,.2f} ({avg_win_pct:.2f}%)")
        Logger.info(f"平均亏损金额: {avg_loss_amount:,.2f} ({avg_loss_pct:.2f}%)")
        Logger.info(f"盈亏比: {avg_win_loss_ratio:.2f}")
        
        # 持仓统计
        Logger.info("\n--- 持仓统计 ---")
        Logger.info(f"平均持仓天数: {avg_hold_days:.1f}")
        Logger.info(f"最短持仓天数: {min_hold_days}")
        Logger.info(f"最长持仓天数: {max_hold_days}")
        
        # 极值统计
        Logger.info("\n--- 极值统计 ---")
        Logger.info(f"最大单笔盈利: {max_win_pct:.2f}%")
        Logger.info(f"最大单笔亏损: {max_loss_pct:.2f}%")
        Logger.info(f"最大连续盈利次数: {max_consecutive_wins}")
        Logger.info(f"最大连续亏损次数: {max_consecutive_losses}")
        
        # 资金统计
        Logger.info("\n--- 资金统计 ---")
        Logger.info(f"初始资金: {initial_capital:,.2f}")
        Logger.info(f"期末资金: {final_capital:,.2f}")
        Logger.info(f"总收益率: {total_return_pct:.2f}%")
        if max_drawdown_pct is not None:
            Logger.info(f"最大回撤: {max_drawdown_pct:.2f}%")

        return self.performance_summary

if __name__ == "__main__":
    # 测试用例：获取平安银行2年日线数据
    stock_code = '002194.SZ'  # 你可以替换为其他股票代码
    backtester = MA10PullbackBacktester(data_source='tushare')
    
    # 第一步：获取历史数据
    Logger.info("="*50)
    Logger.info("第一步：获取历史数据")
    df = backtester.get_history_data(stock_code)
    if df is None:
        Logger.error(f"获取 {stock_code} 的历史数据失败")
        exit(1)
    
    Logger.info(f"成功获取 {stock_code} 的历史数据，共 {len(df)} 行")
    Logger.info(f"原始数据列: {df.columns.tolist()}")
    
    # 第二步：生成历史信号
    Logger.info("="*50)
    Logger.info("第二步：生成历史信号")
    success = backtester.generate_signals(window_size=60)
    
    if not success:
        Logger.error("信号生成失败！")
        exit(1)
        
    Logger.info("信号生成成功！")
    
    # 显示信号统计信息
    total_days = len(backtester.df)
    signal_days = backtester.df['Signal'].sum()
    signal_ratio = signal_days / total_days * 100
    
    Logger.info("\n信号统计:")
    Logger.info(f"- 总交易日数: {total_days}")
    Logger.info(f"- 买入信号天数: {signal_days}")
    Logger.info(f"- 信号比例: {signal_ratio:.2f}%")
    
    # 第三步：执行回测
    Logger.info("="*50)
    Logger.info("第三步：执行回测")
    success = backtester.run_backtest(hold_days=5, initial_capital=100000, position_sizing_ratio=0.8, stop_loss_pct=0.05)
    
    if not success:
        Logger.error("回测执行失败！")
        exit(1)
    
    # 第四步：计算绩效指标
    Logger.info("="*50)
    Logger.info("第四步：计算绩效指标")
    performance = backtester.calculate_performance(initial_capital=100000)
    
    if performance is None:
        Logger.error("绩效指标计算失败！")
        exit(1)
    elif not performance:
        Logger.warning("没有交易记录，无法计算绩效指标。")
    
    # 显示详细的交易记录
    if backtester.trade_log:
        Logger.info("\n=== 详细交易记录 ===")
        for i, trade in backtester.trade_log.iterrows():
            Logger.info(f"\n交易 #{i}:")
            Logger.info(f"买入: {trade['entry_date']} @ {trade['entry_price']:.2f}")
            Logger.info(f"卖出: {trade['exit_date']} @ {trade['exit_price']:.2f}")
            Logger.info(f"持仓天数: {trade['hold_days']}")
            Logger.info(f"交易股数: {trade['shares']}")
            Logger.info(f"盈亏: {trade['profit']:.2f}")
            if trade['exit_type'] == 'stop_loss':
                Logger.info(f"备注: 触发止损")
            elif trade['exit_type'] == 'time_exit':
                Logger.info(f"备注: 持仓时间到期平仓")
            elif trade['exit_type'] == 'end_of_test':
                Logger.info(f"备注: 回测结束平仓")
    else:
        Logger.info("\n没有交易记录") 