"""
交易模拟器模块
"""
import pandas as pd
from datetime import datetime
from typing import Dict, Callable, List
from src.utils.logger import Logger

class TradingSimulator:
    """交易模拟器"""
    
    def __init__(self):
        """初始化交易模拟器"""
        self.trading_results = {}

    def simulate_trading(self, 
                        stock_signal_dates: Dict[str, List[str]], 
                        stock_data: pd.DataFrame,
                        get_market_data_func: Callable,
                        fixed_quantity: int = 100) -> bool:
        """
        模拟交易并计算盈亏
        
        Args:
            stock_signal_dates: Dict[str, List[str]] - 股票代码到其信号日期列表的映射
            stock_data: pd.DataFrame - 包含股票基本信息的DataFrame
            get_market_data_func: Callable - 获取市场数据的函数
            fixed_quantity: int - 固定买入数量，默认100股
            
        Returns:
            bool: 是否成功执行
        """
        try:
            if stock_data.empty:
                Logger.error("没有可用的数据进行交易模拟")
                return False
                
            Logger.section("开始模拟交易分析")
            Logger.info(f"- 固定买入数量: {fixed_quantity}股")
            
            # 清空之前的交易结果
            self.trading_results.clear()
            
            # 遍历每个股票
            for _, row in stock_data.iterrows():
                stock_code = row['股票代码']
                stock_name = row['股票名称']
                
                # 获取该股票的特定信号日期
                if stock_code not in stock_signal_dates:
                    continue
                    
                signal_dates = stock_signal_dates[stock_code]
                
                try:
                    # 获取该股票的历史数据
                    stock_df = get_market_data_func(
                        stock_code,
                        period="6mo",  # 使用更长的周期以确保包含所有信号日期
                        interval="1d",
                        data_source="sina"
                    )
                    
                    if stock_df is None or stock_df.empty:
                        Logger.warning(f"无法获取 {stock_code} {stock_name} 的历史数据，跳过")
                        continue
                        
                    # 确保日期索引
                    if not isinstance(stock_df.index, pd.DatetimeIndex):
                        stock_df.index = pd.to_datetime(stock_df.index)
                    
                    # 获取最新收盘价
                    latest_close = stock_df['Close'].iloc[-1]
                    latest_date = stock_df.index[-1]
                    
                    # 初始化该股票的交易记录
                    trades = []
                    
                    # 遍历该股票的信号日期
                    for signal_date in signal_dates:
                        try:
                            signal_date = pd.to_datetime(signal_date)
                            # 找到信号日期后的第一个交易日
                            next_day = None
                            for date in stock_df.index:
                                if date > signal_date:
                                    next_day = date
                                    break
                            
                            if next_day is None:
                                continue
                                
                            # 获取买入价格（次日开盘价）
                            buy_price = stock_df.loc[next_day, 'Open']
                            
                            # 计算该笔交易的盈亏
                            profit = (latest_close - buy_price) * fixed_quantity
                            profit_rate = (latest_close - buy_price) / buy_price * 100
                            
                            trades.append({
                                'signal_date': signal_date.strftime('%Y%m%d'),
                                'buy_date': next_day.strftime('%Y%m%d'),
                                'buy_price': buy_price,
                                'quantity': fixed_quantity,
                                'current_price': latest_close,
                                'profit': profit,
                                'profit_rate': profit_rate
                            })
                            
                        except Exception as e:
                            Logger.warning(f"处理 {stock_code} 的信号日期 {signal_date} 时出错: {str(e)}")
                            continue
                    
                    if trades:
                        # 计算该股票的总体盈亏
                        total_investment = sum(t['buy_price'] * t['quantity'] for t in trades)
                        total_profit = sum(t['profit'] for t in trades)
                        avg_profit_rate = sum(t['profit_rate'] for t in trades) / len(trades)
                        
                        self.trading_results[stock_code] = {
                            'stock_name': stock_name,
                            'trades': trades,
                            'total_investment': total_investment,
                            'total_profit': total_profit,
                            'avg_profit_rate': avg_profit_rate,
                            'latest_date': latest_date.strftime('%Y%m%d')
                        }
                        
                        # 打印该股票的交易结果
                        Logger.info(f"\n股票: {stock_code} {stock_name}")
                        Logger.info(f"截至日期: {latest_date.strftime('%Y%m%d')}")
                        Logger.info(f"交易次数: {len(trades)}")
                        Logger.info(f"总投资: {total_investment:.2f}")
                        Logger.info(f"总盈亏: {total_profit:.2f}")
                        Logger.info(f"平均收益率: {avg_profit_rate:.2f}%")
                        Logger.info("\n具体交易记录:")
                        for trade in trades:
                            Logger.info(f"- 信号日期: {trade['signal_date']}")
                            Logger.info(f"  买入日期: {trade['buy_date']}")
                            Logger.info(f"  买入价格: {trade['buy_price']:.3f}")
                            Logger.info(f"  当前价格: {trade['current_price']:.3f}")
                            Logger.info(f"  盈亏金额: {trade['profit']:.2f}")
                            Logger.info(f"  盈亏比例: {trade['profit_rate']:.2f}%")
                    
                except Exception as e:
                    Logger.error(f"处理股票 {stock_code} 时发生错误: {str(e)}")
                    continue
            
            # 打印总体统计
            if self.trading_results:
                total_trades = sum(len(result['trades']) for result in self.trading_results.values())
                total_investment = sum(result['total_investment'] for result in self.trading_results.values())
                total_profit = sum(result['total_profit'] for result in self.trading_results.values())
                avg_profit_rate = sum(result['avg_profit_rate'] for result in self.trading_results.values()) / len(self.trading_results)
                
                Logger.section("交易模拟总结")
                Logger.info(f"- 分析股票数: {len(self.trading_results)}")
                Logger.info(f"- 总交易次数: {total_trades}")
                Logger.info(f"- 总投资金额: {total_investment:.2f}")
                Logger.info(f"- 总盈亏金额: {total_profit:.2f}")
                Logger.info(f"- 平均收益率: {avg_profit_rate:.2f}%")
                
                return True
            else:
                Logger.info("\n没有产生任何交易信号")
                return False
                
        except Exception as e:
            Logger.error(f"执行交易模拟时发生错误: {str(e)}")
            return False 