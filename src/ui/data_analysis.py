import tkinter as tk
from tkinter import ttk
import sys
import os

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.ui.analysis import StockAnalyzer, DataAnalyzer

class DataAnalysis:
    def __init__(self, parent, stock_data):
        self.parent = parent
        self.stock_data = stock_data
        self.stock_analyzer = StockAnalyzer(parent, stock_data)
        self.data_analyzer = DataAnalyzer(parent, stock_data)
        
    def create_stock_analysis_window(self):
        """创建股票分析窗口"""
        self.stock_analyzer.create_analysis_window()
        
    def create_data_analysis_window(self):
        """创建数据分析窗口"""
        self.data_analyzer.create_analysis_window()
        
    def show_analysis_menu(self):
        """显示分析菜单"""
        # 创建菜单
        menu = tk.Menu(self.parent, tearoff=0)
        
        # 添加分析选项
        menu.add_command(label="股票涨幅分析", command=self.create_stock_analysis_window)
        menu.add_command(label="数据分析", command=self.create_data_analysis_window)
        
        return menu 