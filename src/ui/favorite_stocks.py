import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
import pandas as pd
import sys

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.data_providers.stock_data_provider import StockDataProviderFactory

class FavoriteStockManager:
    """自选股票管理器类，负责保存和加载自选股票数据"""
    
    def __init__(self):
        # 获取数据存储路径
        self.data_dir = os.path.join(os.path.expanduser("~"), ".stock_app")
        self.data_file = os.path.join(self.data_dir, "favorite_stocks.json")
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 初始化数据
        self.favorite_stocks = self.load_favorites()
        
        # 初始化数据提供者
        self.data_provider = StockDataProviderFactory.get_provider("yahoo")
    
    def load_favorites(self):
        """从本地文件加载自选股票数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载自选股数据时出错: {str(e)}")
                return {}
        return {}
    
    def save_favorites(self):
        """保存自选股票数据到本地文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.favorite_stocks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存自选股数据时出错: {str(e)}")
    
    def add_stock(self, code, name=None, cost=0, shares=0, note=""):
        """添加股票到自选列表"""
        # 格式化股票代码
        formatted_code = self.data_provider.format_stock_code(code)
        
        # 如果没有提供名称，尝试获取
        if name is None:
            try:
                stock_info = self.data_provider.get_stock_info(code)
                if 'shortName' in stock_info:
                    name = stock_info['shortName']
                else:
                    name = formatted_code
            except:
                name = formatted_code
        
        # 添加或更新股票信息
        self.favorite_stocks[formatted_code] = {
            'code': formatted_code,
            'name': name,
            'cost': float(cost),
            'shares': float(shares),
            'note': note
        }
        
        # 保存更新后的数据
        self.save_favorites()
        return True
    
    def remove_stock(self, code):
        """从自选列表中移除股票"""
        formatted_code = self.data_provider.format_stock_code(code)
        if formatted_code in self.favorite_stocks:
            del self.favorite_stocks[formatted_code]
            self.save_favorites()
            return True
        return False
    
    def get_stock(self, code):
        """获取指定股票的信息"""
        formatted_code = self.data_provider.format_stock_code(code)
        return self.favorite_stocks.get(formatted_code)
    
    def get_all_stocks(self):
        """获取所有自选股票"""
        return self.favorite_stocks


class FavoriteStocksApp:
    """自选股票管理应用界面"""
    
    def __init__(self, root, callback=None):
        """
        初始化自选股票管理界面
        
        参数:
            root: Tkinter 根窗口
            callback: 可选的回调函数，当自选股票列表变化时被调用
        """
        self.root = root
        self.callback = callback
        self.root.title("自选股票管理")
        self.root.geometry("700x500")
        
        # 创建股票管理器
        self.manager = FavoriteStockManager()
        
        # 设置UI
        self.setup_ui()
        
        # 加载自选股票数据
        self.load_stocks()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题标签
        title_label = ttk.Label(self.main_frame, text="自选股票管理", font=("Helvetica", 14, "bold"))
        title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 创建工具栏
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 添加按钮
        self.add_button = ttk.Button(toolbar_frame, text="添加股票", command=self.show_add_dialog, width=12)
        self.add_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 编辑按钮
        self.edit_button = ttk.Button(toolbar_frame, text="编辑", command=self.edit_selected, width=8)
        self.edit_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 删除按钮
        self.delete_button = ttk.Button(toolbar_frame, text="删除", command=self.delete_selected, width=8)
        self.delete_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 查看K线图按钮
        self.view_kline_button = ttk.Button(toolbar_frame, text="查看K线图", command=self.view_kline, width=12)
        self.view_kline_button.pack(side=tk.LEFT)
        
        # 创建表格
        self.create_table()
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_table(self):
        """创建股票列表表格"""
        # 表格框架
        self.table_frame = ttk.Frame(self.main_frame)
        self.table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        columns = ("code", "name", "cost", "shares", "value", "note")
        self.tree = ttk.Treeview(self.table_frame, columns=columns, show="headings")
        
        # 设置列标题
        self.tree.heading("code", text="股票代码")
        self.tree.heading("name", text="股票名称")
        self.tree.heading("cost", text="成本价")
        self.tree.heading("shares", text="持仓数量")
        self.tree.heading("value", text="持仓市值")
        self.tree.heading("note", text="备注")
        
        # 设置列宽
        self.tree.column("code", width=100, anchor=tk.CENTER)
        self.tree.column("name", width=150, anchor=tk.W)
        self.tree.column("cost", width=80, anchor=tk.CENTER)
        self.tree.column("shares", width=80, anchor=tk.CENTER)
        self.tree.column("value", width=100, anchor=tk.CENTER)
        self.tree.column("note", width=150, anchor=tk.W)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(fill=tk.BOTH, expand=True)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", lambda e: self.view_kline())
        
        # 设置交替行颜色
        self.tree.tag_configure('odd', background='#f0f0f0')
        self.tree.tag_configure('even', background='white')
    
    def load_stocks(self):
        """加载股票数据到表格"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取所有自选股票
        stocks = self.manager.get_all_stocks()
        
        # 填充表格
        for i, (code, stock) in enumerate(stocks.items()):
            tag = 'odd' if i % 2 else 'even'
            cost = stock.get('cost', 0)
            shares = stock.get('shares', 0)
            value = cost * shares if cost and shares else 0
            
            self.tree.insert("", "end", values=(
                code,
                stock.get('name', ''),
                f"{cost:.2f}",
                f"{shares:.0f}",
                f"{value:.2f}",
                stock.get('note', '')
            ), tags=(tag,))
        
        # 更新状态栏
        self.status_var.set(f"共有 {len(stocks)} 只自选股票")
    
    def show_add_dialog(self):
        """显示添加股票对话框"""
        self.edit_dialog = tk.Toplevel(self.root)
        self.edit_dialog.title("添加自选股票")
        self.edit_dialog.geometry("400x300")
        self.edit_dialog.transient(self.root)
        self.edit_dialog.grab_set()  # 模态对话框
        
        # 创建表单
        form_frame = ttk.Frame(self.edit_dialog, padding="20")
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # 股票代码
        ttk.Label(form_frame, text="股票代码:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.code_var = tk.StringVar()
        code_entry = ttk.Entry(form_frame, textvariable=self.code_var, width=30)
        code_entry.grid(row=0, column=1, sticky=tk.W, pady=(0, 10))
        code_entry.focus_set()  # 默认焦点
        
        # 股票名称 (可选)
        ttk.Label(form_frame, text="股票名称:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        self.name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.name_var, width=30).grid(row=1, column=1, sticky=tk.W, pady=(0, 10))
        ttk.Label(form_frame, text="(可选)").grid(row=1, column=2, sticky=tk.W, pady=(0, 10))
        
        # 成本价
        ttk.Label(form_frame, text="成本价:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.cost_var = tk.StringVar(value="0.00")
        ttk.Entry(form_frame, textvariable=self.cost_var, width=30).grid(row=2, column=1, sticky=tk.W, pady=(0, 10))
        
        # 持仓数量
        ttk.Label(form_frame, text="持仓数量:").grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        self.shares_var = tk.StringVar(value="0")
        ttk.Entry(form_frame, textvariable=self.shares_var, width=30).grid(row=3, column=1, sticky=tk.W, pady=(0, 10))
        
        # 备注
        ttk.Label(form_frame, text="备注:").grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        self.note_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.note_var, width=30).grid(row=4, column=1, sticky=tk.W, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(20, 0))
        
        # 保存按钮
        save_button = ttk.Button(button_frame, text="保存", command=self.save_stock, width=10)
        save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=self.edit_dialog.destroy, width=10)
        cancel_button.pack(side=tk.LEFT)
        
        # 绑定回车键
        self.edit_dialog.bind("<Return>", lambda e: self.save_stock())
        
        # 当前编辑的股票代码（用于区分添加和编辑）
        self.editing_code = None
    
    def edit_selected(self):
        """编辑选中的股票"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要编辑的股票")
            return
        
        # 获取选中行的股票代码
        selected_item = selected[0]
        code = self.tree.item(selected_item, "values")[0]
        
        # 获取股票信息
        stock = self.manager.get_stock(code)
        if not stock:
            messagebox.showerror("错误", f"找不到股票 {code} 的信息")
            return
        
        # 显示编辑对话框
        self.show_add_dialog()
        
        # 填充现有数据
        self.editing_code = code
        self.code_var.set(code)
        self.name_var.set(stock.get('name', ''))
        self.cost_var.set(str(stock.get('cost', 0)))
        self.shares_var.set(str(stock.get('shares', 0)))
        self.note_var.set(stock.get('note', ''))
        
        # 修改对话框标题
        self.edit_dialog.title(f"编辑自选股票 - {code}")
    
    def save_stock(self):
        """保存股票信息"""
        code = self.code_var.get().strip()
        if not code:
            messagebox.showerror("错误", "股票代码不能为空")
            return
        
        # 获取其他信息
        name = self.name_var.get().strip()
        name = name if name else None  # 如果为空，设为None以使用默认名称
        
        try:
            cost = float(self.cost_var.get().strip() or "0")
            shares = float(self.shares_var.get().strip() or "0")
        except ValueError:
            messagebox.showerror("错误", "成本价和持仓数量必须是数字")
            return
        
        note = self.note_var.get().strip()
        
        # 保存股票信息
        if self.manager.add_stock(code, name, cost, shares, note):
            self.edit_dialog.destroy()
            self.load_stocks()  # 刷新列表
            
            # 如果设置了回调函数，调用它
            if self.callback:
                self.callback()
            
            # 更新状态栏
            stocks = self.manager.get_all_stocks()
            self.status_var.set(f"股票 {code} 已{'更新' if self.editing_code else '添加'} | 共有 {len(stocks)} 只自选股票")
        else:
            messagebox.showerror("错误", f"无法{'更新' if self.editing_code else '添加'}股票 {code}")
    
    def delete_selected(self):
        """删除选中的股票"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要删除的股票")
            return
        
        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除选中的股票吗？"):
            return
        
        # 删除选中的股票
        for item in selected:
            code = self.tree.item(item, "values")[0]
            if self.manager.remove_stock(code):
                self.tree.delete(item)
        
        # 如果设置了回调函数，调用它
        if self.callback:
            self.callback()
            
        # 更新状态栏
        stocks = self.manager.get_all_stocks()
        self.status_var.set(f"已删除选中股票 | 共有 {len(stocks)} 只自选股票")
    
    def view_kline(self):
        """查看选中股票的K线图"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要查看的股票")
            return
        
        # 获取选中的股票代码
        selected_item = selected[0]
        code = self.tree.item(selected_item, "values")[0]
        
        # 获取股票信息
        stock = self.manager.get_stock(code)
        if not stock:
            messagebox.showerror("错误", f"找不到股票 {code} 的信息")
            return
            
        try:
            # 获取当前脚本的目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            kline_script = os.path.join(script_dir, "stock_kline.py")
            
            # 移除可能存在的市场后缀
            code = code.upper().replace('.SZ', '').replace('.SH', '').replace('.SS', '').replace('.HK', '')
            
            # 启动K线图查看器进程，传递股票代码和成本价
            python_executable = sys.executable
            
            # 构建命令行参数，使用特定格式来传递成本价
            cmd = [
                python_executable, 
                kline_script, 
                code,
                "--cost", str(stock.get('cost', 0))
            ]
            
            import subprocess
            subprocess.Popen(cmd)
            
            self.status_var.set(f"已打开 {code} 的K线图查看器")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开K线图查看器: {str(e)}")


# 如果直接运行此文件，则创建应用窗口
if __name__ == "__main__":
    root = tk.Tk()
    app = FavoriteStocksApp(root)
    root.mainloop() 