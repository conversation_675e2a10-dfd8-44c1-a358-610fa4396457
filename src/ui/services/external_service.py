import os
import sys
import subprocess
import json

class ExternalServiceManager:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.python_executable = sys.executable

    def open_kline_viewer(self, symbol=None, time_markers=None):
        """打开K线图查看器"""
        try:
            kline_dir = os.path.join(self.script_dir, "stock_kline")
            kline_main = os.path.join(kline_dir, "__main__.py")
            
            cmd = [self.python_executable, "-m", "src.ui.stock_kline"]
            if symbol:
                cmd.append(symbol)
            
            # 设置环境变量
            env = os.environ.copy()
            if time_markers:
                markers_json = json.dumps(time_markers)
                env['TIME_MARKERS'] = markers_json
            
            process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            return True, None
        except Exception as e:
            return False, str(e)

    def open_multi_kline_viewer(self, symbols=None):
        """打开多股票K线图查看器"""
        try:
            kline_script = os.path.join(self.script_dir, "multi_stock_kline.py")
            
            cmd = [self.python_executable, kline_script]
            if symbols:
                cmd.extend(symbols)
            
            subprocess.Popen(cmd)
            return True, None
        except Exception as e:
            return False, str(e)

    def open_favorites_manager(self):
        """打开自选股票管理界面"""
        try:
            favorites_script = os.path.join(self.script_dir, "favorite_stocks.py")
            
            subprocess.Popen([
                self.python_executable,
                favorites_script
            ])
            return True, None
        except Exception as e:
            return False, str(e)

    def open_sector_analysis(self):
        """打开板块分析工具"""
        try:
            sector_script = os.path.join(self.script_dir, "sector_analysis.py")
            
            subprocess.Popen([
                self.python_executable,
                sector_script
            ])
            return True, None
        except Exception as e:
            return False, str(e)

    def add_to_favorites(self, stock_code, stock_name=None):
        """添加股票到自选列表"""
        try:
            from ..favorite_stocks import FavoriteStockManager
            manager = FavoriteStockManager()
            
            # 格式化股票代码
            stock_code = self._format_stock_code(stock_code)
            
            # 添加到自选列表
            success = manager.add_stock(stock_code, stock_name)
            return success, None if success else "添加失败"
        except Exception as e:
            return False, str(e)

    @staticmethod
    def _format_stock_code(code):
        """格式化股票代码"""
        if isinstance(code, (int, float)):
            code = str(int(code))
        elif not isinstance(code, str):
            code = str(code)
            
        # 移除可能存在的市场后缀
        code = code.upper().replace('.SZ', '').replace('.SH', '').replace('.SS', '').replace('.HK', '')
            
        # 处理A股代码（6位数字）
        if code.isdigit():
            if len(code) < 6:
                code = code.zfill(6)
            
        return code 