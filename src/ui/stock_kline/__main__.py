"""
股票K线图应用程序入口
"""
import argparse
import tkinter as tk
import sys
import os
import json
import base64

# 确保可以导入父级包
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))))

from src.ui.stock_kline.app import StockKLineApp

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="股票K线图查看器")
    parser.add_argument("symbol", nargs="?", help="股票代码")
    parser.add_argument("--cost", type=float, help="成本价格")
    args = parser.parse_args()

    # 检查命令行参数
    initial_symbol = args.symbol if args.symbol else None
    cost_price = args.cost if args.cost else None
    
    print(f"[KLine] 接收到的股票代码: {initial_symbol}")
    print(f"[KLine] 接收到的成本价: {cost_price}")
    
    # 从环境变量读取时间标记
    time_markers = None
    if 'TIME_MARKERS' in os.environ:
        try:
            markers_json = os.environ['TIME_MARKERS']
            time_markers = json.loads(markers_json)
            print(f"[KLine] 接收到的时间标记: {markers_json}")
        except Exception as e:
            print(f"[KLine] 警告：解析时间标记时出错 - {str(e)}")
    else:
        print("[KLine] 未接收到时间标记")

    # 创建主窗口
    root = tk.Tk()
    app = StockKLineApp(root, initial_symbol=initial_symbol, cost_price=cost_price, time_markers=time_markers)
    root.mainloop()

if __name__ == "__main__":
    main() 