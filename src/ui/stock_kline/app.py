"""
股票K线图应用主类
"""
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
import pandas as pd
import sys
import os
import re
import json
from datetime import datetime, timedelta

# 导入自定义模块
from src.data_providers.stock_data_provider import StockDataProviderFactory
from src.ui.components.tooltips import create_tooltip
from src.ui.indicators.ichimoku import calculate_ichimoku, plot_ichimoku
from src.ui.indicators.macd import calculate_macd, detect_macd_divergence
from src.technical_indicators.resistance import (
    calculate_fibonacci_levels, 
    calculate_resistance_zones, 
    plot_resistance_zones_with_fibonacci
)
from src.chart_utils.candlestick import (
    plot_candlestick, 
    add_candlestick_hover, 
    plot_volume, 
    plot_cost_line
)
from src.config.config_manager import config_manager
from src.analysis.technical_analyzer import TechnicalAnalyzer

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei',
                                   'Microsoft YaHei', 'WenQuanYi Micro Hei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题


class StockKLineApp:
    def __init__(self, root, initial_symbol=None, initial_market=None, cost_price=None, time_markers=None):
        self.root = root
        self.root.title("股票K线图查看器")
        self.root.geometry("900x600")

        # 初始化数据属性
        self.data = pd.DataFrame()
        
        # 保存成本价
        self.cost_price = cost_price
        
        # 保存时间标记
        self.time_markers = time_markers or []
        
        # 加载配置
        self.ichimoku_params = config_manager.get_config('ichimoku')
        self.indicators_config = config_manager.get_config('indicators')
        
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 创建UI组件
        self._create_ui()
        
        # 初始化数据提供者
        self.data_provider = None
        
        # 绑定数据源变更事件
        self.data_source_combo.bind("<<ComboboxSelected>>", self.on_data_source_change)
        
        # 绑定时间范围和间隔变更事件
        self.period_combo.bind("<<ComboboxSelected>>", self.on_period_interval_change)
        self.interval_combo.bind("<<ComboboxSelected>>", self.on_period_interval_change)
        
        # 绑定成本价变更事件
        self.cost_price_entry.bind("<Return>", lambda event: self.on_indicator_change())
        self.cost_price_entry.bind("<FocusOut>", lambda event: self.on_indicator_change())
        
        # 设置初始股票代码
        if initial_symbol:
            self.stock_code_var.set(initial_symbol)
        
        # 初始化数据提供者，但不自动获取数据
        try:
            self.data_provider = StockDataProviderFactory.get_provider('sina')
        except Exception as e:
            messagebox.showerror("错误", f"初始化数据提供者失败: {str(e)}")
            return
            
        # 如果提供了初始股票代码，使用after方法延迟执行查询
        if initial_symbol:
            # 延迟1秒执行，确保窗口已完全初始化
            self.root.after(1000, self.fetch_and_plot)
        
    def _create_ui(self):
        """创建界面UI元素"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部控制区域
        self._create_control_frame()

        # 创建指标选择区域
        self._create_indicators_frame()

        # 创建图表区域
        self._create_chart_frame()

        # 创建状态栏
        self._create_status_bar()
            
    def _create_control_frame(self):
        """创建控制区域"""
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # 股票代码输入
        ttk.Label(self.control_frame, text="股票代码:").pack(
            side=tk.LEFT, padx=(0, 5))
        self.stock_code_var = tk.StringVar(value="AAPL")
        self.stock_code_entry = ttk.Entry(
            self.control_frame, textvariable=self.stock_code_var, width=10)
        self.stock_code_entry.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.stock_code_entry, "输入股票代码，如：600000或AAPL", self.root)

        # 数据源选择
        ttk.Label(self.control_frame, text="数据源:").pack(
            side=tk.LEFT, padx=(0, 5))
        self.data_source_var = tk.StringVar(value="sina")
        self.data_source_combo = ttk.Combobox(self.control_frame, textvariable=self.data_source_var,
                                             values=["local", "yahoo", "sina", "tushare"],
                                             state="readonly", width=6)
        self.data_source_combo.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.data_source_combo, "选择数据源: 本地数据、Yahoo Finance、新浪财经或 Tushare", self.root)

        # 时间范围选择
        ttk.Label(self.control_frame, text="时间范围:").pack(
            side=tk.LEFT, padx=(0, 5))
        self.period_var = tk.StringVar(value="3mo")
        self.period_combo = ttk.Combobox(self.control_frame, textvariable=self.period_var,
                                         values=[
                                             "1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "max"],
                                         state="readonly", width=5)
        self.period_combo.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.period_combo, "选择查询的时间范围", self.root)

        # 间隔选择
        ttk.Label(self.control_frame, text="间隔:").pack(
            side=tk.LEFT, padx=(0, 5))
        self.interval_var = tk.StringVar(value="1d")
        self.interval_combo = ttk.Combobox(self.control_frame, textvariable=self.interval_var,
                                           values=["1m", "2m", "5m", "15m", "30m", "60m",
                                                   "90m", "1h", "1d", "5d", "1wk", "1mo", "3mo"],
                                           state="readonly", width=5)
        self.interval_combo.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.interval_combo, "选择K线图的时间间隔", self.root)
        
        # 绑定查询事件
        self.stock_code_entry.bind("<Return>", lambda event: self.fetch_and_plot())
        self.stock_code_entry.bind("<FocusOut>", lambda event: self.fetch_and_plot())
        
    def _create_indicators_frame(self):
        """创建技术指标选择区域"""
        self.indicators_frame = ttk.Frame(self.main_frame)
        self.indicators_frame.pack(fill=tk.X, pady=(0, 10))

        # 添加均线选择
        ttk.Label(self.indicators_frame, text="均线:").pack(
            side=tk.LEFT, padx=(0, 5))

        # 重构MA选项为四种周期
        self.ma_groups = {
            "短期": {"name": "短期", "enabled": self.indicators_config['ma_groups'].get('短期', True), "lines": [
                {"name": "MA5", "period": 5, "color": "blue"},
                {"name": "MA10", "period": 10, "color": "orange"},
                {"name": "MA20", "period": 20, "color": "purple"}
            ]},
            "中期": {"name": "中期", "enabled": self.indicators_config['ma_groups'].get('中期', False), "lines": [
                {"name": "MA55", "period": 55, "color": "red"},
                {"name": "MA89", "period": 89, "color": "darkblue"},
                {"name": "MA144", "period": 144, "color": "darkred"}
            ]},
            "长期": {"name": "长期", "enabled": self.indicators_config['ma_groups'].get('长期', False), "lines": [
                {"name": "MA233", "period": 233, "color": "green"},
                {"name": "MA377", "period": 377, "color": "teal"},
                {"name": "MA453", "period": 453, "color": "darkgreen"}
            ]},
            "最长": {"name": "最长", "enabled": self.indicators_config['ma_groups'].get('最长', False), "lines": [
                {"name": "MA610", "period": 610, "color": "purple"},
                {"name": "MA853", "period": 853, "color": "indigo"},
                {"name": "MA987", "period": 987, "color": "navy"}
            ]}
        }

        # 创建均线组复选框变量
        self.ma_group_vars = {}

        # 为每种均线组创建复选框
        for group_key, group in self.ma_groups.items():
            var = tk.BooleanVar(value=group["enabled"])
            self.ma_group_vars[group_key] = var
            cb = ttk.Checkbutton(self.indicators_frame, text=group["name"],
                                variable=var, command=self.on_ma_change)
            cb.pack(side=tk.LEFT, padx=(0, 5))
            tooltip_text = f"显示{group_key}均线: " + ", ".join([f"{line['name']}({line['period']}日)" for line in group["lines"]])
            create_tooltip(cb, tooltip_text, self.root)
        
        # 添加分隔符
        ttk.Label(self.indicators_frame, text="|").pack(side=tk.LEFT, padx=(5, 5))

        # 显示阻力区选项
        self.show_resistance_var = tk.BooleanVar(value=self.indicators_config.get('show_resistance', True))
        self.show_resistance_check = ttk.Checkbutton(self.indicators_frame, text="显示阻力区",
                                                     variable=self.show_resistance_var,
                                                     command=self.on_indicator_change)
        self.show_resistance_check.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.show_resistance_check, 
                     "是否在图表上显示阻力区，考虑高成交量、心理价位和触及次数等因素", self.root)
        
        # 添加一目均衡表选项和参数设置
        self.show_ichimoku_var = tk.BooleanVar(value=self.indicators_config.get('show_ichimoku', False))
        self.show_ichimoku_check = ttk.Checkbutton(self.indicators_frame, text="一目均衡表",
                                                  variable=self.show_ichimoku_var,
                                                  command=self.on_indicator_change)
        self.show_ichimoku_check.pack(side=tk.LEFT, padx=(0, 5))
        create_tooltip(self.show_ichimoku_check, "是否显示一目均衡表指标", self.root)

        # 添加一目均衡表参数设置按钮
        self.ichimoku_settings_btn = ttk.Button(self.indicators_frame, text="⚙️",
                                              width=3, command=self.show_ichimoku_settings)
        self.ichimoku_settings_btn.pack(side=tk.LEFT, padx=(0, 5))
        create_tooltip(self.ichimoku_settings_btn, "设置一目均衡表参数", self.root)
        
        # 添加一目均衡表得分查看按钮
        self.ichimoku_score_btn = ttk.Button(self.indicators_frame, text="📊",
                                            width=3, command=self.show_ichimoku_score)
        self.ichimoku_score_btn.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.ichimoku_score_btn, "查看一目均衡表详细得分", self.root)

        # 添加分隔符
        ttk.Label(self.indicators_frame, text="|").pack(side=tk.LEFT, padx=(5, 5))

        # 成本价输入框
        ttk.Label(self.indicators_frame, text="成本价:").pack(
            side=tk.LEFT, padx=(0, 5))
        self.cost_price_var = tk.StringVar(value=str(self.cost_price) if self.cost_price is not None else "")
        self.cost_price_entry = ttk.Entry(
            self.indicators_frame, textvariable=self.cost_price_var, width=7)
        self.cost_price_entry.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.cost_price_entry, "输入成本价，用于在图表中显示成本线", self.root)

        # 显示成本线选项
        self.show_cost_line_var = tk.BooleanVar(value=self.indicators_config.get('show_cost_line', False))
        self.show_cost_line_check = ttk.Checkbutton(self.indicators_frame, text="显示成本线",
                                                  variable=self.show_cost_line_var,
                                                  command=self.on_indicator_change)
        self.show_cost_line_check.pack(side=tk.LEFT, padx=(0, 10))
        create_tooltip(self.show_cost_line_check, "是否在图表上显示成本线", self.root)
        
    def _create_chart_frame(self):
        """创建图表区域"""
        self.chart_frame = ttk.Frame(self.main_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)

        # 初始化图表
        self.fig = Figure(figsize=(9, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加导航工具栏
        self.toolbar = NavigationToolbar2Tk(self.canvas, self.chart_frame)
        self.toolbar.update()
        
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        self.status_bar = ttk.Label(
            self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def on_data_source_change(self, event=None):
        """当数据源变更时更新数据提供者"""
        data_source = self.data_source_var.get()
        try:
            # 如果是切换到相同的数据源，不做处理
            if self.data_provider and isinstance(self.data_provider, StockDataProviderFactory.get_provider(data_source).__class__):
                return
                
            self.data_provider = StockDataProviderFactory.get_provider(data_source)
            source_name_map = {
                'local': '本地',
                'yahoo': 'Yahoo Finance',
                'sina': '新浪财经',
                'tushare': 'Tushare'
            }
            self.status_var.set(f"已切换到{source_name_map.get(data_source, data_source)}数据源")
            
            # 如果有股票代码，延迟执行数据刷新
            stock_code = self.stock_code_var.get().strip()
            if stock_code:
                self.root.after(100, self.fetch_and_plot)
                
        except Exception as e:
            messagebox.showerror("错误", f"切换数据源失败: {str(e)}")
            # 回退到默认数据源
            self.data_source_var.set("sina")
            self.data_provider = StockDataProviderFactory.get_provider("sina")
            self.status_var.set("已切换到新浪财经数据源")

    def on_ma_change(self):
        """当均线选择改变时的处理函数"""
        # 更新配置
        ma_groups_config = {}
        for group_key, var in self.ma_group_vars.items():
            ma_groups_config[group_key] = var.get()
            self.ma_groups[group_key]["enabled"] = var.get()
        
        # 保存到配置文件
        indicators_config = config_manager.get_config('indicators')
        indicators_config['ma_groups'] = ma_groups_config
        config_manager.update_config('indicators', indicators_config)
        
        # 更新图表
        if hasattr(self, 'data') and not self.data.empty:
            self.update_plot()

    def on_indicator_change(self):
        """当指标选择改变时的处理函数"""
        # 更新配置
        indicators_config = {
            'show_resistance': self.show_resistance_var.get(),
            'show_ichimoku': self.show_ichimoku_var.get(),
            'show_cost_line': self.show_cost_line_var.get(),
            'ma_groups': {group_key: var.get() for group_key, var in self.ma_group_vars.items()}
        }
        
        # 保存到配置文件
        config_manager.update_config('indicators', indicators_config)
        
        # 更新图表
        if hasattr(self, 'data') and not self.data.empty:
            self.update_plot()

    def on_period_interval_change(self, event=None):
        """当周期或时间间隔变更时，如果已有数据则重新获取"""
        if hasattr(self, 'data'):
            self.fetch_and_plot()

    def fetch_and_plot(self):
        """获取股票数据并绘制K线图"""
        raw_code = self.stock_code_var.get().strip()
        if not raw_code:
            messagebox.showerror("错误", "请输入股票代码")
            return

        period = self.period_var.get()
        interval = self.interval_var.get()

        self.status_var.set(f"正在获取 {raw_code} 的K线数据...")
        self.root.update_idletasks()

        try:
            # 计算日期范围
            end_date = datetime.now()
            days = self.data_provider._convert_period_to_days(period)
            start_date = end_date - timedelta(days=days)
            
            # 使用 get_stock_data_by_date 方法获取数据
            self.data = self.data_provider.get_stock_data_by_date(
                raw_code,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if self.data.empty:
                messagebox.showerror(
                    "错误", f"无法获取 {raw_code} 的数据，请检查股票代码是否正确")
                self.status_var.set("准备就绪")
                return

            # 更新图表
            self.update_plot()
            
            # 更新窗口标题
            try:
                stock_info = self.data_provider.get_stock_info(raw_code)
                formatted_code = self.data_provider.format_stock_code(raw_code)
                if stock_info and 'shortName' in stock_info:
                    self.root.title(
                        f"股票K线图查看器 - {stock_info['shortName']} ({formatted_code})")
                elif stock_info and 'name' in stock_info:
                    self.root.title(
                        f"股票K线图查看器 - {stock_info['name']} ({formatted_code})")
                else:
                    self.root.title(f"股票K线图查看器 - {formatted_code}")
            except:
                formatted_code = self.data_provider.format_stock_code(raw_code)
                self.root.title(f"股票K线图查看器 - {formatted_code}")
        except Exception as e:
            messagebox.showerror("错误", f"获取数据时出错: {str(e)}")
            self.status_var.set("准备就绪")
            
    def update_plot(self):
        """更新图表而不重新获取数据"""
        data = self.data
        raw_code = self.stock_code_var.get().strip()
        period = self.period_var.get()
        interval = self.interval_var.get()
        
        # 清除之前的图表
        self.fig.clear()

        # 创建子图 - 价格和成交量
        gs = self.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = self.fig.add_subplot(gs[0])
        ax2 = self.fig.add_subplot(gs[1], sharex=ax1)

        # 绘制K线图
        candle_data, tooltip_annotation = plot_candlestick(ax1, data, time_markers=self.time_markers)
        if tooltip_annotation:
            add_candlestick_hover(self.fig, ax1, candle_data, tooltip_annotation, self.cost_price)
        self.candle_data = candle_data  # 保存K线数据以便其他功能使用

        # 绘制成交量
        plot_volume(ax2, data)

        # 绘制成本线
        if self.show_cost_line_var.get() and self.cost_price is not None and self.cost_price > 0:
            plot_cost_line(ax1, data, self.cost_price)

        # 添加已选择的移动平均线组
        active_ma_lines = []
        for group_key, var in self.ma_group_vars.items():
            if var.get():
                group = self.ma_groups[group_key]
                for line in group["lines"]:
                    if len(data) >= line["period"]:
                        line_name = line["name"]
                        data[line_name] = data['Close'].rolling(window=line["period"]).mean()
                        ax1.plot(range(len(data)), data[line_name], 
                                color=line["color"], linewidth=1, label=line_name,
                                alpha=0.8, linestyle='-')
                        active_ma_lines.append(line_name)
                         
        # 初始化变量，用于存储一目均衡表分析结果
        ichimoku_analysis_result = None
        
        # 添加一目均衡表
        if self.show_ichimoku_var.get():
            ichimoku_data = calculate_ichimoku(
                data,
                short_period=self.ichimoku_params['conversion'],
                mid_period=self.ichimoku_params['base'],
                long_period=self.ichimoku_params['span_b'],
                interval=interval,
                period=period
            )
            plot_ichimoku(ax1, data, ichimoku_data)
            
            # 计算一目均衡表评分
            self.technical_analyzer.set_data(data)
            meets_criteria, scores = self.technical_analyzer.check_ichimoku_signal(days=30)
            
            # 保存分析结果供状态栏和得分查看功能使用
            self.ichimoku_analysis_result = {
                'meets_criteria': meets_criteria,
                'scores': scores
            }
            ichimoku_analysis_result = self.ichimoku_analysis_result
            
            # 只在右上角显示简洁的得分信息
            score = scores['total_score']
            score_color = 'black' if score < 50 else ('orange' if score < 70 else 'green')
            score_text = f"得分: {score:.1f}"
            if meets_criteria:
                score_text += " ✓"
            else:
                score_text += " ✗"
                
            ax1.annotate(score_text, 
                      xy=(0.98, 0.97), 
                      xycoords='axes fraction',
                      fontsize=9, 
                      fontweight='bold',
                      color=score_color,
                      horizontalalignment='right',
                      verticalalignment='top',
                      bbox=dict(boxstyle="round,pad=0.2", 
                               fc='white', 
                               ec='gray', 
                               alpha=0.8))

        # 计算并绘制阻力区
        if self.show_resistance_var.get():
            # 计算斐波那契回撤位
            fib_levels = calculate_fibonacci_levels(data)
            
            # 计算当前价格
            current_price = data['Close'].iloc[-1]
            
            # 计算阻力区
            resistance_zones = calculate_resistance_zones(data, current_price)
            
            # 计算MACD指标，用于检测背离
            lookback = min(120, len(data))
            macd_data = calculate_macd(data)
            divergences = detect_macd_divergence(data, macd_data, lookback)
            
            # 绘制阻力区和斐波那契水平
            plot_resistance_zones_with_fibonacci(ax1, data, resistance_zones, fib_levels, divergences, lookback)

        # 添加图例
        ax1.legend(loc='best', fontsize='small')

        # 设置标题和标签
        formatted_code = self.data_provider.format_stock_code(raw_code)
        title = f"{formatted_code} K线图"
        if self.show_resistance_var.get():
            title += " - 含阻力区"
        if self.show_ichimoku_var.get():
            title += " - 一目均衡表"
            # 如果有一目均衡表分析结果，添加简洁评分信息
            if ichimoku_analysis_result:
                score = ichimoku_analysis_result['scores']['total_score']
                title += f" ({score:.1f}"
                if ichimoku_analysis_result['meets_criteria']:
                    title += "✓]"
                else:
                    title += "✗]"
        self.fig.suptitle(title, fontsize=12, fontweight='bold')
        ax1.set_title(f"周期: {period}, 间隔: {interval}", fontsize=10)
        ax1.set_ylabel("价格", fontsize=10)
        ax2.set_xlabel("日期", fontsize=10)
        ax2.set_ylabel("成交量", fontsize=10)

        # 隐藏上图的x轴标签
        plt.setp(ax1.get_xticklabels(), visible=False)

        # 自动调整布局
        self.fig.tight_layout()

        # 更新画布
        self.canvas.draw()

        # 更新状态栏
        status_text = f"已加载 {formatted_code} 的K线数据 | 共 {len(data)} 个数据点"
        # 添加均线信息到状态栏
        ma_groups_active = []
        for group_key, var in self.ma_group_vars.items():
            if var.get():
                ma_groups_active.append(group_key)
        if ma_groups_active:
            status_text += f" | 均线: {', '.join(ma_groups_active)}"
            
        if self.show_resistance_var.get():
            status_text += " | 已计算阻力区(考虑高成交量、心理价位和触及次数)"
        if self.show_ichimoku_var.get():
            status_text += " | 已显示一目均衡表"
            # 如果有一目均衡表分析结果，添加简洁评分信息
            if ichimoku_analysis_result:
                score = ichimoku_analysis_result['scores']['total_score']
                status_text += f" [{score:.1f}"
                if ichimoku_analysis_result['meets_criteria']:
                    status_text += "✓]"
                else:
                    status_text += "✗]"
        self.status_var.set(status_text) 

    def show_ichimoku_settings(self):
        """显示一目均衡表参数设置对话框"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("一目均衡表参数设置")
        settings_window.geometry("300x250")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 创建参数输入框
        frame = ttk.Frame(settings_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        # 转换线周期
        ttk.Label(frame, text="转换线周期(Conversion):").grid(row=0, column=0, sticky=tk.W, pady=5)
        conversion_var = tk.StringVar(value=str(self.ichimoku_params['conversion']))
        conversion_entry = ttk.Entry(frame, textvariable=conversion_var, width=10)
        conversion_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        create_tooltip(conversion_entry, "转换线计算周期，默认为9", settings_window)

        # 基准线周期
        ttk.Label(frame, text="基准线周期(Base):").grid(row=1, column=0, sticky=tk.W, pady=5)
        base_var = tk.StringVar(value=str(self.ichimoku_params['base']))
        base_entry = ttk.Entry(frame, textvariable=base_var, width=10)
        base_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        create_tooltip(base_entry, "基准线计算周期，默认为26", settings_window)

        # 先行带A周期
        ttk.Label(frame, text="先行带A周期(SpanA):").grid(row=2, column=0, sticky=tk.W, pady=5)
        span_a_var = tk.StringVar(value=str(self.ichimoku_params['span_a']))
        span_a_entry = ttk.Entry(frame, textvariable=span_a_var, width=10)
        span_a_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        create_tooltip(span_a_entry, "先行带A的计算周期，默认为26", settings_window)

        # 先行带B周期
        ttk.Label(frame, text="先行带B周期(SpanB):").grid(row=3, column=0, sticky=tk.W, pady=5)
        span_b_var = tk.StringVar(value=str(self.ichimoku_params['span_b']))
        span_b_entry = ttk.Entry(frame, textvariable=span_b_var, width=10)
        span_b_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        create_tooltip(span_b_entry, "先行带B的计算周期，默认为52", settings_window)

        # 延迟线周期
        ttk.Label(frame, text="延迟线周期(Lagging):").grid(row=4, column=0, sticky=tk.W, pady=5)
        lagging_var = tk.StringVar(value=str(self.ichimoku_params['lagging']))
        lagging_entry = ttk.Entry(frame, textvariable=lagging_var, width=10)
        lagging_entry.grid(row=4, column=1, sticky=tk.W, pady=5)
        create_tooltip(lagging_entry, "延迟线的计算周期，默认为26", settings_window)

        def save_settings():
            try:
                # 验证输入值是否为正整数
                params = {
                    'conversion': int(conversion_var.get()),
                    'base': int(base_var.get()),
                    'span_a': int(span_a_var.get()),
                    'span_b': int(span_b_var.get()),
                    'lagging': int(lagging_var.get())
                }
                
                # 验证所有值是否为正数
                if any(v <= 0 for v in params.values()):
                    raise ValueError("所有参数必须为正整数")
                
                # 保存参数
                self.ichimoku_params = params
                
                # 更新全局配置
                config_manager.update_config('ichimoku', params)
                
                # 如果当前显示了一目均衡表，则更新图表
                if self.show_ichimoku_var.get() and hasattr(self, 'data') and not self.data.empty:
                    self.update_plot()
                    
                settings_window.destroy()
                
            except ValueError as e:
                messagebox.showerror("错误", "请输入有效的正整数参数")

        # 添加重置为默认值按钮
        def reset_to_default():
            default_params = {
                'conversion': 9,
                'base': 26,
                'span_a': 26,
                'span_b': 52,
                'lagging': 26
            }
            conversion_var.set(str(default_params['conversion']))
            base_var.set(str(default_params['base']))
            span_a_var.set(str(default_params['span_a']))
            span_b_var.set(str(default_params['span_b']))
            lagging_var.set(str(default_params['lagging']))

        # 添加按钮框架
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(btn_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="重置默认值", command=reset_to_default).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)

        # 设置窗口为模态
        settings_window.focus_set()
        settings_window.wait_window() 

    def show_ichimoku_score(self):
        """显示一目均衡表得分详情对话框"""
        # 检查是否已经计算了一目均衡表得分
        if not hasattr(self, 'ichimoku_analysis_result') or not self.ichimoku_analysis_result:
            if not self.show_ichimoku_var.get():
                messagebox.showinfo("提示", "请先启用一目均衡表显示")
                return
            elif self.data.empty:
                messagebox.showinfo("提示", "请先加载股票数据")
                return
            else:
                messagebox.showinfo("提示", "未找到一目均衡表分析结果")
                return
        
        # 创建对话框
        score_window = tk.Toplevel(self.root)
        score_window.title("一目均衡表分析得分")
        score_window.geometry("400x350")
        score_window.transient(self.root)
        score_window.grab_set()
        
        # 创建内容框架
        frame = ttk.Frame(score_window, padding="15")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 获取评分数据
        scores = self.ichimoku_analysis_result['scores']
        meets_criteria = self.ichimoku_analysis_result['meets_criteria']
        
        # 显示总得分和判断结果
        total_score = scores['total_score']
        score_color = "black" if total_score < 50 else ("orange" if total_score < 70 else "green")
        
        score_label = ttk.Label(frame, text=f"总得分: {total_score:.1f}", 
                             font=("Arial", 14, "bold"))
        score_label.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        score_label.configure(foreground=score_color)
        
        # 创建一个判断结果标签
        result_text = "符合条件" if meets_criteria else "不符合条件"
        result_color = "green" if meets_criteria else "red"
        result_label = ttk.Label(frame, text=f"判断结果: {result_text}", 
                              font=("Arial", 12))
        result_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 15))
        result_label.configure(foreground=result_color)
        
        # 创建分隔线
        separator = ttk.Separator(frame, orient='horizontal')
        separator.grid(row=2, column=0, columnspan=2, sticky='ew', pady=5)
        
        # 设置详细得分表头
        ttk.Label(frame, text="得分项目", font=("Arial", 11, "bold")).grid(row=3, column=0, sticky=tk.W, pady=(10, 5))
        ttk.Label(frame, text="得分值", font=("Arial", 11, "bold")).grid(row=3, column=1, sticky=tk.W, pady=(10, 5))
        
        # 添加详细得分
        score_items = [
            ("云层位置", scores['cloud_score']),
            ("转换/基准线关系", scores['conversion_base_score']),
            ("价格/基准线关系", scores['price_base_score']),
            ("迟行带位置", scores['chikou_score']),
            ("未来云形态", scores['future_cloud_score'])
        ]
        
        # 如果有MA10回踩加分，也添加进来
        if 'ma10_pullback_bonus' in scores and scores['ma10_pullback_bonus'] > 0:
            score_items.append(("MA10回踩加分", scores['ma10_pullback_bonus']))
        
        # 显示每个得分项
        for i, (name, value) in enumerate(score_items):
            ttk.Label(frame, text=name).grid(row=i+4, column=0, sticky=tk.W, pady=3)
            
            # 根据得分值设置不同颜色
            value_color = "black"
            if name != "MA10回踩加分":  # 常规得分项
                if abs(value) >= 70:
                    value_color = "green" if value > 0 else "red"
                elif abs(value) >= 40:
                    value_color = "orange" if value > 0 else "orange"
            else:  # MA10加分项始终为绿色
                value_color = "green"
                
            value_label = ttk.Label(frame, text=f"{value:.1f}")
            value_label.grid(row=i+4, column=1, sticky=tk.W, pady=3)
            value_label.configure(foreground=value_color)
        
        # 添加云层上方比例显示（如果有）
        if 'true_above_ratio' in scores:
            ratio_row = len(score_items) + 4
            ttk.Label(frame, text="云层上方比例").grid(row=ratio_row, column=0, sticky=tk.W, pady=3)
            ratio_text = f"{scores['true_above_ratio']*100:.1f}%"
            ratio_label = ttk.Label(frame, text=ratio_text)
            ratio_label.grid(row=ratio_row, column=1, sticky=tk.W, pady=3)
            ratio_color = "green" if scores['true_above_ratio'] >= 0.6 else "black"
            ratio_label.configure(foreground=ratio_color)
        
        # 添加权重信息
        weight_info = ttk.Label(frame, text="*得分权重: 云层40%, 转换/基准20%, 价格/基准20%, 迟行15%, 未来云5%", 
                              font=("Arial", 9), foreground="gray")
        weight_info.grid(row=len(score_items)+5, column=0, columnspan=2, sticky=tk.W, pady=(20, 0))
        
        # 添加关闭按钮
        ttk.Button(frame, text="关闭", command=score_window.destroy).grid(
            row=len(score_items)+6, column=0, columnspan=2, pady=(15, 0))
        
        # 设置列权重
        frame.columnconfigure(0, weight=3)
        frame.columnconfigure(1, weight=2)
        
        # 设置窗口为模态
        score_window.focus_set()
        score_window.wait_window()

    def add_time_marker(self, time, text, color='#FF4444'):
        """
        添加时间标记
        
        参数:
        time: 时间字符串，格式为'YYYY-MM-DD'
        text: 标记文本
        color: 标记颜色，默认为醒目的红色
        """
        self.time_markers.append({
            'time': time,
            'text': text,
            'color': color
        })
        self.update_plot()

    def _plot_time_markers(self, ax, data):
        """在图表上绘制时间标记"""
        if not self.time_markers:
            return
        
        ymin, ymax = ax.get_ylim()
        price_range = ymax - ymin
        
        for marker in self.time_markers:
            try:
                # 找到对应时间的索引
                time_idx = data.index.get_loc(pd.to_datetime(marker['time']))
                
                # 获取该时间点的价格
                price = data['Close'].iloc[time_idx]
                
                # 绘制标记线
                ax.axvline(x=time_idx, color=marker['color'], alpha=0.2, linestyle='-', linewidth=2)
                
                # 在顶部添加标记点
                marker_y = ymax - price_range * 0.02
                ax.plot(time_idx, marker_y, marker='v', color=marker['color'], 
                       markersize=8, alpha=0.8)
                
                # 创建带背景的文本标记
                bbox_props = dict(
                    boxstyle='round4,pad=0.5',
                    fc='white',
                    ec=marker['color'],
                    alpha=0.9
                )
                
                # 添加文本标记
                ax.annotate(
                    marker['text'],
                    xy=(time_idx, ymax),
                    xytext=(0, 5),  # 5 points 垂直偏移
                    textcoords='offset points',
                    color=marker['color'],
                    fontsize=9,
                    weight='bold',
                    bbox=bbox_props,
                    va='bottom',
                    ha='center',
                    rotation=0
                )
                
            except (KeyError, ValueError) as e:
                print(f"无法绘制时间标记: {e}")
                continue

    def clear_time_markers(self):
        """清除所有时间标记"""
        self.time_markers = []
        self.update_plot()
        
    def get_time_markers(self):
        """获取所有时间标记"""
        return self.time_markers.copy() 