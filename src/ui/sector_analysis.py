"""
板块分析窗口 (优化版)
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from datetime import datetime, timedelta
from functools import partial # 用于列排序

# 定义常量
WINDOW_WIDTH = 1300
WINDOW_HEIGHT = 800
COLUMN_WIDTHS = {
    "name": 200,
    "count": 80,
    "avg_change": 100,
    "max_change": 100,
    "min_change": 100,
    "hit_dates": 350
}

class SectorAnalysisWindow:
    def __init__(self, parent, df):
        self.parent = parent
        # --- 数据预处理 ---
        if '命中日期' not in df.columns:
             messagebox.showerror("错误", "缺少 '命中日期' 列")
             # self.window.destroy() # 不能在这里destroy，window还没创建
             # 最好是让调用者处理，或者返回一个错误状态
             raise ValueError("缺少 '命中日期' 列")
        if '涨跌幅' not in df.columns:
             messagebox.showerror("错误", "缺少 '涨跌幅' 列")
             raise ValueError("缺少 '涨跌幅' 列")

        self.df = self._preprocess_data(df.copy()) # 预处理数据
        self.all_dates = self._get_all_unique_dates() # 获取所有唯一日期并排序

        self.current_date_filter = "全部" # 当前筛选的日期

        # 创建新窗口
        self.window = tk.Toplevel(parent)
        self.window.title("板块分析")
        self.window.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        # 设置窗口关闭处理
        self.window.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 使窗口大小可调整
        self.window.resizable(True, True)

        self._create_ui()
        self._update_display_data() # 初始显示全部数据

    def _on_closing(self):
        """处理窗口关闭事件"""
        self.window.destroy()

    def _preprocess_data(self, df):
        """数据预处理，确保列类型正确"""
        # 改进涨跌幅处理逻辑
        def parse_percentage(value):
            if pd.isna(value):
                return pd.NA  # 使用pandas的NA而不是0.0
            try:
                # 移除空白字符和百分号
                cleaned_value = str(value).strip().rstrip('%')
                return float(cleaned_value)
            except ValueError:
                print(f"警告：无法解析涨跌幅值: {value}")
                return pd.NA

        # 处理涨跌幅
        df['涨跌幅_数值'] = df['涨跌幅'].apply(parse_percentage)
        
        # 填充缺失值为0（可选，取决于业务需求）
        df['涨跌幅_数值'] = df['涨跌幅_数值'].fillna(0.0)

        # 解析命中日期，可能一行有多个日期，我们主要关心筛选
        # 保留原始命中日期字符串用于显示分布
        return df

    def _get_all_unique_dates(self):
        """从'命中日期'列提取所有唯一的日期并排序"""
        all_dates_set = set()
        if '命中日期' in self.df.columns:
            for date_str in self.df['命中日期'].dropna().unique():
                try:
                    # 处理逗号分隔的多个日期
                    dates = [d.strip() for d in str(date_str).split(',') if d.strip()]
                    for date in dates:
                        # 尝试解析日期以验证格式，并添加到集合中
                        datetime.strptime(date, '%Y%m%d')
                        all_dates_set.add(date)
                except (ValueError, TypeError):
                    # 忽略无法解析的日期格式或非字符串类型
                    print(f"警告：忽略无法解析的日期格式: {date_str}")
                    continue
        # 按日期字符串排序（YYYYMMDD格式可以直接按字符串排序）
        return sorted(list(all_dates_set), reverse=True) # 最新的在前面

    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.window, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # --- 创建顶部工具栏 ---
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(fill=tk.X, pady=(0, 10))

        # 日期选择标签
        ttk.Label(self.toolbar, text="选择日期:").pack(side=tk.LEFT, padx=(0, 5))

        # 日期选择下拉菜单
        self.date_var = tk.StringVar()
        self.date_combobox = ttk.Combobox(self.toolbar, textvariable=self.date_var,
                                          values=["全部"] + self.all_dates, state="readonly", width=12)
        if self.all_dates:
            self.date_combobox.set("全部") # 默认显示全部
        self.date_combobox.pack(side=tk.LEFT, padx=5)
        self.date_combobox.bind("<<ComboboxSelected>>", self._on_date_select)

        # 快捷按钮
        self.today_btn = ttk.Button(self.toolbar, text="最新", command=self._show_latest_data, width=6)
        self.today_btn.pack(side=tk.LEFT, padx=5)
        self.yesterday_btn = ttk.Button(self.toolbar, text="昨天", command=self._show_previous_data, width=6)
        self.yesterday_btn.pack(side=tk.LEFT, padx=5)
        self.all_btn = ttk.Button(self.toolbar, text="全部", command=self._show_all_data, width=6)
        self.all_btn.pack(side=tk.LEFT, padx=5)

        # --- 创建Notebook ---
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(5,0))

        # 创建标签页和表格
        self.tabs = {}
        tab_configs = [
            ("申万板块", "申万板块"),
            ("同花顺概念", "同花顺概念"),
            ("开盘啦概念", "开盘啦概念")
        ]

        for tab_name, column_name in tab_configs:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text=tab_name)
            tree = self._create_tree(frame, column_name) # 传递列名用于排序
            self.tabs[tab_name] = {"frame": frame, "tree": tree, "column": column_name}

        # --- 创建状态栏 ---
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(self.window, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def _create_tree(self, parent, analysis_column_name):
        """创建表格，并添加排序功能和颜色标签"""
        tree = ttk.Treeview(parent, selectmode="browse") # selectmode browse 更好
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) # 改为side=LEFT

        # 添加垂直滚动条
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=vsb.set)

        # 添加水平滚动条
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        hsb.pack(side=tk.BOTTOM, fill=tk.X) # 需要放在vsb之后pack
        tree.configure(xscrollcommand=hsb.set)


        # 定义列
        columns = {
            "name": ("名称", 200, tk.W),
            "count": ("股票数量", 80, tk.CENTER),
            "count_change": ("数量变化 (较前日)", 100, tk.CENTER),  # 新增列
            "avg_change": ("平均涨跌幅", 100, tk.CENTER),
            "max_change": ("最大涨幅", 100, tk.CENTER),
            "min_change": ("最小涨幅", 100, tk.CENTER),
            "hit_dates": ("命中日期分布", 350, tk.W)
        }
        tree["columns"] = list(columns.keys())
        tree.column("#0", width=0, stretch=tk.NO)

        # 设置列属性和排序命令
        for col_id, (text, width, anchor) in columns.items():
            # 使用 partial 传递列ID和Treeview实例给排序函数
            sort_command = partial(self._sort_column, tree, col_id, False)
            tree.heading(col_id, text=text, anchor=tk.CENTER, command=sort_command)
            tree.column(col_id, width=width, anchor=anchor)

        # --- 配置颜色标签 ---
        tree.tag_configure('positive', foreground='red')
        tree.tag_configure('negative', foreground='green')
        # 可以添加 'zero' tag 如果需要区分0和正负
        # tree.tag_configure('zero', foreground='black')

        return tree

    # --- 日期筛选逻辑 ---
    def _on_date_select(self, event=None):
        """响应下拉菜单选择事件"""
        selected_date = self.date_var.get()
        self.current_date_filter = selected_date
        self._update_display_data()

    def _show_latest_data(self):
        """显示最新日期的数据"""
        if self.all_dates:
            latest_date = self.all_dates[0]
            self.date_var.set(latest_date)
            self.current_date_filter = latest_date
            self._update_display_data()
        else:
            messagebox.showinfo("提示", "没有可用的日期数据")

    def _show_previous_data(self):
        """显示当前选定日期的前一个交易日的数据"""
        current_selection = self.date_var.get()
        if current_selection == "全部" or not self.all_dates:
             if self.all_dates: # 如果当前是全部，则显示最新的前一天
                 if len(self.all_dates) > 1:
                     prev_date = self.all_dates[1]
                     self.date_var.set(prev_date)
                     self.current_date_filter = prev_date
                     self._update_display_data()
                 else:
                     messagebox.showinfo("提示", "只有一个可用日期")
             else:
                 messagebox.showinfo("提示", "没有可用的日期数据")
             return

        try:
            current_index = self.all_dates.index(current_selection)
            if current_index + 1 < len(self.all_dates):
                prev_date = self.all_dates[current_index + 1]
                self.date_var.set(prev_date)
                self.current_date_filter = prev_date
                self._update_display_data()
            else:
                messagebox.showinfo("提示", "已经是数据中最早的日期")
        except ValueError:
             messagebox.showinfo("提示", "当前选择无效") # 以防万一

    def _show_all_data(self):
        """显示所有日期的数据"""
        self.date_var.set("全部")
        self.current_date_filter = "全部"
        self._update_display_data()

    # --- 数据分析与显示核心逻辑 ---
    def _update_display_data(self):
        """根据当前日期过滤器更新所有表格的显示"""
        try:
            if self.current_date_filter == "全部":
                df_to_analyze = self.df
                date_info = "全部日期"
                prev_data = None  # 全部数据时不显示变化
            else:
                # 获取当前日期数据
                target_date = self.current_date_filter
                df_to_analyze = self.df[self.df['命中日期'].astype(str).str.contains(target_date, na=False)]
                date_info = f"日期: {target_date}"

                # 获取前一天数据
                prev_date = self._get_previous_available_date(target_date)
                prev_data = {}
                if prev_date:
                    prev_df = self.df[self.df['命中日期'].astype(str).str.contains(prev_date, na=False)]
                    for tab_name, tab_info in self.tabs.items():
                        prev_data[tab_info["column"]] = self._analyze_data_for_date(prev_df, tab_info["column"])
                    date_info += f" (对比: {prev_date})"

            if df_to_analyze.empty and self.current_date_filter != "全部":
                messagebox.showinfo("提示", f"没有找到 {self.current_date_filter} 的数据")
                # 清空表格
                for tab_info in self.tabs.values():
                    self._clear_tree(tab_info["tree"])
                self.status_var.set(f"当前显示: {date_info} | 无数据")
                return

            total_counts = {}
            for tab_name, tab_info in self.tabs.items():
                tree = tab_info["tree"]
                column_name = tab_info["column"]
                self._clear_tree(tree)  # 清空旧数据
                
                # 传递前一天的数据进行比较
                prev_data_for_column = prev_data.get(column_name) if prev_data else None
                count = self._analyze_and_populate_tree(column_name, tree, df_to_analyze, prev_data_for_column)
                total_counts[tab_name] = count

            # 更新状态栏
            status_parts = [f"当前显示: {date_info}"]
            status_parts.extend([f"{name}: {count}个" for name, count in total_counts.items()])
            self.status_var.set(" | ".join(status_parts))

        except Exception as e:
            messagebox.showerror("错误", f"更新数据时发生错误: {str(e)}")
            print(f"Error during data update: {e}")  # 打印到控制台

    def _clear_tree(self, tree):
        """清空Treeview中的所有项目"""
        for item in tree.get_children():
            tree.delete(item)

    def _analyze_and_populate_tree(self, column_name, tree, df_to_analyze, prev_data=None):
        """分析指定列的数据并填充到Treeview中"""
        if column_name not in df_to_analyze.columns:
             print(f"警告: 列 '{column_name}' 不在提供的DataFrame中。")
             return 0

        # 检查必要的列
        required_cols = [column_name, '涨跌幅_数值', '命中日期']
        if not all(col in df_to_analyze.columns for col in required_cols):
            missing = [col for col in required_cols if col not in df_to_analyze.columns]
            messagebox.showwarning("警告", f"分析所需的部分列缺失: {', '.join(missing)}")
            return 0

        # 获取当前数据的分析结果
        data = self._analyze_data_for_date(df_to_analyze, column_name)
        
        # 使用列表推导式优化排序过程
        sorted_items = sorted(
            data.items(),
            key=lambda x: (x[1]['count'], -sum(x[1]['changes'])/len(x[1]['changes']) if x[1]['changes'] else 0),
            reverse=True
        )

        # 批量插入数据到树形视图
        for item, item_data in sorted_items:
            avg_change = sum(item_data['changes']) / len(item_data['changes']) if item_data['changes'] else 0.0
            
            # 计算数量变化
            count_change = 0
            if prev_data and item in prev_data:
                count_change = item_data['count'] - prev_data[item]['count']
            elif prev_data:
                count_change = item_data['count']  # 新出现的板块
            
            # 格式化数量变化显示
            count_change_str = ""
            if count_change > 0:
                count_change_str = f"+{count_change}"
            elif count_change < 0:
                count_change_str = str(count_change)
            
            # 使用列表推导式优化日期分布字符串生成
            date_dist = [
                f"{date}({count})" 
                for date, count in sorted(
                    item_data['hit_dates'].items(),
                    key=lambda x: (x[0], x[1]),
                    reverse=True
                )
            ]
            
            # 确定标签
            tags = ('positive',) if avg_change > 0 else ('negative',) if avg_change < 0 else ()
            
            # 插入数据行
            tree.insert("", tk.END, values=(
                item,
                item_data['count'],
                count_change_str,  # 新增数量变化列
                f"{avg_change:.2f}%",
                f"{item_data['max_change']:.2f}%" if item_data['max_change'] > float('-inf') else "N/A",
                f"{item_data['min_change']:.2f}%" if item_data['min_change'] < float('inf') else "N/A",
                ", ".join(date_dist)
            ), tags=tags)
            
        return len(sorted_items)

    def _sort_column(self, tree, col, reverse):
        """对表格列进行排序
        
        Args:
            tree: Treeview实例
            col: 要排序的列名
            reverse: 是否反向排序
        """
        # 获取所有项目
        items = [(tree.set(item, col), item) for item in tree.get_children('')]
        
        # 根据不同列类型进行排序
        if col in ['count', 'avg_change', 'max_change', 'min_change']:
            # 数值型列的排序
            def extract_number(x):
                try:
                    # 移除百分号并转换为浮点数
                    return float(x[0].replace('%', ''))
                except ValueError:
                    return float('-inf')  # 对于无效值返回负无穷
            
            items.sort(key=extract_number, reverse=reverse)
        elif col == 'count_change':
            # 数量变化列的排序
            def extract_change(x):
                try:
                    # 处理带加号的正数
                    value = x[0].replace('+', '') if x[0] else '0'
                    return float(value)
                except ValueError:
                    return 0.0
            
            items.sort(key=extract_change, reverse=reverse)
        else:
            # 文本型列的排序
            items.sort(reverse=reverse)
        
        # 重新排列项目
        for index, (_, item) in enumerate(items):
            tree.move(item, '', index)
        
        # 切换排序方向并更新表头
        tree.heading(col, command=lambda: self._sort_column(tree, col, not reverse))

    def _get_previous_available_date(self, current_date):
        """获取数据中存在的前一个可用日期"""
        try:
            current_index = self.all_dates.index(current_date)
            if current_index + 1 < len(self.all_dates):
                return self.all_dates[current_index + 1]
        except ValueError:
            pass
        return None

    def _analyze_data_for_date(self, df_to_analyze, column_name):
        """分析特定日期的数据，返回分析结果字典"""
        data = {}
        
        valid_rows = df_to_analyze[column_name].notna()
        valid_df = df_to_analyze[valid_rows]
        
        for items_str, change_pct, hit_date in zip(
            valid_df[column_name],
            valid_df['涨跌幅_数值'],
            valid_df['命中日期']
        ):
            items = str(items_str).split(',')
            hit_date_str = str(hit_date) if pd.notna(hit_date) else ''
            hit_dates = [d.strip() for d in hit_date_str.split(',') if d.strip()]
            
            for item in items:
                item = item.strip()
                if not item or item.lower() == 'nan':
                    continue
                    
                if item not in data:
                    data[item] = {
                        'count': 0,
                        'changes': [],
                        'max_change': float('-inf'),
                        'min_change': float('inf'),
                        'hit_dates': {}
                    }
                
                data[item]['count'] += 1
                data[item]['changes'].append(change_pct)
                data[item]['max_change'] = max(data[item]['max_change'], change_pct)
                data[item]['min_change'] = min(data[item]['min_change'], change_pct)
                
                for date in hit_dates:
                    data[item]['hit_dates'][date] = data[item]['hit_dates'].get(date, 0) + 1
                    
        return data