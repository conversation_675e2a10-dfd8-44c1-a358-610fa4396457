"""
股票K线图应用，已改用模块化结构实现
此文件保留用于兼容性
"""
import argparse
import tkinter as tk
import sys
import os

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 从新的模块化结构导入
from src.ui.stock_kline.app import StockKLineApp

# 兼容旧的入口点，用于向后兼容
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="股票K线图查看器")
    parser.add_argument("symbol", nargs="?", help="股票代码")
    parser.add_argument("--cost", type=float, help="成本价格")
    args = parser.parse_args()

    # 检查命令行参数
    initial_symbol = args.symbol if args.symbol else None
    cost_price = args.cost if args.cost else None

    root = tk.Tk()
    app = StockKLineApp(root, initial_symbol, cost_price=cost_price)
    root.mainloop()


if __name__ == "__main__":
    main()
