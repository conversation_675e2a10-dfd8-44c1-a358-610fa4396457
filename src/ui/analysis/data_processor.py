import pandas as pd
import numpy as np

class DataProcessor:
    @staticmethod
    def validate_stock_data(stock_data):
        """验证股票数据的有效性"""
        if stock_data is None or stock_data.empty:
            return False
            
        # 检查必要的列是否存在
        required_columns = ['Close', 'Open', 'High', 'Low', 'Volume']
        if not all(col in stock_data.columns for col in required_columns):
            return False
            
        # 检查数据是否包含有效值
        if stock_data[required_columns].isnull().any().any():
            return False
            
        # 检查价格数据的合理性
        if not (stock_data['High'] >= stock_data['Low']).all():
            return False
            
        if not (stock_data['High'] >= stock_data['Open']).all():
            return False
            
        if not (stock_data['High'] >= stock_data['Close']).all():
            return False
            
        if not (stock_data['Low'] <= stock_data['Open']).all():
            return False
            
        if not (stock_data['Low'] <= stock_data['Close']).all():
            return False
            
        # 检查成交量是否为正数
        if not (stock_data['Volume'] >= 0).all():
            return False
            
        return True

    @staticmethod
    def calculate_price_changes(stock_data):
        """计算价格变化"""
        try:
            if len(stock_data) < 2:
                return 0
                
            current_price = stock_data['Close'].iloc[-1]
            prev_price = stock_data['Close'].iloc[-2]
            return (current_price - prev_price) / prev_price * 100
        except:
            return 0

    @staticmethod
    def filter_data(data, conditions):
        """根据条件过滤数据"""
        filtered_data = data.copy()
        
        for column, condition in conditions.items():
            if isinstance(condition, dict):
                if condition.get('type') == 'greater':
                    filtered_data = filtered_data[filtered_data[column] > condition['value']]
                elif condition.get('type') == 'less':
                    filtered_data = filtered_data[filtered_data[column] < condition['value']]
                elif condition.get('type') == 'equals':
                    filtered_data = filtered_data[filtered_data[column] == condition['value']]
                elif condition.get('type') == 'contains':
                    filtered_data = filtered_data[filtered_data[column].astype(str).str.contains(
                        condition['value'], case=False)]
                elif condition.get('type') == 'in':
                    filtered_data = filtered_data[filtered_data[column].isin(condition['value'])]
                    
        return filtered_data

    @staticmethod
    def standardize_column_names(stock_data):
        """统一股票数据列名"""
        column_mapping = {
            '收盘价': 'Close',
            '开盘价': 'Open',
            '最高价': 'High',
            '最低价': 'Low',
            '成交量': 'Volume',
            '成交额': 'Amount'
        }
        return stock_data.rename(columns=column_mapping) 