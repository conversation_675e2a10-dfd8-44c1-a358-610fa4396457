import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
from queue import Queue
import time
import json
import re

from .analysis_ui import AnalysisUI
from .data_processor import DataProcessor
from src.data_providers.stock_data_provider import StockDataProviderFactory

class DataAnalyzer:
    def __init__(self, parent, filtered_df):
        self.parent = parent
        self.filtered_df = filtered_df
        self.ui = AnalysisUI(parent)
        self.data_processor = DataProcessor()
        self.analysis_window = None
        self.data_provider = StockDataProviderFactory.get_provider('sina')
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 300
        
        # 初始化变量
        self.filter_type_var = tk.StringVar(value="contains")
        self.filter_value_var = tk.StringVar()
        self.analysis_column_var = tk.StringVar()
        self.show_table_var = tk.BooleanVar(value=True)
        self.show_chart_var = tk.BooleanVar(value=True)
        self.time_range_var = tk.StringVar(value="5d")
        self.price_change_type_var = tk.StringVar(value="daily")
        self.price_change_threshold_var = tk.StringVar(value="5")
        self.price_change_direction_var = tk.StringVar(value="up")
        self.chart_type_var = tk.StringVar(value="bar")
        
    def create_analysis_window(self):
        """创建数据分析窗口"""
        if self.filtered_df is None or len(self.filtered_df) == 0:
            messagebox.showinfo("提示", "没有数据可用于分析")
            return
            
        # 创建分析窗口
        if self.analysis_window and self.analysis_window.winfo_exists():
            self.analysis_window.lift()
            return
            
        self.analysis_window = self.ui.create_window("数据分析", "1200x800")
        
        # 创建主框架
        self._create_main_frame()
        
    def _create_main_frame(self):
        """创建主框架"""
        # 创建主分割框架
        main_paned = ttk.PanedWindow(self.analysis_window, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左侧配置面板
        config_frame = self._create_config_panel(main_paned)
        
        # 创建右侧数据显示面板
        display_frame = self._create_display_panel(main_paned)
        
        # 添加面板到分割窗口
        main_paned.add(config_frame, weight=1)
        main_paned.add(display_frame, weight=3)
        
    def _create_config_panel(self, parent):
        """创建配置面板"""
        # 创建配置面板外层框架（带滚动条）
        config_outer_frame = ttk.Frame(parent)
        
        # 创建垂直滚动条
        config_scrollbar = ttk.Scrollbar(config_outer_frame, orient=tk.VERTICAL)
        config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建Canvas作为滚动视图
        self.config_canvas = tk.Canvas(config_outer_frame, yscrollcommand=config_scrollbar.set, 
                                     bg="#f5f5f5", highlightthickness=0)
        self.config_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条控制Canvas
        config_scrollbar.config(command=self.config_canvas.yview)
        
        # 创建内部框架放置配置选项
        self.config_frame = ttk.Frame(self.config_canvas, padding="10")
        
        # 把内部框架放到Canvas中
        self.config_window = self.config_canvas.create_window((0, 0), 
                                                            window=self.config_frame, 
                                                            anchor=tk.NW)
        
        # 添加所有配置选项
        self._add_config_options()
        
        # 设置Canvas的滚动区域
        self.config_frame.update_idletasks()
        self.config_canvas.config(scrollregion=self.config_canvas.bbox("all"))
        self.config_canvas.config(width=300)
        
        # 绑定Canvas大小变化事件
        self.config_frame.bind("<Configure>", self._on_frame_configure)
        
        # 绑定鼠标滚轮事件
        self.config_canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        
        return config_outer_frame
        
    def _create_display_panel(self, parent):
        """创建数据显示面板"""
        display_frame = ttk.Frame(parent, padding="10")
        
        # 创建标题标签
        self.display_header = ttk.Label(display_frame, text="数据分析结果", 
                                      font=("Helvetica", 14, "bold"))
        self.display_header.pack(anchor=tk.W, pady=(0, 10))
        
        # 创建数据统计信息标签
        self.stats_info = ttk.Label(display_frame, text="", foreground="#555555")
        self.stats_info.pack(anchor=tk.W, pady=(0, 10))
        
        # 创建数据显示区域
        display_paned = ttk.PanedWindow(display_frame, orient=tk.VERTICAL)
        display_paned.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格显示区域
        table_frame = ttk.LabelFrame(display_paned, text="数据表格", padding=10)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        self._create_table(table_frame)
        
        # 创建图表显示区域
        chart_frame = ttk.LabelFrame(display_paned, text="数据图表", padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建图表
        self._create_chart(chart_frame)
        
        # 添加到分割窗口
        display_paned.add(table_frame, weight=1)
        display_paned.add(chart_frame, weight=1)
        
        return display_frame
        
    def _create_table(self, parent):
        """创建数据表格"""
        self.table = self.ui.create_table(parent)
        
    def _create_chart(self, parent):
        """创建图表区域"""
        self.chart_figure, self.chart_canvas = self.ui.create_chart(parent)
        
    def _add_config_options(self):
        """添加配置选项"""
        # 标题
        title_label = ttk.Label(self.config_frame, text="数据分析设置", 
                              font=("Helvetica", 12, "bold"))
        title_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 时间范围选择
        self._add_time_range_selection()
        
        # 涨幅条件设置
        self._add_price_change_conditions()
        
        # 选择列
        self._add_column_selection()
        
        # 筛选条件
        self._add_filter_conditions()
        
        # 显示选项
        self._add_display_options()
        
        # 操作按钮
        self._add_action_buttons()
        
    def _add_time_range_selection(self):
        """添加时间范围选择"""
        time_frame = ttk.LabelFrame(self.config_frame, text="时间范围", padding=10)
        time_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 时间范围选择
        ttk.Label(time_frame, text="选择时间范围:").pack(anchor=tk.W)
        time_ranges = [
            ("最近5天", "5d"),
            ("最近10天", "10d"),
            ("最近20天", "20d"),
            ("最近30天", "30d"),
            ("最近60天", "60d"),
            ("最近90天", "90d")
        ]
        
        for text, value in time_ranges:
            ttk.Radiobutton(time_frame, text=text,
                          variable=self.time_range_var,
                          value=value).pack(anchor=tk.W)
        
    def _add_price_change_conditions(self):
        """添加涨幅条件设置"""
        price_frame = ttk.LabelFrame(self.config_frame, text="涨幅条件", padding=10)
        price_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 涨幅类型选择
        ttk.Label(price_frame, text="涨幅类型:").pack(anchor=tk.W)
        price_types = [
            ("单日涨幅", "daily"),
            ("区间涨幅", "period"),
            ("最高涨幅", "max"),
            ("最低涨幅", "min")
        ]
        
        for text, value in price_types:
            ttk.Radiobutton(price_frame, text=text,
                          variable=self.price_change_type_var,
                          value=value).pack(anchor=tk.W)
        
        # 涨跌方向选择
        direction_frame = ttk.Frame(price_frame)
        direction_frame.pack(fill=tk.X, pady=5)
        ttk.Label(direction_frame, text="涨跌方向:").pack(side=tk.LEFT)
        ttk.Radiobutton(direction_frame, text="上涨",
                      variable=self.price_change_direction_var,
                      value="up").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(direction_frame, text="下跌",
                      variable=self.price_change_direction_var,
                      value="down").pack(side=tk.LEFT)
        
        # 涨幅阈值设置
        threshold_frame = ttk.Frame(price_frame)
        threshold_frame.pack(fill=tk.X, pady=5)
        ttk.Label(threshold_frame, text="涨幅阈值(%):").pack(side=tk.LEFT)
        ttk.Entry(threshold_frame, textvariable=self.price_change_threshold_var,
                 width=10).pack(side=tk.LEFT, padx=5)
        
        # 添加查询按钮
        ttk.Button(price_frame, text="开始查询", 
                  command=self._start_data_query).pack(pady=(5, 0))
        
    def _add_column_selection(self):
        """添加列选择部分"""
        column_frame = ttk.LabelFrame(self.config_frame, text="选择列", padding=10)
        column_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 定义列的描述信息
        column_descriptions = {
            "股票代码": "用于识别不同股票的编号",
            "股票名称": "股票的中文名称",
            "开盘价": "每个交易日的开盘价格",
            "收盘价": "每个交易日的收盘价格",
            "最高价": "交易期间的最高价格",
            "最低价": "交易期间的最低价格",
            "成交量": "交易期间的总成交量",
            "成交额": "交易期间的总成交金额",
            "涨跌幅(%)": "相比前一个交易日的价格变动百分比",
            "振幅(%)": "最高价与最低价之间的波动幅度",
            "换手率(%)": "成交量占流通股本的百分比"
        }
        
        # 选择要分析的列
        analysis_frame = ttk.Frame(column_frame)
        analysis_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(analysis_frame, text="分析列:").pack(anchor=tk.W)
        analysis_column_combo = ttk.Combobox(analysis_frame, 
                                           textvariable=self.analysis_column_var,
                                           state="readonly", 
                                           width=30)
        analysis_column_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # 创建分析列描述标签
        self.analysis_desc_label = ttk.Label(analysis_frame, 
                                           text="请选择要分析的数据列",
                                           foreground="#666666",
                                           wraplength=250)
        self.analysis_desc_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 选择筛选列
        filter_frame = ttk.Frame(column_frame)
        filter_frame.pack(fill=tk.X)
        
        ttk.Label(filter_frame, text="筛选列:").pack(anchor=tk.W)
        self.filter_column_var = tk.StringVar()
        filter_column_combo = ttk.Combobox(filter_frame, 
                                         textvariable=self.filter_column_var,
                                         state="readonly", 
                                         width=30)
        filter_column_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # 创建筛选列描述标签
        self.filter_desc_label = ttk.Label(filter_frame, 
                                         text="请选择要筛选的数据列",
                                         foreground="#666666",
                                         wraplength=250)
        self.filter_desc_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 填充可用的列
        columns = list(self.filtered_df.columns)
        analysis_column_combo['values'] = columns
        filter_column_combo['values'] = columns
        
        if len(columns) > 0:
            analysis_column_combo.current(0)
            filter_column_combo.current(0)
            
        # 绑定列选择变更事件
        def update_analysis_desc(*args):
            selected = self.analysis_column_var.get()
            desc = column_descriptions.get(selected, "该列的数据描述")
            self.analysis_desc_label.config(text=desc)
            
        def update_filter_desc(*args):
            selected = self.filter_column_var.get()
            desc = column_descriptions.get(selected, "该列的数据描述")
            self.filter_desc_label.config(text=desc)
            
        self.analysis_column_var.trace_add("write", update_analysis_desc)
        self.filter_column_var.trace_add("write", update_filter_desc)
        
        # 绑定列选择变更事件
        self.analysis_column_var.trace_add("write", self.refresh_analysis)
        self.filter_column_var.trace_add("write", self.refresh_analysis)
        
        # 初始化描述文本
        update_analysis_desc()
        update_filter_desc()
        
    def _add_filter_conditions(self):
        """添加筛选条件部分"""
        filter_frame = ttk.LabelFrame(self.config_frame, text="筛选条件", padding=10)
        filter_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 创建筛选条件容器
        self.filter_container = ttk.Frame(filter_frame)
        self.filter_container.pack(fill=tk.X, expand=True)
        
        # 用于存储当前的筛选控件
        self.current_filter_widgets = []
        
        # 更新筛选界面的函数
        def update_filter_ui(*args):
            # 清除当前的筛选控件
            for widget in self.current_filter_widgets:
                widget.destroy()
            self.current_filter_widgets.clear()
            
            selected_column = self.filter_column_var.get()
            
            # 对于分类数据（如申万板块、股票名称等），显示选择列表
            if selected_column in ["申万板块", "股票名称", "行业"]:
                # 获取该列的唯一值
                unique_values = sorted(self.filtered_df[selected_column].unique())
                
                # 创建列表框和滚动条
                list_frame = ttk.Frame(self.filter_container)
                list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
                self.current_filter_widgets.append(list_frame)
                
                scrollbar = ttk.Scrollbar(list_frame)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                
                # 改为多选模式
                listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, 
                                   selectmode=tk.MULTIPLE, height=6)
                listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                
                scrollbar.config(command=listbox.yview)
                
                # 填充选项
                for value in unique_values:
                    if pd.notna(value):  # 排除空值
                        listbox.insert(tk.END, value)
                
                # 绑定选择事件
                def on_select(event):
                    selected_indices = listbox.curselection()
                    selected_values = [listbox.get(i) for i in selected_indices]
                    self.filter_value_var.set(json.dumps(selected_values))  # 将多选值存储为JSON字符串
                    self.refresh_analysis()
                
                listbox.bind('<<ListboxSelect>>', on_select)
                self.current_filter_widgets.append(listbox)
                
                # 添加搜索框
                search_frame = ttk.Frame(self.filter_container)
                search_frame.pack(fill=tk.X, pady=(0, 5))
                self.current_filter_widgets.append(search_frame)
                
                search_var = tk.StringVar()
                search_entry = ttk.Entry(search_frame, textvariable=search_var)
                search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
                
                def filter_list(*args):
                    search_text = search_var.get().lower()
                    listbox.delete(0, tk.END)
                    for value in unique_values:
                        if pd.notna(value) and search_text in str(value).lower():
                            listbox.insert(tk.END, value)
                
                search_var.trace_add("write", filter_list)
                
            # 对于数值型数据，显示范围选择
            elif selected_column in ["开盘价", "收盘价", "最高价", "最低价", "成交量", "成交额", 
                                  "涨跌幅(%)", "振幅(%)", "换手率(%)"]:
                # 创建数值范围选择
                range_frame = ttk.Frame(self.filter_container)
                range_frame.pack(fill=tk.X, pady=5)
                self.current_filter_widgets.append(range_frame)
                
                # 条件选择
                self.filter_type_var = tk.StringVar(value="greater")
                ttk.Radiobutton(range_frame, text="大于", 
                              variable=self.filter_type_var, 
                              value="greater").pack(side=tk.LEFT)
                ttk.Radiobutton(range_frame, text="小于", 
                              variable=self.filter_type_var, 
                              value="less").pack(side=tk.LEFT)
                ttk.Radiobutton(range_frame, text="等于", 
                              variable=self.filter_type_var, 
                              value="equals").pack(side=tk.LEFT)
                
                # 数值输入
                value_frame = ttk.Frame(self.filter_container)
                value_frame.pack(fill=tk.X, pady=5)
                self.current_filter_widgets.append(value_frame)
                
                ttk.Label(value_frame, text="值:").pack(side=tk.LEFT)
                value_entry = ttk.Entry(value_frame, textvariable=self.filter_value_var)
                value_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
                
                # 绑定事件
                self.filter_type_var.trace_add("write", self.refresh_analysis)
                
            # 对于其他类型的数据，显示默认的文本搜索
            else:
                # 创建默认的文本搜索
                search_frame = ttk.Frame(self.filter_container)
                search_frame.pack(fill=tk.X, pady=5)
                self.current_filter_widgets.append(search_frame)
                
                ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
                search_entry = ttk.Entry(search_frame, textvariable=self.filter_value_var)
                search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 绑定列选择变更事件
        self.filter_column_var.trace_add("write", update_filter_ui)
        
        # 初始化筛选值变量
        self.filter_value_var = tk.StringVar()
        self.filter_value_var.trace_add("write", self.refresh_analysis)
        
        # 初始化界面
        update_filter_ui()
        
    def _add_display_options(self):
        """添加显示选项部分"""
        display_frame = ttk.LabelFrame(self.config_frame, text="显示选项", padding=10)
        display_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 图表类型选择
        ttk.Label(display_frame, text="图表类型:").pack(anchor=tk.W)
        chart_types = [("柱状图", "bar"), ("折线图", "line"), ("饼图", "pie")]
        
        for text, value in chart_types:
            ttk.Radiobutton(display_frame, text=text,
                          variable=self.chart_type_var,
                          value=value).pack(anchor=tk.W)
        
        # 显示选项
        self.show_table_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_frame, text="显示数据表格",
                       variable=self.show_table_var).pack(anchor=tk.W)
        
        self.show_chart_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_frame, text="显示数据图表",
                       variable=self.show_chart_var).pack(anchor=tk.W)
        
        # 绑定显示选项变更事件
        self.chart_type_var.trace_add("write", self.refresh_analysis)
        self.show_table_var.trace_add("write", self.refresh_analysis)
        self.show_chart_var.trace_add("write", self.refresh_analysis)
        
    def _add_action_buttons(self):
        """添加操作按钮部分"""
        action_frame = ttk.Frame(self.config_frame)
        action_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 创建风格化按钮
        style = ttk.Style()
        style.configure("Accent.TButton", background="#4a7dff")
        
        # 导出按钮
        export_button = ttk.Button(action_frame, 
                                 text="导出数据", 
                                 style="Accent.TButton",
                                 command=self.export_data)
        export_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清理缓存按钮
        clear_cache_button = ttk.Button(action_frame,
                                      text="清理缓存",
                                      command=self.clear_cache)
        clear_cache_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 关闭按钮
        close_button = ttk.Button(action_frame, 
                                text="关闭", 
                                command=self.analysis_window.destroy)
        close_button.pack(side=tk.LEFT)
        
    def _on_frame_configure(self, event):
        """处理框架大小变化事件"""
        self.config_canvas.configure(scrollregion=self.config_canvas.bbox("all"))
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.config_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def refresh_analysis(self, *args):
        """刷新分析结果"""
        try:
            # 获取选择的列和筛选条件
            analysis_column = self.analysis_column_var.get()
            filter_column = self.filter_column_var.get()
            filter_value = self.filter_value_var.get()
            
            if not analysis_column or not filter_column:
                return
                
            # 应用筛选条件
            filtered_data = self._apply_filter(filter_column, filter_value)
            
            # 更新统计信息
            self._update_stats(filtered_data)
            
            # 更新表格显示
            if self.show_table_var.get():
                self._update_table(filtered_data)
                
            # 更新图表显示
            if self.show_chart_var.get():
                self._update_chart(filtered_data, analysis_column)
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新分析结果时出错: {str(e)}")
            
    def _apply_filter(self, column, filter_value):
        """应用筛选条件"""
        if not filter_value or filter_value.strip() == "":
            return self.filtered_df
            
        try:
            filtered_data = self.filtered_df.copy()
            
            # 应用其他筛选条件
            if column in ["开盘价", "收盘价", "最高价", "最低价", "成交量", "成交额", 
                         "涨跌幅(%)", "振幅(%)", "换手率(%)"]:
                try:
                    value = float(filter_value)
                    filter_type = self.filter_type_var.get()
                    if filter_type == "greater":
                        filtered_data = filtered_data[filtered_data[column] > value]
                    elif filter_type == "less":
                        filtered_data = filtered_data[filtered_data[column] < value]
                    else:  # equals
                        filtered_data = filtered_data[filtered_data[column] == value]
                except ValueError:
                    pass
            elif column in ["申万板块", "股票名称", "行业"]:
                try:
                    selected_values = json.loads(filter_value)
                    if selected_values:
                        filtered_data = filtered_data[filtered_data[column].isin(selected_values)]
                except json.JSONDecodeError:
                    pass
            else:
                escaped_value = re.escape(filter_value)
                filtered_data = filtered_data[filtered_data[column].astype(str).str.contains(escaped_value, case=False)]
            
            return filtered_data
            
        except Exception as e:
            messagebox.showerror("错误", f"应用筛选条件时出错: {str(e)}")
            return self.filtered_df
            
    def _update_stats(self, filtered_data):
        """更新统计信息"""
        analysis_column = self.analysis_column_var.get()
        
        # 基础统计
        stats_text = f"总记录数: {len(filtered_data)}"
        if len(filtered_data) != len(self.filtered_df):
            stats_text += f" (筛选前: {len(self.filtered_df)})"
            
        # 数值型列的统计信息
        if analysis_column in ["开盘价", "收盘价", "最高价", "最低价", "成交量", "成交额", 
                             "涨跌幅(%)", "振幅(%)", "换手率(%)"]:
            mean_val = filtered_data[analysis_column].mean()
            median_val = filtered_data[analysis_column].median()
            std_val = filtered_data[analysis_column].std()
            stats_text += f"\n平均值: {mean_val:.2f} | 中位数: {median_val:.2f} | 标准差: {std_val:.2f}"
            
        self.stats_info.config(text=stats_text)
        
    def _update_table(self, filtered_data):
        """更新表格显示"""
        self.ui.update_table(filtered_data, filtered_data.columns.tolist())
        
    def _update_chart(self, filtered_data, column):
        """更新图表显示"""
        self.ui.update_chart(filtered_data, column, self.chart_type_var.get())
        
    def export_data(self):
        """导出数据到Excel"""
        file_path = filedialog.asksaveasfilename(
            title="导出数据",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")]
        )
        
        if file_path:
            try:
                # 获取当前筛选后的数据
                filter_column = self.filter_column_var.get()
                filter_value = self.filter_value_var.get()
                filtered_data = self._apply_filter(filter_column, filter_value)
                
                # 导出到Excel
                filtered_data.to_excel(file_path, index=False)
                messagebox.showinfo("成功", f"数据已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出数据时出错: {str(e)}")
                
    def _start_data_query(self):
        """开始数据查询"""
        try:
            # 获取时间范围
            time_range = self.time_range_var.get()
            days = int(time_range.replace('d', ''))
            current_time = datetime.now()
            
            # 调整结束日期到最近的交易日
            end_date = current_time
            while end_date.weekday() >= 5:  # 如果是周末，往前调整
                end_date -= timedelta(days=1)
            
            # 计算开始日期，考虑工作日
            start_date = end_date
            trading_days = 0
            while trading_days < days:
                start_date -= timedelta(days=1)
                if start_date.weekday() < 5:  # 0-4 表示周一到周五
                    trading_days += 1
            
            # 获取涨幅条件
            price_change_type = self.price_change_type_var.get()
            price_change_direction = self.price_change_direction_var.get()
            threshold = float(self.price_change_threshold_var.get())
            
            print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"查询时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            print(f"交易日数量: {trading_days} 天")
            print(f"涨幅类型: {price_change_type}, 方向: {price_change_direction}, 阈值: {threshold}")
            
            # 初始化进度条
            self.ui.update_progress(0)
            
            # 用于存储所有股票的数据
            all_stock_data = []
            
            # 获取筛选后的股票代码
            filter_column = self.filter_column_var.get()
            filter_value = self.filter_value_var.get()
            filtered_df = self._apply_filter(filter_column, filter_value)
            stock_codes = filtered_df['股票代码'].unique()
            
            print(f"筛选后的股票数量: {len(stock_codes)}")
            if len(stock_codes) == 0:
                messagebox.showwarning("警告", "没有符合条件的股票")
                return
                
            for i, stock_code in enumerate(stock_codes):
                try:
                    # 更新进度条
                    progress = (i + 1) / len(stock_codes) * 40  # 数据获取占40%进度
                    self.ui.update_progress(progress)
                    
                    print(f"\n处理股票 {stock_code} ({i+1}/{len(stock_codes)})")
                    
                    # 检查缓存
                    cache_key = self._get_cache_key(start_date, end_date, price_change_type,
                                                  price_change_direction, threshold)
                    cached_data = self._get_cached_data(cache_key)
                    
                    if cached_data is not None:
                        print(f"使用缓存数据: {len(cached_data)} 条记录")
                        print(f"缓存数据时间范围: {cached_data.index.min()} 到 {cached_data.index.max()}")
                        stock_data = cached_data
                    else:
                        # 获取股票数据
                        print(f"从新浪财经获取数据: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                        stock_data = self.data_provider.get_stock_data(
                            stock_code,
                            start_date.strftime('%Y-%m-%d'),
                            end_date.strftime('%Y-%m-%d')
                        )
                        
                        print(f"获取到原始数据: {len(stock_data)} 条记录")
                        print(f"原始数据时间范围: {stock_data.index.min()} 到 {stock_data.index.max()}")
                        
                        # 验证数据
                        if not self.data_processor.validate_stock_data(stock_data):
                            print(f"股票 {stock_code} 数据无效")
                            continue
                            
                        # 统一列名
                        stock_data = self.data_processor.standardize_column_names(stock_data)
                        
                        # 过滤时间范围
                        stock_data = stock_data[
                            (stock_data.index >= start_date) & 
                            (stock_data.index <= end_date)
                        ]
                        
                        print(f"过滤后的数据: {len(stock_data)} 条记录")
                        print(f"过滤后的时间范围: {stock_data.index.min()} 到 {stock_data.index.max()}")
                        
                        # 检查交易日数量
                        if len(stock_data) < trading_days:
                            print(f"警告: 股票 {stock_code} 数据不完整，期望 {trading_days} 个交易日，实际只有 {len(stock_data)} 个交易日")
                            continue
                        
                        # 更新缓存
                        self._update_cache(cache_key, stock_data)
                    
                    if stock_data is not None and not stock_data.empty:
                        # 确保数据按时间正序排列
                        stock_data = stock_data.sort_index()
                        
                        # 计算涨跌幅
                        if price_change_type == "daily":
                            # 计算涨跌幅
                            stock_data['涨跌幅'] = stock_data['Close'].pct_change() * 100
                            
                            # 打印详细的涨跌幅数据
                            print(f"\n股票 {stock_code} 涨跌幅详细数据:")
                            for date, row in stock_data.iterrows():
                                print(f"日期: {date.strftime('%Y-%m-%d')}")
                                print(f"收盘价: {row['Close']:.2f}")
                                print(f"涨跌幅: {row['涨跌幅']:.2f}%")
                                print("---")
                            
                            print(f"\n股票 {stock_code} 涨跌幅统计:")
                            print(f"最大涨跌幅: {stock_data['涨跌幅'].max():.2f}%")
                            print(f"最小涨跌幅: {stock_data['涨跌幅'].min():.2f}%")
                            print(f"平均涨跌幅: {stock_data['涨跌幅'].mean():.2f}%")
                            print(f"阈值: {threshold}%")
                            
                            # 忽略 NaN 值进行比较
                            if price_change_direction == "up":
                                condition = stock_data['涨跌幅'].dropna() >= threshold
                            else:
                                condition = stock_data['涨跌幅'].dropna() <= -threshold
                                
                            if condition.any():
                                print(f"股票 {stock_code} 满足单日涨跌幅条件")
                                print(f"满足条件的日期:")
                                for date, row in stock_data[condition].iterrows():
                                    print(f"- {date.strftime('%Y-%m-%d')}: {row['涨跌幅']:.2f}%")
                                all_stock_data.append(stock_data)
                            else:
                                print(f"股票 {stock_code} 不满足涨跌幅条件")
                                
                        elif price_change_type == "period":
                            first_price = stock_data['Close'].iloc[0]
                            last_price = stock_data['Close'].iloc[-1]
                            period_change = (last_price - first_price) / first_price * 100
                            print(f"股票 {stock_code} 区间涨跌幅计算:")
                            print(f"起始价格: {first_price:.2f}")
                            print(f"结束价格: {last_price:.2f}")
                            print(f"区间涨跌幅: {period_change:.2f}%")
                            print(f"阈值: {threshold}%")
                            if (price_change_direction == "up" and period_change >= threshold) or \
                               (price_change_direction == "down" and period_change <= -threshold):
                                print(f"股票 {stock_code} 满足区间涨跌幅条件")
                                all_stock_data.append(stock_data)
                                
                        elif price_change_type == "max":
                            first_price = stock_data['Close'].iloc[0]
                            max_price = stock_data['Close'].max()
                            max_change = (max_price - first_price) / first_price * 100
                            print(f"股票 {stock_code} 最高涨幅计算:")
                            print(f"起始价格: {first_price:.2f}")
                            print(f"最高价格: {max_price:.2f}")
                            print(f"最高涨幅: {max_change:.2f}%")
                            print(f"阈值: {threshold}%")
                            if (price_change_direction == "up" and max_change >= threshold) or \
                               (price_change_direction == "down" and max_change <= -threshold):
                                print(f"股票 {stock_code} 满足最高涨幅条件")
                                all_stock_data.append(stock_data)
                                
                        elif price_change_type == "min":
                            first_price = stock_data['Close'].iloc[0]
                            min_price = stock_data['Close'].min()
                            min_change = (min_price - first_price) / first_price * 100
                            print(f"股票 {stock_code} 最低涨幅计算:")
                            print(f"起始价格: {first_price:.2f}")
                            print(f"最低价格: {min_price:.2f}")
                            print(f"最低涨幅: {min_change:.2f}%")
                            print(f"阈值: {threshold}%")
                            if (price_change_direction == "up" and min_change >= threshold) or \
                               (price_change_direction == "down" and min_change <= -threshold):
                                print(f"股票 {stock_code} 满足最低涨幅条件")
                                all_stock_data.append(stock_data)
                                
                except Exception as e:
                    print(f"获取股票 {stock_code} 数据时出错: {str(e)}")
                    continue
            
            print(f"\n查询完成，找到 {len(all_stock_data)} 个符合条件的股票")
            
            # 更新进度条
            self.ui.update_progress(50)
            
            if not all_stock_data:
                messagebox.showwarning("警告", "没有找到符合条件的数据")
                return
                
            # 合并所有股票数据
            filtered_data = pd.concat(all_stock_data, ignore_index=True)
            print(f"合并后的数据: {len(filtered_data)} 条记录")
            
            # 根据涨幅类型添加排序列
            if price_change_type == "daily":
                sort_column = "涨跌幅"
            elif price_change_type == "period":
                filtered_data['区间涨跌幅'] = filtered_data.groupby('股票代码')['Close'].transform(
                    lambda x: (x.iloc[-1] - x.iloc[0]) / x.iloc[0] * 100)
                sort_column = "区间涨跌幅"
            elif price_change_type == "max":
                filtered_data['最高涨幅'] = filtered_data.groupby('股票代码')['Close'].transform(
                    lambda x: (x.max() - x.iloc[0]) / x.iloc[0] * 100)
                sort_column = "最高涨幅"
            elif price_change_type == "min":
                filtered_data['最低涨幅'] = filtered_data.groupby('股票代码')['Close'].transform(
                    lambda x: (x.min() - x.iloc[0]) / x.iloc[0] * 100)
                sort_column = "最低涨幅"
            
            print(f"排序列: {sort_column}")
            
            # 更新进度条
            self.ui.update_progress(80)
            
            # 排序
            if price_change_direction == "up":
                filtered_data = filtered_data.sort_values(sort_column, ascending=False)
            else:
                filtered_data = filtered_data.sort_values(sort_column, ascending=True)
            
            # 更新数据
            self.filtered_df = filtered_data
            
            # 刷新显示
            self.refresh_analysis()
            
            # 完成进度
            self.ui.update_progress(100)
            
            # 获取实际符合条件的股票数量
            unique_stocks = filtered_data['股票代码'].unique()
            total_stocks = len(unique_stocks)
            total_records = len(filtered_data)
            
            print(f"最终结果: {total_stocks} 个股票，{total_records} 条记录")
            messagebox.showinfo("成功", f"数据查询完成，共找到 {total_stocks} 个股票，{total_records} 条记录")
            
        except Exception as e:
            print(f"查询过程出错: {str(e)}")
            messagebox.showerror("错误", f"执行查询时出错: {str(e)}")
        finally:
            # 重置进度条
            self.ui.update_progress(0)
            
    def _get_cache_key(self, start_date, end_date, price_change_type, 
                      price_change_direction, threshold):
        """生成缓存键"""
        # 只使用时间范围作为缓存键
        return f"{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        
    def _is_cache_valid(self, cache_key):
        """检查缓存是否有效"""
        if cache_key not in self.cache_timestamps:
            return False
        current_time = time.time()
        return current_time - self.cache_timestamps[cache_key] < self.cache_ttl
        
    def _update_cache(self, cache_key, data):
        """更新缓存"""
        self.cache[cache_key] = data
        self.cache_timestamps[cache_key] = time.time()
        
    def _get_cached_data(self, cache_key):
        """获取缓存数据"""
        if cache_key in self.cache and self._is_cache_valid(cache_key):
            return self.cache[cache_key]
        return None

    def clear_cache(self):
        """清理缓存数据"""
        try:
            # 记录清理前的缓存状态
            print(f"清理缓存前:")
            print(f"缓存数据条数: {len(self.cache)}")
            print(f"缓存时间戳条数: {len(self.cache_timestamps)}")
            
            # 清空缓存数据
            self.cache.clear()
            self.cache_timestamps.clear()
            
            # 记录清理后的缓存状态
            print(f"清理缓存后:")
            print(f"缓存数据条数: {len(self.cache)}")
            print(f"缓存时间戳条数: {len(self.cache_timestamps)}")
            
            # 显示清理结果
            messagebox.showinfo("成功", "缓存已清理")
        except Exception as e:
            print(f"清理缓存时出错: {str(e)}")
            messagebox.showerror("错误", f"清理缓存时出错: {str(e)}") 