import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from datetime import datetime, timedelta
import threading
from queue import Queue
from concurrent.futures import ThreadPoolExecutor

from .analysis_ui import AnalysisUI
from .data_processor import DataProcessor
from src.data_providers.stock_data_provider import StockDataProviderFactory

class StockAnalyzer:
    def __init__(self, parent, stock_data):
        self.parent = parent
        self.stock_data = stock_data
        self.ui = AnalysisUI(parent)
        self.data_processor = DataProcessor()
        self.analysis_window = None
        self.selected_stocks = set()
        self.data_provider = StockDataProviderFactory.get_provider('sina')
        self.max_stocks = 20
        self.loading_data = False
        self.result_queue = Queue()
        self.is_analyzing = False
        
    def create_analysis_window(self):
        """创建股票分析窗口"""
        if self.stock_data is None or len(self.stock_data) == 0:
            messagebox.showinfo("提示", "没有数据可用于分析")
            return
            
        # 创建分析窗口
        if self.analysis_window and self.analysis_window.winfo_exists():
            self.analysis_window.lift()
            return
            
        self.analysis_window = self.ui.create_window("股票涨幅分析", "1000x800")
        
        # 创建主框架
        self._create_main_frame()
        
    def _create_main_frame(self):
        """创建主框架"""
        # 创建主分割框架
        main_paned = ttk.PanedWindow(self.analysis_window, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左侧股票选择面板
        left_frame = self._create_stock_selection_panel(main_paned)
        
        # 创建右侧分析面板
        right_frame = self._create_analysis_panel(main_paned)
        
        # 添加面板到分割窗口
        main_paned.add(left_frame, weight=1)
        main_paned.add(right_frame, weight=2)
        
    def _create_stock_selection_panel(self, parent):
        """创建股票选择面板"""
        # 创建选择面板框架
        selection_frame = ttk.LabelFrame(parent, text="股票选择", padding=10)
        selection_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建搜索框
        search_frame = ttk.Frame(selection_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace_add("write", self._filter_stocks)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 创建股票列表
        list_frame = ttk.Frame(selection_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建垂直滚动条
        y_scrollbar = ttk.Scrollbar(list_frame)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建水平滚动条
        x_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建股票列表
        self.stock_list = ttk.Treeview(list_frame, 
                                     yscrollcommand=y_scrollbar.set,
                                     xscrollcommand=x_scrollbar.set,
                                     selectmode="extended")
        self.stock_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        y_scrollbar.config(command=self.stock_list.yview)
        x_scrollbar.config(command=self.stock_list.xview)
        
        # 设置列
        columns = ["股票代码", "股票名称", "当前价格", "涨跌幅(%)"]
        self.stock_list["columns"] = columns
        self.stock_list["show"] = "headings"
        
        # 设置列标题
        for col in columns:
            self.stock_list.heading(col, text=col)
            self.stock_list.column(col, width=100)
            
        # 绑定选择事件
        self.stock_list.bind("<<TreeviewSelect>>", self._on_stock_select)
        
        # 填充股票列表
        self._populate_stock_list()
        
        # 创建按钮框架
        button_frame = ttk.Frame(selection_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 全选按钮
        ttk.Button(button_frame, text="全选", 
                  command=self._select_all).pack(side=tk.LEFT, padx=5)
        
        # 取消全选按钮
        ttk.Button(button_frame, text="取消全选", 
                  command=self._deselect_all).pack(side=tk.LEFT, padx=5)
        
        return selection_frame
        
    def _create_analysis_panel(self, parent):
        """创建分析面板"""
        # 创建分析面板框架
        analysis_frame = ttk.LabelFrame(parent, text="分析设置", padding=10)
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        
        # 时间范围选择
        ttk.Label(analysis_frame, text="分析时间范围:").pack(anchor=tk.W)
        self.period_var = tk.StringVar(value="5d")
        period_frame = ttk.Frame(analysis_frame)
        period_frame.pack(fill=tk.X, pady=5)
        ttk.Radiobutton(period_frame, text="最近5天", 
                       variable=self.period_var, 
                       value="5d").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(period_frame, text="最近10天", 
                       variable=self.period_var, 
                       value="10d").pack(side=tk.LEFT, padx=5)
        
        # 涨幅阈值设置
        ttk.Label(analysis_frame, text="涨幅阈值(%):").pack(anchor=tk.W, pady=(10, 0))
        self.threshold_var = tk.StringVar(value="5")
        ttk.Entry(analysis_frame, textvariable=self.threshold_var, width=10).pack(anchor=tk.W)
        
        # 分析按钮
        self.analyze_button = ttk.Button(analysis_frame, text="开始分析", 
                                       command=self._start_analysis)
        self.analyze_button.pack(pady=10)
        
        # 创建结果显示区域
        result_frame = ttk.LabelFrame(analysis_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 创建结果表格
        self._create_result_table(result_frame)
        
        # 启动结果更新线程
        self._start_result_update_thread()
        
    def _populate_stock_list(self):
        """填充股票列表"""
        if self.loading_data:
            return
            
        self.loading_data = True
        
        # 获取最新的股票数据
        latest_data = self.stock_data.groupby('股票代码').last().reset_index()
        
        # 限制显示前20个股票
        latest_data = latest_data.head(self.max_stocks)
        
        # 计算涨跌幅
        latest_data['涨跌幅(%)'] = latest_data.apply(
            lambda x: self.data_processor.calculate_price_changes(
                self.stock_data[self.stock_data['股票代码'] == x['股票代码']]), axis=1)
        
        # 填充列表
        for _, row in latest_data.iterrows():
            self.stock_list.insert("", "end", values=[
                row['股票代码'],
                row['股票名称'],
                f"{row['收盘价']:.2f}",
                f"{row['涨跌幅(%)']:.2f}"
            ])
            
        self.loading_data = False
        
    def _filter_stocks(self, *args):
        """根据搜索条件过滤股票列表"""
        if self.loading_data:
            return
            
        search_text = self.search_var.get().lower()
        
        # 清除现有项目
        for item in self.stock_list.get_children():
            self.stock_list.delete(item)
            
        # 获取最新的股票数据
        latest_data = self.stock_data.groupby('股票代码').last().reset_index()
        
        # 根据搜索条件过滤
        filtered_data = latest_data[
            (latest_data['股票代码'].str.lower().str.contains(search_text)) |
            (latest_data['股票名称'].str.lower().str.contains(search_text))
        ]
        
        # 限制显示前20个股票
        filtered_data = filtered_data.head(self.max_stocks)
        
        # 计算涨跌幅
        filtered_data['涨跌幅(%)'] = filtered_data.apply(
            lambda x: self.data_processor.calculate_price_changes(
                self.stock_data[self.stock_data['股票代码'] == x['股票代码']]), axis=1)
        
        # 填充列表
        for _, row in filtered_data.iterrows():
            self.stock_list.insert("", "end", values=[
                row['股票代码'],
                row['股票名称'],
                f"{row['收盘价']:.2f}",
                f"{row['涨跌幅(%)']:.2f}"
            ])
            
    def _on_stock_select(self, event):
        """处理股票选择事件"""
        selected_items = self.stock_list.selection()
        if len(selected_items) > self.max_stocks:
            messagebox.showwarning("警告", f"最多只能选择{self.max_stocks}个股票进行分析")
            # 取消多余的选择
            for item in selected_items[self.max_stocks:]:
                self.stock_list.selection_remove(item)
            selected_items = selected_items[:self.max_stocks]
            
        self.selected_stocks = {self.stock_list.item(item)['values'][0] 
                              for item in selected_items}
        
    def _select_all(self):
        """全选所有股票"""
        for item in self.stock_list.get_children():
            self.stock_list.selection_add(item)
        self._on_stock_select(None)
        
    def _deselect_all(self):
        """取消全选"""
        self.stock_list.selection_remove(self.stock_list.selection())
        self.selected_stocks.clear()
        
    def _start_analysis(self):
        """开始分析过程"""
        if self.is_analyzing:
            return
            
        if not self.selected_stocks:
            messagebox.showwarning("警告", "请先选择要分析的股票")
            return
            
        if len(self.selected_stocks) > self.max_stocks:
            messagebox.showwarning("警告", f"最多只能分析{self.max_stocks}个股票")
            return
            
        self.is_analyzing = True
        self.analyze_button.config(state='disabled')
        self.ui.update_progress(0)
        
        # 在新线程中执行分析
        thread = threading.Thread(target=self._analyze_stocks_thread)
        thread.daemon = True
        thread.start()
        
    def _analyze_stocks_thread(self):
        """在新线程中执行股票分析"""
        try:
            # 获取参数
            period = self.period_var.get()
            threshold = float(self.threshold_var.get())
            
            # 计算时间范围
            end_date = datetime.now()
            if period == "5d":
                start_date = end_date - timedelta(days=5)
            else:  # 10d
                start_date = end_date - timedelta(days=10)
                
            # 获取选中的股票数量
            total_stocks = len(self.selected_stocks)
            
            # 使用线程池处理数据
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                for i, stock_code in enumerate(self.selected_stocks):
                    future = executor.submit(self._analyze_single_stock, 
                                          stock_code, 
                                          start_date,
                                          end_date,
                                          threshold)
                    futures.append(future)
                    
                    # 更新进度
                    progress = (i + 1) / total_stocks * 100
                    self.ui.update_progress(progress)
                    
                # 收集结果
                results = []
                for future in futures:
                    result = future.result()
                    if result:
                        results.append(result)
                        
            # 将结果放入队列
            self.result_queue.put(results)
            
        except Exception as e:
            self.result_queue.put([])
            print(f"分析过程出错: {str(e)}")
        finally:
            self.is_analyzing = False
            self.analyze_button.config(state='normal')
            
    def _analyze_single_stock(self, stock_code, start_date, end_date, threshold):
        """分析单个股票"""
        try:
            # 获取股票数据
            stock_data = self._load_stock_data(stock_code)
            if stock_data is None or len(stock_data) < 2:
                return None
                
            # 计算涨幅
            current_price = stock_data['Close'].iloc[-1]
            base_price = stock_data['Close'].iloc[0]
            price_change = (current_price - base_price) / base_price * 100
            
            # 计算最高涨幅
            max_price = stock_data['Close'].max()
            max_change = (max_price - base_price) / base_price * 100
            max_change_date = stock_data.loc[stock_data['Close'] == max_price].index[0]
            
            # 如果涨幅超过阈值，返回结果
            if max_change >= threshold:
                return {
                    "股票代码": stock_code,
                    "股票名称": stock_data['股票名称'].iloc[0],
                    "当前价格": current_price,
                    "涨幅(%)": round(price_change, 2),
                    "最高涨幅(%)": round(max_change, 2),
                    "最高涨幅日期": max_change_date.strftime("%Y-%m-%d")
                }
        except Exception as e:
            print(f"分析股票 {stock_code} 时出错: {str(e)}")
        return None
        
    def _start_result_update_thread(self):
        """启动结果更新线程"""
        def update_results():
            while True:
                try:
                    results = self.result_queue.get(timeout=0.1)
                    if results is not None:
                        self._update_result_table(results)
                except:
                    continue
                    
        thread = threading.Thread(target=update_results, daemon=True)
        thread.start()
        
    def _create_result_table(self, parent):
        """创建结果表格"""
        self.result_table = self.ui.create_table(parent)
        
    def _update_result_table(self, results):
        """更新结果表格"""
        if not results:
            return
            
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 更新表格
        self.ui.update_table(df, df.columns.tolist())
        
    def _load_stock_data(self, stock_code):
        """加载单个股票的数据"""
        try:
            # 获取当前日期
            end_date = datetime.now()
            # 获取30天前的日期作为起始日期
            start_date = end_date - timedelta(days=30)
            
            # 获取股票数据
            data = self.data_provider.get_stock_data(
                stock_code,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if data is not None and not data.empty:
                # 统一列名
                data = self.data_processor.standardize_column_names(data)
                return data
            return None
        except Exception as e:
            print(f"加载股票 {stock_code} 数据时出错: {str(e)}")
            return None 