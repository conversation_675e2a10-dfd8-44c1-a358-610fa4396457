import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import sys
import os

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.ui.analysis import DataAnalyzer
from src.ui.data_analysis import DataAnalysis

class StandaloneAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("股票数据分析工具")
        self.root.geometry("1200x800")
        
        # 设置主题样式
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=3)
        self.style.configure("TFrame", background="#f5f5f5")
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 添加数据源选择按钮
        self.open_button = ttk.Button(self.toolbar_frame, text="打开Excel文件", command=self.open_file, width=15)
        self.open_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.sample_button = ttk.Button(self.toolbar_frame, text="加载示例数据", command=self.load_sample_data, width=15)
        self.sample_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 创建内容区域
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 初始化数据
        self.stock_data = None
        self.data_analysis = None
        
    def open_file(self):
        """打开Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 读取Excel文件
                self.stock_data = pd.read_excel(file_path)
                
                # 检查是否有股票代码列
                stock_code_col = None
                for col in self.stock_data.columns:
                    if col == '股票代码' or '代码' in col:
                        stock_code_col = col
                        break
                
                # 如果有股票代码列，将其作为字符串读取
                if stock_code_col is not None:
                    converters = {stock_code_col: str}
                    self.stock_data = pd.read_excel(file_path, converters=converters)
                
                # 创建数据分析实例
                self._create_data_analysis()
                self.root.title(f"股票数据分析工具 - {os.path.basename(file_path)}")
                
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")
    
    def load_sample_data(self):
        """加载示例数据"""
        try:
            # 获取示例数据文件路径
            sample_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils', 'sample_stock_data.xlsx')
            
            if os.path.exists(sample_data_path):
                # 读取示例数据
                self.stock_data = pd.read_excel(sample_data_path)
                
                # 创建数据分析实例
                self._create_data_analysis()
                self.root.title("股票数据分析工具 - 示例数据")
            else:
                messagebox.showwarning("警告", "示例数据文件不存在")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载示例数据时出错: {str(e)}")
    
    def _create_data_analysis(self):
        """创建数据分析实例"""
        # 清除现有内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 创建数据分析实例
        self.data_analysis = DataAnalysis(self.content_frame, self.stock_data)
        
        # 创建分析菜单
        menu = self.data_analysis.show_analysis_menu()
        
        # 创建按钮框架
        button_frame = ttk.Frame(self.content_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 添加分析按钮
        ttk.Button(button_frame, text="股票涨幅分析", 
                  command=self.data_analysis.create_stock_analysis_window).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="数据分析", 
                  command=self.data_analysis.create_data_analysis_window).pack(side=tk.LEFT)
        
        # 创建数据预览表格
        self._create_preview_table()
    
    def _create_preview_table(self):
        """创建数据预览表格"""
        # 创建表格框架
        table_frame = ttk.LabelFrame(self.content_frame, text="数据预览", padding="10")
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        tree = ttk.Treeview(table_frame)
        tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        vsb = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=vsb.set)
        
        hsb = ttk.Scrollbar(table_frame, orient="horizontal", command=tree.xview)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        tree.configure(xscrollcommand=hsb.set)
        
        # 设置列
        tree['columns'] = list(self.stock_data.columns)
        tree['show'] = 'headings'
        
        # 设置列标题
        for col in self.stock_data.columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)
        
        # 添加数据
        for i, row in self.stock_data.iterrows():
            tree.insert('', 'end', values=list(row))

def main():
    root = tk.Tk()
    app = StandaloneAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main() 