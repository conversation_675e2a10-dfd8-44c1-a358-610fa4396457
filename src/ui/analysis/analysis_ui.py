import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd

class AnalysisUI:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.progress_var = None
        self.progress_bar = None
        self.table = None
        self.chart_figure = None
        self.chart_canvas = None
        
    def create_window(self, title, size="1000x800"):
        """创建分析窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(title)
        self.window.geometry(size)
        self.window.transient(self.parent)
        self.window.configure(bg="#f5f5f5")
        
        # 创建进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.window, 
                                          variable=self.progress_var,
                                          maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        return self.window
        
    def create_table(self, parent):
        """创建数据表格"""
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建水平滚动条
        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建垂直滚动条
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建表格
        self.table = ttk.Treeview(table_frame, 
                                 xscrollcommand=x_scrollbar.set,
                                 yscrollcommand=y_scrollbar.set)
        self.table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        x_scrollbar.config(command=self.table.xview)
        y_scrollbar.config(command=self.table.yview)
        
        return self.table
        
    def create_chart(self, parent):
        """创建图表"""
        # 创建图表框架
        chart_frame = ttk.Frame(parent)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Figure和Canvas
        self.chart_figure = Figure(figsize=(6, 4), dpi=100, facecolor='#f5f5f5')
        self.chart_canvas = FigureCanvasTkAgg(self.chart_figure, chart_frame)
        self.chart_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        return self.chart_figure, self.chart_canvas
        
    def update_progress(self, value):
        """更新进度条"""
        if self.progress_var is not None:
            self.progress_var.set(value)
            
    def update_table(self, data, columns):
        """更新表格数据"""
        if self.table is None:
            return
            
        # 清除现有数据
        for item in self.table.get_children():
            self.table.delete(item)
            
        # 设置列
        self.table["columns"] = columns
        self.table["show"] = "headings"
        
        # 设置列标题
        for column in columns:
            self.table.heading(column, text=column)
            self.table.column(column, width=100)
            
        # 添加数据
        for idx, row in data.iterrows():
            self.table.insert("", "end", values=list(row))
            
    def update_chart(self, data, column, chart_type="bar"):
        """更新图表"""
        if self.chart_figure is None:
            return
            
        # 清除现有图表
        self.chart_figure.clear()
        
        # 创建子图
        ax = self.chart_figure.add_subplot(111)
        
        # 根据图表类型绘制
        if chart_type == "bar":
            value_counts = data[column].value_counts().head(15)
            value_counts.plot(kind="bar", ax=ax)
            ax.set_title(f"{column} 分布 (Top 15)")
            ax.set_xlabel("")
            ax.set_ylabel("数量")
            plt.xticks(rotation=45, ha='right')
            
        elif chart_type == "line":
            if column in ["开盘价", "收盘价", "最高价", "最低价", "成交量", "成交额", 
                         "涨跌幅(%)", "振幅(%)", "换手率(%)"]:
                data[column].hist(ax=ax, bins=30)
                ax.set_title(f"{column} 分布")
                ax.set_xlabel(column)
                ax.set_ylabel("频率")
            else:
                value_counts = data[column].value_counts().head(15)
                value_counts.plot(kind="line", ax=ax, marker='o')
                ax.set_title(f"{column} 趋势 (Top 15)")
                ax.set_xlabel("")
                ax.set_ylabel("数量")
                plt.xticks(rotation=45, ha='right')
                
        elif chart_type == "pie":
            value_counts = data[column].value_counts()
            if len(value_counts) > 8:
                others = pd.Series({'其他': value_counts[8:].sum()})
                value_counts = pd.concat([value_counts[:8], others])
                
            ax.pie(value_counts, labels=value_counts.index, autopct="%1.1f%%")
            ax.set_title(f"{column} 占比")
            
        # 调整布局
        self.chart_figure.tight_layout()
        
        # 重新绘制
        self.chart_canvas.draw() 