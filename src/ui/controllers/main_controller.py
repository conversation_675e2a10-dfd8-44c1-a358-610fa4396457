import tkinter as tk
from tkinter import messagebox, filedialog
import os

from ..models.stock_data_model import StockDataModel
from ..components.toolbar import Toolbar
from ..components.filter_panel import FilterPanel
from ..components.table_view import TableView
from ..services.external_service import ExternalServiceManager

class MainController:
    def __init__(self, root):
        self.root = root
        self.setup_root()
        
        # 初始化模型和服务
        self.model = StockDataModel()
        self.service = ExternalServiceManager()
        
        # 初始化UI组件
        self.setup_ui()
        self.setup_callbacks()
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        self.status_bar = tk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def setup_root(self):
        """设置主窗口"""
        self.root.title("股票数据预览工具")
        self.root.geometry("1200x600")
        
        # 设置主题样式
        style = tk.ttk.Style()
        style.configure("TButton", padding=6, relief="flat", background="#ccc")
        style.configure("TLabel", padding=3)
        style.configure("TFrame", background="#f5f5f5")
        
    def setup_ui(self):
        """设置UI组件"""
        # 创建主框架
        self.main_frame = tk.ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        self.toolbar = Toolbar(self.main_frame)
        
        # 创建筛选面板
        self.filter_panel = FilterPanel(self.main_frame)
        
        # 创建表格视图
        self.table_view = TableView(self.main_frame)
        
    def setup_callbacks(self):
        """设置回调函数"""
        # 工具栏回调
        toolbar_callbacks = {
            'open_file': self.open_file,
            'favorites': self.open_favorites,
            'kline': self.open_kline_viewer,
            'multi_kline': self.open_multi_kline_viewer,
            'sector_analysis': self.open_sector_analysis,
            'view_kline': self.view_custom_kline
        }
        self.toolbar.set_callbacks(toolbar_callbacks)
        
        # 筛选面板回调
        filter_callbacks = {
            'apply_filter': self.apply_filter,
            'reset_filter': self.reset_filter
        }
        self.filter_panel.set_callbacks(filter_callbacks)
        
        # 表格视图回调
        table_callbacks = {
            'motion_callback': self.on_table_motion,
            'double_click_callback': self.on_table_double_click,
            'copy_callback': self.on_table_copy,
            'right_click_callback': self.on_table_right_click
        }
        self.table_view.set_callbacks(table_callbacks)
        
    def open_file(self):
        """打开Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            # 如果用户取消选择，尝试加载示例数据
            sample_data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils', 'sample_stock_data.xlsx')
            if os.path.exists(sample_data_path):
                file_path = sample_data_path
                messagebox.showinfo("提示", f"已加载示例数据: {os.path.basename(sample_data_path)}")
            else:
                return
        
        success, error = self.model.load_data(file_path)
        if success:
            self.filter_panel.set_columns(self.model.df.columns)
            self.table_view.display_data(
                self.model.filtered_df,
                self.model.current_sort_column,
                self.model.sort_ascending
            )
            self.root.title(f"股票数据预览工具 - {os.path.basename(file_path)}")
            self.status_var.set(f"已加载 {len(self.model.df)} 行数据")
        else:
            messagebox.showerror("错误", f"无法打开文件: {error}")
            
    def apply_filter(self):
        """应用筛选条件"""
        if self.model.df is None:
            messagebox.showinfo("提示", "请先打开Excel文件")
            return
            
        conditions = self.filter_panel.get_filter_conditions()
        if not conditions:
            messagebox.showinfo("提示", "请添加至少一个筛选条件")
            return
            
        success, error = self.model.apply_filter(conditions)
        if success:
            self.table_view.display_data(
                self.model.filtered_df,
                self.model.current_sort_column,
                self.model.sort_ascending
            )
            self.status_var.set(f"已筛选出 {len(self.model.filtered_df)} 条记录")
        else:
            messagebox.showerror("错误", f"筛选时发生错误: {error}")
            
    def reset_filter(self):
        """重置筛选条件"""
        if self.model.df is None:
            return
            
        self.filter_panel.reset()
        self.model.filtered_df = self.model.df.copy()
        self.table_view.display_data(
            self.model.filtered_df,
            self.model.current_sort_column,
            self.model.sort_ascending
        )
        self.status_var.set(f"已重置筛选 | 显示 {len(self.model.df)} 行数据")
        
    def open_kline_viewer(self):
        """打开K线图查看器"""
        success, error = self.service.open_kline_viewer()
        if success:
            self.status_var.set("已打开K线图查看器")
        else:
            messagebox.showerror("错误", f"无法打开K线图查看器: {error}")
            
    def open_multi_kline_viewer(self):
        """打开多股票K线图查看器"""
        success, error = self.service.open_multi_kline_viewer()
        if success:
            self.status_var.set("已打开多股票K线图查看器")
        else:
            messagebox.showerror("错误", f"无法打开多股票K线图查看器: {error}")
            
    def open_favorites(self):
        """打开自选股票管理界面"""
        success, error = self.service.open_favorites_manager()
        if success:
            self.status_var.set("已打开自选股票管理界面")
        else:
            messagebox.showerror("错误", f"无法打开自选股票管理界面: {error}")
            
    def open_sector_analysis(self):
        """打开板块分析工具"""
        if self.model.filtered_df is None or len(self.model.filtered_df) == 0:
            messagebox.showinfo("提示", "没有可分析的数据")
            return
            
        success, error = self.service.open_sector_analysis()
        if success:
            self.status_var.set("已打开板块分析工具")
        else:
            messagebox.showerror("错误", f"无法打开板块分析工具: {error}")
            
    def view_custom_kline(self):
        """查看用户输入的股票代码的K线图"""
        stock_code = self.toolbar.get_stock_code()
        
        if not stock_code:
            messagebox.showwarning("警告", "请输入股票代码")
            return
            
        success, error = self.service.open_kline_viewer(stock_code)
        if success:
            self.toolbar.clear_stock_code()
            self.status_var.set(f"已打开 {stock_code} 的K线图")
        else:
            messagebox.showerror("错误", f"无法打开K线图: {error}")
            
    def on_table_motion(self, event):
        """处理表格鼠标移动事件"""
        region = self.table_view.tree.identify_region(event.x, event.y)
        if region == "cell":
            item_id = self.table_view.tree.identify_row(event.y)
            col = self.table_view.tree.identify_column(event.x)
            if item_id and col:
                col_idx = int(col.replace('#', '')) - 1
                if 0 <= col_idx < len(self.model.filtered_df.columns):
                    col_name = self.model.filtered_df.columns[col_idx]
                    cell_value = self.table_view.tree.item(item_id, 'values')[col_idx]
                    self.status_var.set(f"列: {col_name} | 值: {cell_value}")
        else:
            if self.model.filtered_df is not None:
                self.status_var.set(f"显示 {len(self.model.filtered_df)} 行数据")
                
    def on_table_double_click(self, event):
        """处理表格双击事件"""
        region = self.table_view.tree.identify_region(event.x, event.y)
        if region == "cell":
            item = self.table_view.tree.identify_row(event.y)
            if item:
                # 获取股票代码列的索引
                stock_code_idx = -1
                for i, col in enumerate(self.model.filtered_df.columns):
                    if col == '股票代码' or '代码' in col:
                        stock_code_idx = i
                        break
                        
                if stock_code_idx == -1:
                    messagebox.showerror("错误", "无法找到股票代码列")
                    return
                    
                values = self.table_view.tree.item(item, 'values')
                stock_code = values[stock_code_idx]
                
                # 检查是否按住了Shift键
                if event.state & 0x0001:  # Shift键被按下
                    selected_items = self.table_view.tree.selection()
                    if selected_items:
                        stock_codes = []
                        for selected_item in selected_items[:8]:
                            code = self.table_view.tree.item(selected_item, 'values')[stock_code_idx]
                            stock_codes.append(code)
                            
                        if len(selected_items) > 8:
                            messagebox.showinfo("提示", "多股票K线图最多支持8个，将只显示前8个选中的股票。")
                            
                        success, error = self.service.open_multi_kline_viewer(stock_codes)
                        if success:
                            self.status_var.set(f"已打开多股票K线图: {', '.join(stock_codes)}")
                        else:
                            messagebox.showerror("错误", f"无法打开多股票K线图: {error}")
                else:
                    success, error = self.service.open_kline_viewer(stock_code)
                    if success:
                        self.status_var.set(f"已打开 {stock_code} 的K线图")
                    else:
                        messagebox.showerror("错误", f"无法打开K线图: {error}")
                        
    def on_table_copy(self, event):
        """处理表格复制事件"""
        selection = self.table_view.get_selection()
        if not selection:
            return
            
        # 获取选中的数据
        copy_text = []
        for item in selection:
            values = self.table_view.tree.item(item, 'values')
            copy_text.append('\t'.join(str(v) for v in values))
            
        if copy_text:
            # 将数据复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append('\n'.join(copy_text))
            self.status_var.set("已复制选中数据到剪贴板")
        
    def on_table_right_click(self, event):
        """处理表格右键点击事件"""
        # 确保点击位置在单元格上
        region = self.table_view.tree.identify_region(event.x, event.y)
        if region != "cell":
            return
            
        # 创建右键菜单
        menu = tk.Menu(self.root, tearoff=0)
        
        # 获取当前行的股票代码
        item = self.table_view.tree.identify_row(event.y)
        if item:
            # 获取股票代码列的索引
            stock_code_idx = -1
            for i, col in enumerate(self.model.filtered_df.columns):
                if col == '股票代码' or '代码' in col:
                    stock_code_idx = i
                    break
                    
            if stock_code_idx != -1:
                values = self.table_view.tree.item(item, 'values')
                stock_code = values[stock_code_idx]
                
                # 添加菜单项
                menu.add_command(label=f"查看 {stock_code} K线图",
                               command=lambda: self.service.open_kline_viewer(stock_code))
                menu.add_command(label=f"添加 {stock_code} 到自选",
                               command=lambda: self.add_to_favorites(stock_code))
                menu.add_separator()
            
        # 通用菜单项
        menu.add_command(label="复制选中", command=lambda: self.on_table_copy(None))
        
        # 显示菜单
        menu.post(event.x_root, event.y_root)
        
    def add_to_favorites(self, stock_code):
        """添加股票到自选列表"""
        # 获取股票名称（如果有）
        stock_name = None
        for i, col in enumerate(self.model.filtered_df.columns):
            if '名称' in col:
                row_idx = self.model.filtered_df[self.model.filtered_df['股票代码'] == stock_code].index[0]
                stock_name = self.model.filtered_df.iloc[row_idx][col]
                break
                
        success, error = self.service.add_to_favorites(stock_code, stock_name)
        if success:
            self.status_var.set(f"已添加 {stock_code} 到自选列表")
        else:
            messagebox.showerror("错误", f"无法添加到自选列表: {error}") 