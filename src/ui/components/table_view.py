import tkinter as tk
from tkinter import ttk

class TableView(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        self.create_table()
        
        # 绑定事件
        self.bind_events()
        
    def create_table(self):
        """创建表格"""
        # 创建滚动条
        y_scrollbar = ttk.Scrollbar(self)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        x_scrollbar = ttk.Scrollbar(self, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建树形视图
        self.tree = ttk.Treeview(self, selectmode="extended")
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        y_scrollbar.config(command=self.tree.yview)
        x_scrollbar.config(command=self.tree.xview)
        self.tree.configure(yscrollcommand=y_scrollbar.set,
                          xscrollcommand=x_scrollbar.set)
                          
        # 设置表格样式
        style = ttk.Style()
        style.configure("Treeview", rowheight=25)
        style.configure("Treeview.Heading", font=('Helvetica', 10, 'bold'))
        
    def bind_events(self):
        """绑定事件"""
        # 双击事件
        self.tree.bind("<Double-1>", self._on_double_click)
        
        # 鼠标移动事件
        self.tree.bind("<Motion>", self._on_motion)
        
        # 复制事件
        self.tree.bind("<Control-c>", self._on_copy)
        
        # 右键菜单事件
        self.tree.bind("<Button-3>", self._on_right_click)
        
        # 列标题点击事件
        self.tree.bind("<Button-1>", self._on_click)
        
    def display_data(self, df, sort_column=None, ascending=True):
        """显示数据"""
        # 清除现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 设置列
        self.tree["columns"] = list(df.columns)
        self.tree["show"] = "headings"
        
        # 配置列
        for column in df.columns:
            self.tree.heading(column, text=column,
                            command=lambda c=column: self._on_heading_click(c))
            # 根据内容设置列宽
            max_width = max(
                len(str(column)),
                df[column].astype(str).str.len().max() if len(df) > 0 else 0
            )
            self.tree.column(column, width=min(max_width * 10, 200))
            
        # 插入数据
        for idx, row in df.iterrows():
            values = [str(val) for val in row]
            self.tree.insert("", tk.END, values=values)
            
        # 如果指定了排序列，进行排序
        if sort_column and sort_column in df.columns:
            self.sort_by_column(sort_column, ascending)
            
    def sort_by_column(self, column, ascending=True):
        """按列排序"""
        # 获取所有项目
        items = [(self.tree.set(item, column), item) for item in self.tree.get_children("")]
        
        # 排序
        items.sort(reverse=not ascending)
        
        # 重新排列项目
        for index, (_, item) in enumerate(items):
            self.tree.move(item, "", index)
            
        # 更新列标题
        for col in self.tree["columns"]:
            if col == column:
                self.tree.heading(col, text=f"{col} {'↑' if ascending else '↓'}")
            else:
                self.tree.heading(col, text=col)
                
    def get_selection(self):
        """获取选中的项目"""
        return self.tree.selection()
        
    def set_callbacks(self, callbacks):
        """设置回调函数"""
        self._callbacks = callbacks
        
    def _on_double_click(self, event):
        """处理双击事件"""
        if 'double_click_callback' in self._callbacks:
            self._callbacks['double_click_callback'](event)
            
    def _on_motion(self, event):
        """处理鼠标移动事件"""
        if 'motion_callback' in self._callbacks:
            self._callbacks['motion_callback'](event)
            
    def _on_copy(self, event):
        """处理复制事件"""
        if 'copy_callback' in self._callbacks:
            self._callbacks['copy_callback'](event)
            
    def _on_right_click(self, event):
        """处理右键点击事件"""
        if 'right_click_callback' in self._callbacks:
            self._callbacks['right_click_callback'](event)
            
    def _on_click(self, event):
        """处理点击事件"""
        region = self.tree.identify_region(event.x, event.y)
        if region == "heading":
            column = self.tree.identify_column(event.x)
            column_name = self.tree["columns"][int(column.replace("#", "")) - 1]
            self.sort_by_column(column_name)
            
    def _on_heading_click(self, column):
        """处理列标题点击事件"""
        # 获取当前排序状态
        current_text = self.tree.heading(column)["text"]
        ascending = True if "↓" in current_text else False
        
        # 排序
        self.sort_by_column(column, ascending) 