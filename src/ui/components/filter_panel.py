import tkinter as tk
from tkinter import ttk

class FilterPanel(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.pack(fill=tk.X, padx=5, pady=5)
        
        # 初始化变量
        self.conditions = []
        self.columns = []
        
        # 创建筛选条件区域
        self.create_filter_area()
        
        # 创建按钮区域
        self.create_button_area()
        
    def create_filter_area(self):
        """创建筛选条件区域"""
        # 创建条件Frame
        self.conditions_frame = ttk.LabelFrame(self, text="筛选条件", padding="5")
        self.conditions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加条件按钮
        self.add_condition_btn = ttk.But<PERSON>(self.conditions_frame, text="添加条件", command=self.add_condition)
        self.add_condition_btn.pack(side=tk.TOP, pady=5)
        
    def create_button_area(self):
        """创建按钮区域"""
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 应用按钮
        self.apply_btn = ttk.Button(button_frame, text="应用筛选")
        self.apply_btn.pack(side=tk.LEFT, padx=5)
        
        # 重置按钮
        self.reset_btn = ttk.Button(button_frame, text="重置")
        self.reset_btn.pack(side=tk.LEFT)
        
    def add_condition(self):
        """添加新的筛选条件"""
        condition_frame = ttk.Frame(self.conditions_frame)
        condition_frame.pack(fill=tk.X, pady=2)
        
        # 列选择
        column_cb = ttk.Combobox(condition_frame, values=self.columns, width=15)
        column_cb.pack(side=tk.LEFT, padx=2)
        
        # 操作符选择
        operators = ["=", ">", "<", ">=", "<=", "!=", "包含", "不包含"]
        operator_cb = ttk.Combobox(condition_frame, values=operators, width=8)
        operator_cb.pack(side=tk.LEFT, padx=2)
        
        # 值输入框
        value_entry = ttk.Entry(condition_frame, width=15)
        value_entry.pack(side=tk.LEFT, padx=2)
        
        # 删除按钮
        delete_btn = ttk.Button(condition_frame, text="×", width=2,
                              command=lambda: self.delete_condition(condition_frame))
        delete_btn.pack(side=tk.LEFT, padx=2)
        
        # 保存条件组件
        self.conditions.append({
            'frame': condition_frame,
            'column': column_cb,
            'operator': operator_cb,
            'value': value_entry
        })
        
    def delete_condition(self, condition_frame):
        """删除筛选条件"""
        # 从列表中移除条件
        self.conditions = [c for c in self.conditions if c['frame'] != condition_frame]
        # 销毁条件框架
        condition_frame.destroy()
        
    def set_columns(self, columns):
        """设置可选列"""
        self.columns = list(columns)
        # 更新所有已存在的列选择框
        for condition in self.conditions:
            condition['column']['values'] = self.columns
            
    def get_filter_conditions(self):
        """获取所有筛选条件"""
        result = []
        for condition in self.conditions:
            column = condition['column'].get()
            operator = condition['operator'].get()
            value = condition['value'].get()
            
            if column and operator and value:
                result.append({
                    'column': column,
                    'operator': operator,
                    'value': value
                })
        return result
        
    def reset(self):
        """重置所有筛选条件"""
        for condition in self.conditions:
            condition['frame'].destroy()
        self.conditions = []
        
    def set_callbacks(self, callbacks):
        """设置按钮回调函数"""
        self.apply_btn.config(command=callbacks.get('apply_filter'))
        self.reset_btn.config(command=callbacks.get('reset_filter')) 