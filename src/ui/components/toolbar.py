import tkinter as tk
from tkinter import ttk

class Toolbar(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.pack(fill=tk.X, padx=5, pady=5)
        
        # 创建工具栏按钮
        self.create_buttons()
        
        # 创建股票代码输入框
        self.create_stock_code_entry()
        
    def create_buttons(self):
        """创建工具栏按钮"""
        # 打开文件按钮
        self.open_btn = ttk.But<PERSON>(self, text="打开文件")
        self.open_btn.pack(side=tk.LEFT, padx=2)
        
        # 自选股按钮
        self.favorites_btn = ttk.<PERSON><PERSON>(self, text="自选股")
        self.favorites_btn.pack(side=tk.LEFT, padx=2)
        
        # K线图按钮
        self.kline_btn = ttk.Button(self, text="K线图")
        self.kline_btn.pack(side=tk.LEFT, padx=2)
        
        # 多股票K线图按钮
        self.multi_kline_btn = ttk.Button(self, text="多股票K线")
        self.multi_kline_btn.pack(side=tk.LEFT, padx=2)
        
        # 板块分析按钮
        self.sector_btn = ttk.Button(self, text="板块分析")
        self.sector_btn.pack(side=tk.LEFT, padx=2)
        
    def create_stock_code_entry(self):
        """创建股票代码输入框"""
        # 创建Frame用于包含标签和输入框
        entry_frame = ttk.Frame(self)
        entry_frame.pack(side=tk.LEFT, padx=10)
        
        # 标签
        ttk.Label(entry_frame, text="股票代码:").pack(side=tk.LEFT)
        
        # 输入框
        self.stock_code_var = tk.StringVar()
        self.stock_code_entry = ttk.Entry(entry_frame, textvariable=self.stock_code_var, width=10)
        self.stock_code_entry.pack(side=tk.LEFT, padx=2)
        
        # 查看按钮
        self.view_btn = ttk.Button(entry_frame, text="查看")
        self.view_btn.pack(side=tk.LEFT, padx=2)
        
    def set_callbacks(self, callbacks):
        """设置按钮回调函数"""
        self.open_btn.config(command=callbacks.get('open_file'))
        self.favorites_btn.config(command=callbacks.get('favorites'))
        self.kline_btn.config(command=callbacks.get('kline'))
        self.multi_kline_btn.config(command=callbacks.get('multi_kline'))
        self.sector_btn.config(command=callbacks.get('sector_analysis'))
        self.view_btn.config(command=callbacks.get('view_kline'))
        
    def get_stock_code(self):
        """获取输入的股票代码"""
        return self.stock_code_var.get().strip()
        
    def clear_stock_code(self):
        """清空股票代码输入框"""
        self.stock_code_var.set("") 