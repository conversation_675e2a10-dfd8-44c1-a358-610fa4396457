"""
K线图组件模块
提供绘制K线图和成交量图的功能
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import tkinter as tk
from tkinter import ttk

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei',
                                  'Microsoft YaHei', 'WenQuanYi Micro Hei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题


class KLineChart:
    """K线图组件类，用于绘制股票K线图和成交量图"""
    
    def __init__(self, parent, width=9, height=6, dpi=100):
        """
        初始化K线图组件
        
        参数:
        parent: tkinter父容器
        width: 图表宽度（英寸）
        height: 图表高度（英寸）
        dpi: 图表分辨率
        """
        self.parent = parent
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.canvas = FigureCanvasTkAgg(self.fig, master=parent)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加导航工具栏
        self.toolbar = NavigationToolbar2Tk(self.canvas, parent)
        self.toolbar.update()
        
        # 存储绘图相关的数据
        self.data = None
        self.candle_data = []
        self.tooltip_annotation = None
        
    def plot_candlestick(self, data, title=None, show_tooltip=True):
        """
        绘制K线图和成交量图
        
        参数:
        data: DataFrame，包含OHLC和Volume数据
        title: 图表标题
        show_tooltip: 是否显示鼠标悬停提示
        """
        self.data = data
        self.fig.clear()
        
        # 创建子图 - 价格和成交量
        gs = self.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = self.fig.add_subplot(gs[0])
        ax2 = self.fig.add_subplot(gs[1], sharex=ax1)
        
        # 绘制K线图
        self._plot_candlestick(ax1, data)
        
        # 绘制成交量
        self._plot_volume(ax2, data)
        
        # 设置标题
        if title:
            self.fig.suptitle(title, fontsize=12, fontweight='bold')
        
        # 隐藏上图的x轴标签
        plt.setp(ax1.get_xticklabels(), visible=False)
        
        # 自动调整布局
        self.fig.tight_layout()
        
        # 更新画布
        self.canvas.draw()
        
        # 添加鼠标悬停提示
        if show_tooltip:
            self._add_tooltip(ax1)
            
        return ax1, ax2
    
    def _plot_candlestick(self, ax, data):
        """绘制K线图"""
        # 设置x轴日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        
        # 计算每个蜡烛的宽度
        width = 0.6
        
        # 存储K线数据和位置信息，用于鼠标悬停显示
        self.candle_data = []
        
        # 绘制K线
        for i, (idx, row) in enumerate(data.iterrows()):
            # 确定颜色（涨为红，跌为绿）- 中国市场习惯
            color = 'red' if row['Close'] >= row['Open'] else 'green'
            
            # 绘制实体
            rect = plt.Rectangle(
                (i - width/2, min(row['Open'], row['Close'])),
                width,
                abs(row['Close'] - row['Open']),
                facecolor=color,
                edgecolor=color,
                alpha=0.8
            )
            ax.add_patch(rect)
            
            # 绘制上下影线
            ax.plot([i, i], [row['Low'], min(row['Open'], row['Close'])],
                    color=color, linewidth=1)
            ax.plot([i, i], [max(row['Open'], row['Close']),
                    row['High']], color=color, linewidth=1)
            
            # 计算涨跌幅
            if i > 0:
                prev_close = data.iloc[i-1]['Close']
                change_pct = (row['Close'] - prev_close) / prev_close * 100
            else:
                change_pct = 0
                
            # 存储K线数据和位置信息
            self.candle_data.append({
                'index': i,
                'date': idx.strftime('%Y-%m-%d') if hasattr(idx, 'strftime') else str(idx),
                'open': row['Open'],
                'high': row['High'],
                'low': row['Low'],
                'close': row['Close'],
                'change_pct': change_pct,
                'volume': row['Volume'] if 'Volume' in row else 0,
                'x_range': (i - width/2, i + width/2),
                'y_range': (row['Low'], row['High'])
            })
        
        # 设置x轴刻度
        ax.set_xticks(range(0, len(data), max(1, len(data) // 10)))
        ax.set_xticklabels([data.index[i].strftime('%Y-%m-%d') if hasattr(data.index[i], 'strftime') else str(data.index[i])
                           for i in ax.get_xticks()])
        
        # 旋转x轴标签以避免重叠
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 标签
        ax.set_ylabel("价格", fontsize=10)
    
    def _plot_volume(self, ax, data):
        """绘制成交量图"""
        # 计算每个柱的宽度
        width = 0.6
        
        # 绘制成交量柱状图
        for i, (idx, row) in enumerate(data.iterrows()):
            # 确定颜色（涨为红，跌为绿）- 中国市场习惯
            color = 'red' if row['Close'] >= row['Open'] else 'green'
            
            # 绘制成交量柱
            ax.bar(i, row['Volume'], width=width, color=color, alpha=0.5)
        
        # 设置x轴刻度
        ax.set_xticks(range(0, len(data), max(1, len(data) // 10)))
        ax.set_xticklabels([data.index[i].strftime('%Y-%m-%d') if hasattr(data.index[i], 'strftime') else str(data.index[i])
                           for i in ax.get_xticks()])
        
        # 旋转x轴标签以避免重叠
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 标签
        ax.set_xlabel("日期", fontsize=10)
        ax.set_ylabel("成交量", fontsize=10)
    
    def _add_tooltip(self, ax):
        """添加鼠标悬停提示"""
        self.tooltip_annotation = ax.annotate('', 
                                         xy=(0, 0), 
                                         xytext=(10, 10),
                                         textcoords='offset points',
                                         bbox=dict(boxstyle='round,pad=0.5', fc='#e6f2ff', alpha=0.9, edgecolor='#99ccff'),
                                         visible=False)
        
        def hover(event):
            if event.inaxes == ax:
                for candle in self.candle_data:
                    x_min, x_max = candle['x_range']
                    y_min, y_max = candle['y_range']
                    
                    if x_min <= event.xdata <= x_max and y_min <= event.ydata <= y_max:
                        # 更新注释内容和位置
                        tooltip_text = (
                            f"日期: {candle['date']}\n"
                            f"开盘: {candle['open']:.2f}\n"
                            f"最高: {candle['high']:.2f}\n"
                            f"最低: {candle['low']:.2f}\n"
                            f"收盘: {candle['close']:.2f}\n"
                            f"涨跌幅: {candle['change_pct']:.2f}%\n"
                            f"成交量: {int(candle['volume'])}"
                        )
                        
                        self.tooltip_annotation.set_text(tooltip_text)
                        
                        # 调整提示框位置，防止超出右侧边界
                        # 获取图表的边界
                        fig_width = self.fig.get_figwidth() * self.fig.dpi
                        # 计算鼠标在图表中的相对位置
                        transform = ax.transData.transform
                        x_display, y_display = transform((event.xdata, event.ydata))
                        
                        # 如果鼠标位置靠近右侧边界，将提示框显示在左侧
                        if x_display > fig_width * 0.8:
                            self.tooltip_annotation.xytext = (-120, 10)
                        else:
                            self.tooltip_annotation.xytext = (10, 10)
                            
                        self.tooltip_annotation.xy = (event.xdata, event.ydata)
                        self.tooltip_annotation.set_visible(True)
                        self.fig.canvas.draw_idle()
                        return
                
                # 如果鼠标不在任何K线上，隐藏注释
                if self.tooltip_annotation.get_visible():
                    self.tooltip_annotation.set_visible(False)
                    self.fig.canvas.draw_idle()
        
        def leave(event):
            if self.tooltip_annotation.get_visible():
                self.tooltip_annotation.set_visible(False)
                self.fig.canvas.draw_idle()
        
        # 连接事件处理函数
        self.fig.canvas.mpl_connect('motion_notify_event', hover)
        self.fig.canvas.mpl_connect('axes_leave_event', leave)
    
    def plot_cost_line(self, ax, cost_price):
        """
        在K线图上绘制成本线
        
        参数:
        ax: matplotlib轴对象
        cost_price: 成本价格
        """
        if cost_price <= 0 or self.data is None or self.data.empty:
            return
            
        # 绘制成本线
        ax.axhline(y=cost_price, color='blue', linestyle='-', linewidth=1.5, alpha=0.7)
        
        # 添加成本线标签
        current_price = self.data['Close'].iloc[-1]
        profit_pct = (current_price - cost_price) / cost_price * 100
        label_color = 'red' if profit_pct >= 0 else 'green'
        label_text = f"成本线: {cost_price:.2f} (当前盈亏: {profit_pct:.2f}%)"
        
        # 在右侧显示成本线标签
        ax.text(len(self.data)*0.99, cost_price*1.002,
                label_text,
                color=label_color, fontsize=9, fontweight='bold',
                horizontalalignment='right', verticalalignment='bottom',
                bbox=dict(facecolor='white', alpha=0.8, edgecolor=label_color, pad=2))
        
        # 绘制成本区域底色 (盈利区域为淡红色，亏损区域为淡绿色)
        if profit_pct >= 0:  # 盈利
            ax.axhspan(cost_price, current_price, color='red', alpha=0.05)
        else:  # 亏损
            ax.axhspan(current_price, cost_price, color='green', alpha=0.05)
        
        # 更新画布
        self.canvas.draw() 