"""
工具提示组件模块
"""
import tkinter as tk
from tkinter import ttk

def create_tooltip(widget, text, root):
    """
    为控件创建工具提示
    
    参数:
    widget: 要添加提示的控件
    text: 提示文本
    root: 主窗口对象
    """
    tooltip = None  # 先定义变量以便在闭包中使用
    
    def enter(event):
        nonlocal tooltip
        tooltip = tk.Toplevel(root)
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
        label = ttk.Label(tooltip, text=text,
                          background="#ffffe0", relief="solid", borderwidth=1)
        label.pack()

    def leave(event):
        nonlocal tooltip
        if tooltip:
            tooltip.destroy()
            tooltip = None

    widget.bind("<Enter>", enter)
    widget.bind("<Leave>", leave)
    
    return (enter, leave)  # 返回处理函数，以便于需要时解绑 