import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import re
import numpy as np
import os
from scipy.signal import find_peaks
from datetime import datetime
from PIL import Image, ImageGrab  # 添加PIL库用于保存图片和访问剪贴板
import io
import threading

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.data_providers.stock_data_provider import StockDataProviderFactory

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei',
                                   'Microsoft YaHei', 'WenQuanYi Micro Hei', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题

# 添加加载动画类
class LoadingAnimation:
    def __init__(self, canvas, x, y, size=20, color="blue"):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.size = size
        self.color = color
        self.angle = 0
        self.animation_id = None
        self.arc_id = None
        
    def start(self):
        if self.arc_id:
            self.canvas.delete(self.arc_id)
        self.arc_id = self.canvas.create_arc(
            self.x - self.size, self.y - self.size,
            self.x + self.size, self.y + self.size,
            start=self.angle, extent=60, outline=self.color, width=3, style="arc"
        )
        self.angle = (self.angle + 10) % 360
        self.animation_id = self.canvas.after(50, self.start)
        
    def stop(self):
        if self.animation_id:
            self.canvas.after_cancel(self.animation_id)
            self.animation_id = None
        if self.arc_id:
            self.canvas.delete(self.arc_id)
            self.arc_id = None


class MultiStockKLineApp:
    def __init__(self, root, initial_symbols=None):
        self.root = root
        self.root.title("多股票K线图查看器")
        self.root.geometry("1200x800")

        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部控制区域
        self.control_frame = ttk.Frame(self.main_frame)
        self.control_frame.pack(fill=tk.X, pady=(0, 10))

        # 股票代码输入框架 - 使用两行布局以容纳8个股票
        self.stock_codes_frame = ttk.LabelFrame(self.control_frame, text="股票代码")
        self.stock_codes_frame.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        # 创建两行框架来容纳股票输入框
        self.stock_row1 = ttk.Frame(self.stock_codes_frame)
        self.stock_row1.pack(fill=tk.X, expand=True)
        
        self.stock_row2 = ttk.Frame(self.stock_codes_frame)
        self.stock_row2.pack(fill=tk.X, expand=True)
        
        # 创建8个股票代码输入框
        self.stock_code_vars = []
        self.stock_code_entries = []
        
        for i in range(8):
            # 决定放在哪一行
            parent_frame = self.stock_row1 if i < 4 else self.stock_row2
            
            frame = ttk.Frame(parent_frame)
            frame.pack(side=tk.LEFT, padx=5, pady=5)
            
            ttk.Label(frame, text=f"股票 {i+1}:").pack(side=tk.LEFT, padx=(0, 5))
            var = tk.StringVar(value=initial_symbols[i] if initial_symbols and i < len(initial_symbols) else "")
            entry = ttk.Entry(frame, textvariable=var, width=10)
            entry.pack(side=tk.LEFT)
            self.create_tooltip(entry, f"输入股票{i+1}代码，如：600000或AAPL")
            
            self.stock_code_vars.append(var)
            self.stock_code_entries.append(entry)
            
            # 绑定回车键到查询功能
            entry.bind("<Return>", lambda event: self.fetch_and_plot())
            
            # 绑定Tab键优化导航
            entry_index = i  # 创建一个局部变量来保存当前索引
            entry.bind("<Tab>", lambda event, idx=entry_index: self.focus_next_entry(event, idx))
            entry.bind("<Shift-Tab>", lambda event, idx=entry_index: self.focus_prev_entry(event, idx))

        # 数据源选择
        self.settings_frame = ttk.LabelFrame(self.control_frame, text="设置")
        self.settings_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(self.settings_frame, text="数据源:").grid(row=0, column=0, padx=(5, 5), pady=5, sticky=tk.W)
        self.data_source_var = tk.StringVar(value="sina")
        self.data_source_combo = ttk.Combobox(self.settings_frame, textvariable=self.data_source_var,
                                             values=["yahoo", "sina"],
                                             state="readonly", width=6)
        self.data_source_combo.grid(row=0, column=1, padx=(0, 5), pady=5, sticky=tk.W)
        self.create_tooltip(self.data_source_combo, "选择数据源: Yahoo Finance或新浪财经")

        # 时间范围选择
        ttk.Label(self.settings_frame, text="时间范围:").grid(row=1, column=0, padx=(5, 5), pady=5, sticky=tk.W)
        self.period_var = tk.StringVar(value="3mo")
        self.period_combo = ttk.Combobox(self.settings_frame, textvariable=self.period_var,
                                         values=[
                                             "1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "max"],
                                         state="readonly", width=5)
        self.period_combo.grid(row=1, column=1, padx=(0, 5), pady=5, sticky=tk.W)
        self.create_tooltip(self.period_combo, "选择查询的时间范围")

        # 间隔选择
        ttk.Label(self.settings_frame, text="间隔:").grid(row=2, column=0, padx=(5, 5), pady=5, sticky=tk.W)
        self.interval_var = tk.StringVar(value="1d")
        self.interval_combo = ttk.Combobox(self.settings_frame, textvariable=self.interval_var,
                                           values=["1m", "2m", "5m", "15m", "30m", "60m",
                                                   "90m", "1h", "1d", "5d", "1wk", "1mo", "3mo"],
                                           state="readonly", width=5)
        self.interval_combo.grid(row=2, column=1, padx=(0, 5), pady=5, sticky=tk.W)
        self.create_tooltip(self.interval_combo, "选择K线图的时间间隔")

        # 显示阻力区选项
        self.show_resistance_var = tk.BooleanVar(value=True)
        self.show_resistance_check = ttk.Checkbutton(self.settings_frame, text="显示阻力区",
                                                     variable=self.show_resistance_var)
        self.show_resistance_check.grid(row=3, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W)
        self.create_tooltip(self.show_resistance_check, "是否在图表上显示阻力区、斐波那契回撤位和MACD背离")

        # 查询按钮
        self.button_frame = ttk.Frame(self.control_frame)
        self.button_frame.pack(side=tk.LEFT, padx=10)
        
        self.query_button = ttk.Button(
            self.button_frame, text="查询", command=self.fetch_and_plot)
        self.query_button.pack(side=tk.TOP, pady=(5, 5))
        
        self.clear_button = ttk.Button(
            self.button_frame, text="清空", command=self.clear_entries)
        self.clear_button.pack(side=tk.TOP, pady=(0, 5))
        
        # 添加刷新按钮
        self.refresh_button = ttk.Button(
            self.button_frame, text="刷新", command=self.refresh_data)
        self.refresh_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.refresh_button, "刷新当前显示的股票数据")
        
        # 添加导入/导出按钮
        self.import_button = ttk.Button(
            self.button_frame, text="导入代码", command=self.import_stock_codes)
        self.import_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.import_button, "从文本文件导入股票代码列表")
        
        self.export_button = ttk.Button(
            self.button_frame, text="导出代码", command=self.export_stock_codes)
        self.export_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.export_button, "将当前股票代码导出到文本文件")
        
        # 添加截图按钮
        self.screenshot_button = ttk.Button(
            self.button_frame, text="截图保存", command=self.save_screenshot)
        self.screenshot_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.screenshot_button, "保存当前显示的所有股票K线图为图片")
        
        # 添加剪贴板截图按钮
        self.clipboard_button = ttk.Button(
            self.button_frame, text="复制到剪贴板", command=self.copy_to_clipboard)
        self.clipboard_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.clipboard_button, "将当前显示的所有股票K线图复制到剪贴板")
        
        # 添加股票导航按钮
        self.nav_frame = ttk.Frame(self.button_frame)
        self.nav_frame.pack(side=tk.TOP, pady=(0, 5))
        
        self.prev_page_button = ttk.Button(
            self.nav_frame, text="◀ 上一组", command=self.show_prev_page, state="disabled", width=8)
        self.prev_page_button.pack(side=tk.LEFT, padx=(0, 2))
        self.create_tooltip(self.prev_page_button, "显示上一组股票代码")
        
        self.next_page_button = ttk.Button(
            self.nav_frame, text="下一组 ▶", command=self.show_next_page, state="disabled", width=8)
        self.next_page_button.pack(side=tk.LEFT)
        self.create_tooltip(self.next_page_button, "显示下一组股票代码")
        
        # 添加批量输入按钮
        self.batch_input_button = ttk.Button(
            self.button_frame, text="批量输入", command=self.show_batch_input_dialog)
        self.batch_input_button.pack(side=tk.TOP, pady=(0, 5))
        self.create_tooltip(self.batch_input_button, "批量输入多个股票代码")
        
        # 初始化导入的股票代码列表和当前页码
        self.all_imported_codes = []
        self.current_page = 0

        # 创建图表区域 - 添加滚动条支持
        self.chart_container = ttk.Frame(self.main_frame)
        self.chart_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建垂直滚动条
        self.v_scrollbar = ttk.Scrollbar(self.chart_container, orient=tk.VERTICAL)
        self.v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建水平滚动条
        self.h_scrollbar = ttk.Scrollbar(self.chart_container, orient=tk.HORIZONTAL)
        self.h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建画布用于滚动
        self.canvas = tk.Canvas(self.chart_container, 
                               yscrollcommand=self.v_scrollbar.set,
                               xscrollcommand=self.h_scrollbar.set)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        self.v_scrollbar.config(command=self.canvas.yview)
        self.h_scrollbar.config(command=self.canvas.xview)
        
        # 创建图表框架
        self.chart_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), window=self.chart_frame, anchor="nw")
        
        # 绑定事件以更新滚动区域
        self.chart_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # 添加鼠标滚轮支持
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)  # Windows
        self.canvas.bind("<Button-4>", self.on_mousewheel)    # Linux上滚
        self.canvas.bind("<Button-5>", self.on_mousewheel)    # Linux下滚

        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        self.status_bar = ttk.Label(
            root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 初始化图表
        self.fig = Figure(figsize=(12, 8), dpi=100)
        self.mpl_canvas = FigureCanvasTkAgg(self.fig, master=self.chart_frame)
        self.mpl_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加导航工具栏
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        self.toolbar = NavigationToolbar2Tk(self.mpl_canvas, self.chart_frame)
        self.toolbar.update()
        self.mpl_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 绑定数据源变更事件
        self.data_source_combo.bind("<<ComboboxSelected>>", self.on_data_source_change)
        
        # 初始化数据提供者
        self.on_data_source_change()
        
        # 添加数据加载状态变量
        self.loading_data = False
        self.data_cache = {}  # 添加数据缓存
        
        # 初始化加载动画
        self.loading_animation = LoadingAnimation(self.canvas, 600, 400, size=30, color="#3498db")
        
        # 如果提供了初始股票代码，则自动查询
        if initial_symbols and any(initial_symbols):
            # 确保股票代码格式正确
            formatted_symbols = []
            for symbol in initial_symbols:
                if symbol.isdigit() and len(symbol) < 6:
                    symbol = symbol.zfill(6)
                formatted_symbols.append(symbol)
                
            # 更新输入框中的股票代码
            for i, symbol in enumerate(formatted_symbols[:8]):
                if i < len(self.stock_code_vars):
                    self.stock_code_vars[i].set(symbol)
            
            # 延迟执行查询，确保UI已完全加载
            self.root.after(500, self.fetch_and_plot)
            
        # 设置第一个输入框获取焦点
        if self.stock_code_entries:
            self.stock_code_entries[0].focus_set()
            
    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 确定滚动方向和数量
        if event.num == 4 or event.delta > 0:  # 向上滚动
            self.canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:  # 向下滚动
            self.canvas.yview_scroll(1, "units")
            
    def refresh_data(self):
        """刷新当前显示的股票数据"""
        # 清除数据缓存
        self.data_cache = {}
        # 重新获取数据并绘制
        self.fetch_and_plot()

    def focus_next_entry(self, event, current_idx):
        """将焦点移动到下一个输入框"""
        next_idx = (current_idx + 1) % len(self.stock_code_entries)
        self.stock_code_entries[next_idx].focus_set()
        # 选中全部文本，方便用户直接输入
        self.stock_code_entries[next_idx].select_range(0, tk.END)
        # 阻止默认的Tab键行为
        return "break"

    def focus_prev_entry(self, event, current_idx):
        """将焦点移动到上一个输入框"""
        prev_idx = (current_idx - 1) % len(self.stock_code_entries)
        self.stock_code_entries[prev_idx].focus_set()
        # 选中全部文本，方便用户直接输入
        self.stock_code_entries[prev_idx].select_range(0, tk.END)
        # 阻止默认的Shift+Tab键行为
        return "break"

    def import_stock_codes(self):
        """从文本文件导入股票代码"""
        file_path = filedialog.askopenfilename(
            title="选择股票代码文件",
            filetypes=[("文本文件", "*.txt"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                codes = [line.strip() for line in f.readlines() if line.strip()]
            
            # 格式化股票代码
            formatted_codes = []
            for code in codes:
                # 处理可能的逗号分隔
                for c in code.split(','):
                    c = c.strip()
                    if c:
                        # 格式化股票代码（例如将数字代码补齐为6位）
                        formatted_code = self.format_stock_code(c)
                        formatted_codes.append(formatted_code)
            
            # 清空现有输入框
            self.clear_entries()
            
            # 保存所有导入的股票代码
            self.all_imported_codes = formatted_codes
            
            # 填充输入框
            for i, code in enumerate(formatted_codes[:8]):  # 显示界面上的前8个
                self.stock_code_vars[i].set(code)
            
            # 更新状态栏显示导入的股票数量
            total_codes = len(formatted_codes)
            if total_codes > 8:
                self.status_var.set(f"已导入 {total_codes} 个股票代码，显示前 8 个。使用导航按钮查看更多。")
                # 启用导航按钮
                self.prev_page_button.config(state="disabled")  # 第一页禁用上一页
                self.next_page_button.config(state="normal")
                # 设置当前页码
                self.current_page = 0
            else:
                self.status_var.set(f"已导入 {total_codes} 个股票代码")
                # 禁用导航按钮
                self.prev_page_button.config(state="disabled")
                self.next_page_button.config(state="disabled")
            
            # 如果导入了代码，自动查询
            if formatted_codes:
                self.fetch_and_plot()
                
        except Exception as e:
            messagebox.showerror("错误", f"导入股票代码失败: {str(e)}")

    def format_stock_code(self, code):
        """格式化股票代码"""
        code = code.strip()
        
        # 处理纯数字代码，补齐6位
        if code.isdigit():
            # 中国股票代码通常为6位数字
            if len(code) < 6:
                code = code.zfill(6)
        
        # 处理带交易所前缀的代码
        if code.upper().startswith(('SH', 'SZ', 'BJ', 'HK')):
            # 确保交易所代码大写，股票代码部分补齐
            prefix = code[:2].upper()
            stock_num = code[2:].strip()
            if stock_num.isdigit() and len(stock_num) < 6:
                stock_num = stock_num.zfill(6)
            code = f"{prefix}{stock_num}"
        
        return code

    def clear_entries(self):
        """清空所有股票代码输入框"""
        for var in self.stock_code_vars:
            var.set("")
        self.status_var.set("已清空所有股票代码")
        # 重置导入的股票代码列表和页码
        self.all_imported_codes = []
        self.current_page = 0
        # 禁用导航按钮
        self.prev_page_button.config(state="disabled")
        self.next_page_button.config(state="disabled")
        
    def show_prev_page(self):
        """显示上一页股票代码"""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_stock_entries()
            
            # 更新按钮状态
            self.next_page_button.config(state="normal")
            if self.current_page == 0:
                self.prev_page_button.config(state="disabled")
                
            # 自动查询新显示的股票
            self.fetch_and_plot()
    
    def show_next_page(self):
        """显示下一页股票代码"""
        max_pages = (len(self.all_imported_codes) - 1) // 8
        if self.current_page < max_pages:
            self.current_page += 1
            self.update_stock_entries()
            
            # 更新按钮状态
            self.prev_page_button.config(state="normal")
            if self.current_page == max_pages:
                self.next_page_button.config(state="disabled")
                
            # 自动查询新显示的股票
            self.fetch_and_plot()
    
    def update_stock_entries(self):
        """根据当前页码更新股票输入框"""
        # 清空当前输入框
        for var in self.stock_code_vars:
            var.set("")
            
        # 计算当前页的起始索引
        start_idx = self.current_page * 8
        end_idx = min(start_idx + 8, len(self.all_imported_codes))
        
        # 填充输入框
        for i, code in enumerate(self.all_imported_codes[start_idx:end_idx]):
            self.stock_code_vars[i].set(code)
            
        # 更新状态栏
        total_codes = len(self.all_imported_codes)
        self.status_var.set(f"显示第 {start_idx+1}-{end_idx} 个股票代码 (共 {total_codes} 个)")

    def show_batch_input_dialog(self):
        """显示批量输入股票代码的对话框"""
        # 创建对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("批量输入股票代码")
        dialog.geometry("400x300")
        dialog.transient(self.root)  # 设置为主窗口的子窗口
        dialog.grab_set()  # 模态对话框
        
        # 创建说明标签
        ttk.Label(dialog, text="请输入股票代码，每行一个或用逗号分隔:").pack(pady=(10, 5), padx=10, anchor=tk.W)
        
        # 创建文本框
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建文本框
        text_box = tk.Text(text_frame, height=10, width=40, yscrollcommand=scrollbar.set)
        text_box.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_box.yview)
        
        # 如果已有导入的代码，预填充文本框
        if self.all_imported_codes:
            text_box.insert(tk.END, "\n".join(self.all_imported_codes))
        
        # 创建按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))
        
        # 确定按钮
        def on_confirm():
            # 获取文本内容
            text_content = text_box.get("1.0", tk.END).strip()
            
            # 解析股票代码
            codes = []
            for line in text_content.split("\n"):
                # 处理每行，支持逗号分隔
                line_codes = [code.strip() for code in line.split(",") if code.strip()]
                codes.extend(line_codes)  # 修正缩进，将这行移到循环内部
            
            if not codes:
                messagebox.showwarning("警告", "未输入任何股票代码", parent=dialog)
                return
                
            # 格式化股票代码
            formatted_codes = [self.format_stock_code(code) for code in codes]
            
            # 保存所有导入的股票代码
            self.all_imported_codes = formatted_codes
            
            # 更新输入框
            self.current_page = 0
            self.update_stock_entries()
            
            # 更新导航按钮状态
            if len(formatted_codes) > 8:
                self.prev_page_button.config(state="disabled")
                self.next_page_button.config(state="normal")
            else:
                self.prev_page_button.config(state="disabled")
                self.next_page_button.config(state="disabled")
            
            # 关闭对话框
            dialog.destroy()
            
            # 自动查询
            self.fetch_and_plot()
        
        confirm_button = ttk.Button(button_frame, text="确定", command=on_confirm)
        confirm_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 聚焦文本框
        text_box.focus_set()

    def on_data_source_change(self, event=None):
        """当数据源变更时更新数据提供者"""
        data_source = self.data_source_var.get()
        try:
            self.data_provider = StockDataProviderFactory.get_provider(data_source)
            self.status_var.set(f"已切换到{data_source}数据源")
        except Exception as e:
            messagebox.showerror("错误", f"切换数据源失败: {str(e)}")
            # 回退到默认数据源
            self.data_source_var.set("yahoo")
            self.data_provider = StockDataProviderFactory.get_provider("sina")
            self.status_var.set("已切换到yahoo数据源")

    def create_tooltip(self, widget, text):
        """为控件创建工具提示"""
        def enter(event):
            self.tooltip = tk.Toplevel(self.root)
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            label = ttk.Label(self.tooltip, text=text,
                              background="#ffffe0", relief="solid", borderwidth=1)
            label.pack()

        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()

        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def fetch_and_plot(self):
        """获取多个股票数据并绘制K线图"""
        # 获取所有非空的股票代码
        stock_codes = [var.get().strip() for var in self.stock_code_vars if var.get().strip()]
        
        # 格式化股票代码
        stock_codes = [self.format_stock_code(code) for code in stock_codes]
        
        # 更新输入框中的格式化后的代码
        for i, code in enumerate(stock_codes):
            if i < len(self.stock_code_vars):
                self.stock_code_vars[i].set(code)
        
        if not stock_codes:
            messagebox.showerror("错误", "请至少输入一个股票代码")
            return
        
        # 防止重复点击查询按钮
        if self.loading_data:
            messagebox.showinfo("提示", "正在加载数据，请稍候...")
            return
        
        self.loading_data = True
        self.query_button.config(state="disabled")
        self.refresh_button.config(state="disabled")
        
        # 启动加载动画
        self.loading_animation.start()
        
        period = self.period_var.get()
        interval = self.interval_var.get()
        
        self.status_var.set(f"正在获取 {', '.join(stock_codes)} 的K线数据...")
        self.root.update_idletasks()
        
        # 清除之前的图表
        self.fig.clear()
        
        # 确定子图布局 - 使用动态布局
        num_stocks = len(stock_codes)
        rows, cols = self.get_optimal_grid_layout(num_stocks)
        
        # 调整图表大小以适应更多的股票
        # 根据股票数量动态调整图表大小
        if num_stocks > 4:
            # 对于更多的股票，增加图表的高度
            height = max(8, rows * 4)  # 每行至少4英寸高
            width = max(12, cols * 4)  # 每列至少4英寸宽
            self.fig.set_size_inches(width, height)
        else:
            self.fig.set_size_inches(12, 8)  # 默认大小
        
        # 创建子图
        axes = []
        for i in range(min(num_stocks, rows * cols)):
            ax = self.fig.add_subplot(rows, cols, i+1)
            axes.append(ax)
        
        # 使用线程获取数据并绘制，避免UI冻结
        def fetch_data_thread():
            successful_plots = 0  # 跟踪成功绘制的图表数量
            failed_codes = []     # 记录获取失败的股票代码
            
            # 获取数据并绘制
            for i, code in enumerate(stock_codes[:rows * cols]):
                try:
                    # 检查缓存
                    cache_key = f"{code}_{period}_{interval}"
                    if cache_key in self.data_cache:
                        data = self.data_cache[cache_key]
                    else:
                        # 使用当前选择的数据提供者获取数据
                        data = self.data_provider.get_stock_data(code, period, interval)
                        # 缓存数据
                        if data is not None and not data.empty:
                            self.data_cache[cache_key] = data
                    
                    # 更新状态栏
                    self.status_var.set(f"正在处理 {code} 的数据 ({i+1}/{len(stock_codes[:rows * cols])})...")
                    self.root.update_idletasks()
                    
                    if data is None or data.empty:
                        axes[i].text(0.5, 0.5, f"无法获取 {code} 的数据\n请检查股票代码或网络连接", 
                                    horizontalalignment='center', verticalalignment='center',
                                    transform=axes[i].transAxes)
                        failed_codes.append(code)
                        continue
                    
                    # 绘制K线图
                    try:
                        self.plot_candlestick(axes[i], data, code)
                    except Exception as e:
                        print(f"绘制K线图时出错: {e}")
                        import traceback
                        print(traceback.format_exc())
                        axes[i].text(0.5, 0.5, f"绘制 {code} 的K线图出错\n{str(e)[:50]}...", 
                                    horizontalalignment='center', verticalalignment='center',
                                    transform=axes[i].transAxes)
                        failed_codes.append(code)
                        continue
                    
                    # 添加移动平均线
                    try:
                        if len(data) >= 5:
                            data['MA5'] = data['Close'].rolling(window=5).mean()
                            axes[i].plot(range(len(data)),
                                        data['MA5'], color='blue', linewidth=1, label='MA5')

                        if len(data) >= 20:
                            data['MA20'] = data['Close'].rolling(window=20).mean()
                            axes[i].plot(range(len(data)),
                                        data['MA20'], color='orange', linewidth=1, label='MA20')
                        
                        # 添加图例
                        axes[i].legend(loc='best', fontsize='small')
                    except Exception as e:
                        print(f"绘制移动平均线时出错: {e}")
                    
                    # 如果需要显示阻力区
                    try:
                        if self.show_resistance_var.get():
                            # 使用增强版的阻力区绘制（包含斐波那契回撤位和MACD背离检测）
                            self.plot_resistance_zones_with_fibonacci(axes[i], data)
                        else:
                            # 使用简单版的阻力区绘制
                            self.plot_resistance_zones(axes[i], data)
                    except Exception as e:
                        print(f"绘制阻力区时出错: {e}")
                    
                    # 添加图表联动功能
                    try:
                        ax = axes[i]
                        ax.callbacks.connect('xlim_changed', lambda event: self.sync_x_axes(event, axes))
                    except Exception as e:
                        print(f"添加图表联动功能时出错: {e}")
                    
                    successful_plots += 1
                    
                except Exception as e:
                    import traceback
                    error_msg = str(e)
                    # 限制错误消息长度，避免显示过长
                    if len(error_msg) > 100:
                        error_msg = error_msg[:100] + "..."
                    axes[i].text(0.5, 0.5, f"获取 {code} 数据出错:\n{error_msg}\n请尝试刷新或更换数据源", 
                                horizontalalignment='center', verticalalignment='center',
                                transform=axes[i].transAxes)
                    # 记录详细错误到控制台
                    print(f"绘制 {code} 时出错: {e}")
                    print(traceback.format_exc())
                    failed_codes.append(code)
            
            # 停止加载动画
            self.root.after(0, self.loading_animation.stop)
            
            try:
                # 调整布局
                self.fig.tight_layout()
                self.mpl_canvas.draw()
                
                # 更新滚动区域
                self.chart_frame.update_idletasks()
                self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            except Exception as e:
                print(f"更新图表布局时出错: {e}")
            
            if successful_plots == 0 and stock_codes:
                self.status_var.set(f"警告: 所有股票数据获取失败，请检查网络连接或更换数据源")
                # 显示错误对话框
                self.root.after(100, lambda: messagebox.showerror("数据获取失败", 
                                                                "无法获取任何股票数据，请检查:\n"
                                                                "1. 网络连接是否正常\n"
                                                                "2. 股票代码是否正确\n"
                                                                "3. 尝试更换数据源"))
            elif failed_codes:
                self.status_var.set(f"已显示 {successful_plots} 个股票的K线图，{len(failed_codes)} 个获取失败")
            else:
                self.status_var.set(f"已显示 {', '.join(stock_codes[:rows * cols])} 的K线图")
            
            # 恢复按钮状态
            self.loading_data = False
            self.query_button.config(state="normal")
            self.refresh_button.config(state="normal")
        
        # 启动数据获取线程
        data_thread = threading.Thread(target=fetch_data_thread)
        data_thread.daemon = True
        data_thread.start()

    def sync_x_axes(self, event, axes):
        """同步所有图表的X轴范围"""
        # 获取当前被操作的图表的X轴范围
        xlim = event.get_xlim()
        
        # 将该范围应用到所有图表
        for ax in axes:
            if ax.get_xlim() != xlim:
                ax.set_xlim(xlim)
        
        # 重绘画布
        self.mpl_canvas.draw_idle()

    def open_single_stock_view(self, stock_code):
        """打开单股票K线图视图"""
        try:
            # 创建新窗口
            new_window = tk.Toplevel(self.root)
            
            # 导入单股票K线图模块
            from src.ui.stock_kline import StockKLineApp
            
            # 获取当前数据源和时间范围设置
            data_source = self.data_source_var.get()
            
            # 创建单股票K线图应用
            StockKLineApp(new_window, initial_symbol=stock_code)
            
            # 设置窗口标题
            new_window.title(f"股票K线图 - {stock_code}")
            
            # 显示状态信息
            self.status_var.set(f"已在新窗口打开 {stock_code} 的详细K线图")
        except Exception as e:
            messagebox.showerror("错误", f"打开单股票视图失败: {str(e)}")

    def plot_candlestick(self, ax, data, title):
        """在指定的轴上绘制K线图"""
        # 获取股票信息，显示股票名称和代码
        try:
            stock_info = self.data_provider.get_stock_info(title)
            if 'shortName' in stock_info:
                display_title = f"{stock_info['shortName']} ({title})"
            else:
                display_title = title
        except Exception:
            display_title = title
        
        # 设置标题
        ax.set_title(display_title)
        
        # 获取数据
        opens = data['Open'].values
        highs = data['High'].values
        lows = data['Low'].values
        closes = data['Close'].values
        
        # 计算上涨和下跌
        up = closes >= opens
        down = opens > closes
        
        # 设置x轴刻度
        if len(data) > 30:
            ax.set_xticks(range(0, len(data), len(data) // 10))
            ax.set_xticklabels([data.index[i].strftime('%Y-%m-%d') for i in range(0, len(data), len(data) // 10)], rotation=45)
        else:
            ax.set_xticks(range(len(data)))
            ax.set_xticklabels([date.strftime('%Y-%m-%d') for date in data.index], rotation=45)
        
        # 绘制K线
        width = 0.6
        
        # 绘制上涨蜡烛
        if any(up):
            ax.bar(np.where(up)[0], closes[up] - opens[up], width, bottom=opens[up], color='red', alpha=0.8)
            ax.vlines(np.where(up)[0], lows[up], highs[up], color='red', linewidth=1)
        
        # 绘制下跌蜡烛
        if any(down):
            ax.bar(np.where(down)[0], opens[down] - closes[down], width, bottom=closes[down], color='green', alpha=0.8)
            ax.vlines(np.where(down)[0], lows[down], highs[down], color='green', linewidth=1)
        
        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 添加最新价格标签
        if len(data) > 0:
            latest_price = data['Close'].iloc[-1]
            ax.axhline(y=latest_price, color='black', linestyle='--', alpha=0.5)
            ax.text(len(data) - 1, latest_price, f' {latest_price:.2f}', 
                    verticalalignment='bottom', horizontalalignment='right')

        # 添加点击事件，跳转到单股票视图
        stock_code = title  # 保存当前股票代码
        
        # 跟踪最后一次点击时间，用于检测双击
        self.last_click_time = 0
        
        def on_click(event):
            if event.inaxes == ax:
                # 检测双击 (两次点击间隔小于0.5秒)
                current_time = datetime.now().timestamp()
                if hasattr(self, 'last_click_time') and current_time - self.last_click_time < 0.5:
                    self.open_single_stock_view(stock_code)
                    self.last_click_time = 0  # 重置点击时间，防止连续触发
                else:
                    self.last_click_time = current_time
        
        # 连接点击事件
        self.fig.canvas.mpl_connect('button_press_event', on_click)
        
        # 添加提示信息
        ax.text(0.5, 0.01, "双击查看详情", 
                horizontalalignment='center', verticalalignment='bottom',
                transform=ax.transAxes, fontsize=8, alpha=0.7,
                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

    def plot_resistance_zones(self, ax, data):
        """在指定的轴上绘制阻力区"""
        if len(data) < 20:  # 数据太少，不绘制阻力区
            return
            
        current_price = data['Close'].iloc[-1]
        zones = self.calculate_resistance_zones(data, current_price)
        
        # 绘制阻力区
        for zone in zones:
            price = zone['price']
            strength = zone['strength']
            
            # 根据强度确定颜色深浅
            alpha = min(0.3, 0.1 + strength * 0.05) if isinstance(strength, (int, float)) else 0.3
            
            # 绘制水平线
            ax.axhline(y=price, color='purple', linestyle='-', alpha=alpha, linewidth=1)
            
            # 在右侧添加标签
            ax.text(len(data) - 1, price, f' {price:.2f}', 
                    verticalalignment='bottom', horizontalalignment='right',
                    color='purple', alpha=min(0.8, 0.5 + (strength * 0.1 if isinstance(strength, (int, float)) else 0.3)))

    def plot_resistance_zones_with_fibonacci(self, ax, data):
        """绘制阻力区并结合斐波那契回撤进行确认"""
        if len(data) < 10:
            return

        try:
            current_price = data['Close'].iloc[-1]
            resistance_zones = self.calculate_resistance_zones(data, current_price)

            # 计算斐波那契回撤位
            fib_levels = self.calculate_fibonacci_levels(data)
            
            # 调用MACD计算与背离检测
            lookback = min(120, len(data))  # 使用120个数据点或全部数据点
            macd_data = self.calculate_macd(data)
            divergences = self.detect_macd_divergence(data, macd_data, lookback=lookback)

            # 绘制斐波那契回撤位
            for level, price in fib_levels.items():
                ax.axhline(y=price, color='green', linestyle='-.', alpha=0.6, linewidth=1)
                ax.text(len(data)*0.01, price, f"{level}% ({price:.2f})",
                        color='green', fontsize=7, verticalalignment='bottom',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 绘制KDE阻力区
            for zone in resistance_zones:
                ax.axhline(y=zone['price'], color='purple', linestyle='--', alpha=0.7, linewidth=1)
                ax.text(len(data)*0.95, zone['price']*1.002,
                        f"阻力: {zone['price']:.2f} ({zone['strength']})",
                        color='purple', fontsize=8, verticalalignment='bottom',
                        horizontalalignment='right', bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 检查并标记汇合区 (±0.5%内)
            for fib_price in fib_levels.values():
                for zone in resistance_zones:
                    if abs(zone['price'] - fib_price) / fib_price <= 0.005:
                        # 汇合点突出标记
                        ax.axhspan(zone['price']*0.9975, zone['price']*1.0025,
                                color='red', alpha=0.2)
                        ax.text(len(data)*0.5, zone['price'],
                                f"汇合区: {zone['price']:.2f}",
                                color='red', fontsize=9, fontweight='bold',
                                horizontalalignment='center', verticalalignment='center',
                                bbox=dict(facecolor='yellow', alpha=0.5, edgecolor='red', pad=2))
                
            # 在图上绘制背离标记
            for div in divergences:
                idx = div['index']
                # 由于我们现在使用的是整数索引位置，需要确保它在有效范围内
                if idx < 0 or idx >= lookback:
                    continue
                    
                # 获取实际数据点的位置（在绘图中的x坐标）
                x_pos = len(data) - lookback + idx
                if x_pos < 0 or x_pos >= len(data):
                    continue
                    
                price = data['High'].iloc[x_pos] if div['type'] == '顶背离' else data['Low'].iloc[x_pos]
                offset = (ax.get_ylim()[1] - ax.get_ylim()[0]) * 0.02
                y_pos = price + offset if div['type']=='顶背离' else price - offset
                
                ax.annotate(div['type'], xy=(x_pos, price), 
                            xytext=(x_pos, y_pos),
                            arrowprops=dict(facecolor='red' if div['type']=='顶背离' else 'green', shrink=0.05),
                            fontsize=8, color='red' if div['type']=='顶背离' else 'green',
                            horizontalalignment='center',
                            verticalalignment='bottom' if div['type']=='顶背离' else 'top',
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))
                
            # 当前价格标记
            ax.axhline(y=current_price, color='blue', linestyle='-', alpha=0.5, linewidth=1)
            ax.text(len(data)*0.95, current_price*0.998, f"当前: {current_price:.2f}",
                    color='blue', fontsize=8, verticalalignment='top',
                    horizontalalignment='right', bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))
        except Exception as e:
            # 捕获所有异常，确保绘图过程不会中断
            print(f"绘制阻力区和斐波那契回撤位时出错: {e}")
            import traceback
            print(traceback.format_exc())

    def calculate_fibonacci_levels(self, data):
        """计算斐波那契回撤位"""
        max_price = data['High'].max()
        min_price = data['Low'].min()

        diff = max_price - min_price
        levels = [23.6, 38.2, 50.0, 61.8, 78.6]

        fib_levels = {level: max_price - diff * (level / 100) for level in levels}
        return fib_levels
        
    def calculate_macd(self, data, short=12, long=26, signal=9):
        """计算MACD指标"""
        data = data.copy()
        data['EMA_short'] = data['Close'].ewm(span=short, adjust=False).mean()
        data['EMA_long'] = data['Close'].ewm(span=long, adjust=False).mean()
        data['DIF'] = data['EMA_short'] - data['EMA_long']
        data['DEA'] = data['DIF'].ewm(span=signal, adjust=False).mean()
        data['MACD'] = (data['DIF'] - data['DEA']) * 2
        return data[['DIF', 'DEA', 'MACD']]
    
    def find_local_extremes(self, series, distance=5, prominence=None):
        """找出序列中的局部极值点"""
        # 检查输入数据是否有效
        if series is None or len(series) < distance + 1:
            return [], []  # 返回空列表表示没有找到极值点
        
        try:
            # 找局部高点
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(series, distance=distance, prominence=prominence)
            
            # 找局部低点
            troughs, _ = find_peaks(-series, distance=distance, prominence=prominence)
            
            return peaks, troughs
        except Exception as e:
            print(f"查找局部极值点时出错: {e}")
            # 出错时返回空列表
            return [], []
    
    def _find_closest_peak(self, price_idx, indicator_peaks, max_distance=15):
        """找到与价格峰值/谷值最接近的指标峰值/谷值"""
        if not len(indicator_peaks):
            return None
            
        try:
            # 找到最接近的峰值
            distances = [abs(price_idx - peak) for peak in indicator_peaks]
            min_distance_idx = distances.index(min(distances))
            closest_peak = indicator_peaks[min_distance_idx]
            
            # 如果距离太远，则认为没有对应的峰值
            if abs(price_idx - closest_peak) > max_distance:
                return None
            
            return closest_peak
        except Exception as e:
            print(f"查找最接近峰值时出错: {e}")
            return None
    
    def detect_macd_divergence(self, data, macd_data, lookback=120):
        """检测MACD与价格之间的背离"""
        divergences = []
        
        # 确保数据足够
        if len(data) < 10 or len(macd_data) < 10:
            return divergences
        
        try:
            closes = data['Close'].iloc[-lookback:].reset_index(drop=True)
            macd = macd_data['DIF'].iloc[-lookback:].reset_index(drop=True)
            
            # 确保数据长度一致
            min_len = min(len(closes), len(macd))
            if min_len < 10:  # 数据太少，无法进行有效分析
                return divergences
            
            closes = closes.iloc[:min_len]
            macd = macd.iloc[:min_len]

            price_peaks, price_troughs = self.find_local_extremes(
                closes, distance=5, prominence=(closes.max()-closes.min())*0.01)
            macd_peaks, macd_troughs = self.find_local_extremes(
                macd, distance=5, prominence=(macd.max()-macd.min())*0.01)
            
            # 如果没有找到足够的峰值，返回空列表
            if len(price_peaks) < 2 or len(macd_peaks) < 2:
                return divergences

            # 顶背离检测
            for i in range(1, len(price_peaks)):
                # 当前价格峰值和前一个价格峰值
                curr_pp, prev_pp = price_peaks[i], price_peaks[i-1]
                curr_price, prev_price = closes.iloc[curr_pp], closes.iloc[prev_pp]
                
                # 如果当前价格没有创新高，则不是典型的顶背离情况
                if curr_price <= prev_price:
                    continue
                    
                # 改进的峰值匹配逻辑
                # 为每个价格峰值找到最接近的MACD峰值
                curr_macd_peak = self._find_closest_peak(curr_pp, macd_peaks)
                prev_macd_peak = self._find_closest_peak(prev_pp, macd_peaks)
                
                # 如果找不到对应的MACD峰值，跳过
                if curr_macd_peak is None or prev_macd_peak is None:
                    continue
                    
                # 使用更宽松的时间顺序检查：允许MACD峰值索引相等，但不允许前一个MACD峰值的索引严格大于当前MACD峰值的索引
                if prev_macd_peak > curr_macd_peak:
                    continue
                    
                macd_prev, macd_curr = macd.iloc[prev_macd_peak], macd.iloc[curr_macd_peak]

                # 验证背离：价格创新高但MACD走低
                if macd_curr < macd_prev:
                    # 计算背离强度
                    price_change_pct = (curr_price - prev_price) / prev_price
                    macd_std = macd.iloc[-lookback:].std()
                    if macd_std != 0:
                        macd_change_z = (macd_curr - macd_prev) / macd_std
                    else:
                        macd_change_z = 0
                    strength = abs(price_change_pct - macd_change_z)
                    
                    # 添加背离强度过滤，只保留强度大于阈值的背离
                    min_strength = 0.03  # 设置最小强度阈值
                    if strength > min_strength:
                        divergences.append({
                            'type': '顶背离',
                            'index': curr_pp,  # 使用整数索引位置而不是时间戳
                            'strength': strength,
                            'price_points': (prev_pp, curr_pp),
                            'macd_points': (prev_macd_peak, curr_macd_peak)
                        })
                    
            # 如果没有找到足够的谷值，返回已有的背离结果
            if len(price_troughs) < 2 or len(macd_troughs) < 2:
                return divergences

            # 底背离检测
            for i in range(1, len(price_troughs)):
                curr_pt, prev_pt = price_troughs[i], price_troughs[i-1]
                curr_price, prev_price = closes.iloc[curr_pt], closes.iloc[prev_pt]
                
                # 如果当前价格没有创新低，则不是典型的底背离情况
                if curr_price >= prev_price:
                    continue
                    
                # 改进的峰值匹配逻辑
                curr_macd_trough = self._find_closest_peak(curr_pt, macd_troughs)
                prev_macd_trough = self._find_closest_peak(prev_pt, macd_troughs)
                
                # 如果找不到对应的MACD谷值，跳过
                if curr_macd_trough is None or prev_macd_trough is None:
                    continue
                    
                # 使用更宽松的时间顺序检查：允许MACD谷值索引相等，但不允许前一个MACD谷值的索引严格大于当前MACD谷值的索引
                if prev_macd_trough > curr_macd_trough:
                    continue
                    
                macd_prev, macd_curr = macd.iloc[prev_macd_trough], macd.iloc[curr_macd_trough]

                # 验证背离：价格创新低但MACD走高
                if macd_curr > macd_prev:
                    # 计算背离强度
                    price_change_pct = (curr_price - prev_price) / prev_price
                    macd_std = macd.iloc[-lookback:].std()
                    if macd_std != 0:
                        macd_change_z = (macd_curr - macd_prev) / macd_std
                    else:
                        macd_change_z = 0
                    strength = abs(price_change_pct - macd_change_z)
                    
                    # 添加背离强度过滤，只保留强度大于阈值的背离
                    min_strength = 0.03  # 设置最小强度阈值
                    if strength > min_strength:
                        divergences.append({
                            'type': '底背离',
                            'index': curr_pt,
                            'strength': strength,
                            'price_points': (prev_pt, curr_pt),
                            'macd_points': (prev_macd_trough, curr_macd_trough)
                        })
        except Exception as e:
            print(f"检测MACD背离时出错: {e}")
            import traceback
            print(traceback.format_exc())

        return divergences

    def calculate_resistance_zones(self, data, current_price):
        """计算股票的阻力区 (优化版)

        Args:
            data: 股票历史数据 (DataFrame，含High列)
            current_price: 当前价格 (float)

        Returns:
            list: 阻力区列表，每个阻力区包含价格(price)和强度(strength)
        """
        import numpy as np
        resistance_zones = []

        # 只考虑当前价格以上的高点
        highs = data[data['High'] > current_price]['High']

        if highs.empty:
            # 没有高于当前价格的数据，返回历史最高点
            if not data.empty:
                highest = data['High'].max()
                resistance_zones.append({
                    'price': highest,
                    'strength': '历史最高'
                })
            return resistance_zones

        # 使用KDE (核密度估计) 找出价格密集区域
        try:
            from scipy.stats import gaussian_kde
            from scipy.signal import find_peaks

            # 检查高点数据是否足够进行KDE分析
            if len(highs) < 2:
                # 如果只有一个高点，直接使用它作为阻力位
                resistance_zones.append({
                    'price': highs.iloc[0],
                    'strength': '强'
                })
                return resistance_zones

            # 创建价格范围
            price_range = np.linspace(
                current_price, data['High'].max() * 1.05, 1000)

            # 计算KDE
            kde = gaussian_kde(highs)
            density = kde(price_range)

            # 找出密度峰值
            peaks, _ = find_peaks(density, height=0, distance=len(price_range)//20)

            # 按密度值排序
            peak_densities = [(price_range[peak], density[peak]) for peak in peaks]
            peak_densities.sort(key=lambda x: x[1], reverse=True)

            # 距离当前价格20%以内的阻力位
            valid_peaks = [p for p in peak_densities if 0 <
                        (p[0] - current_price)/current_price <= 0.2]

            if not valid_peaks and peak_densities:
                # 没有符合条件的，选择最接近的一个
                valid_peaks = [
                    min(peak_densities, key=lambda x: abs(x[0]-current_price))]

            # 取前3个最强的阻力区
            for i, (price, _) in enumerate(valid_peaks[:3]):
                strength = '强' if i == 0 else '中' if i == 1 else '弱'
                resistance_zones.append({
                    'price': price,
                    'strength': strength
                })

        except (ImportError, ValueError) as e:
            print(f"使用备用阻力计算方案: {e}")

            # 使用备用方案：局部高点法
            window = min(10, max(3, len(highs) // 5))
            try:
                from scipy.signal import find_peaks
                # 如果高点数据不足，直接使用所有高点
                if len(highs) < window:
                    local_highs = highs
                else:
                    peaks_idx, _ = find_peaks(highs, distance=window)
                    local_highs = highs.iloc[peaks_idx]
                    
                    # 如果没有找到峰值，直接使用所有高点
                    if len(local_highs) == 0:
                        local_highs = highs
            except ImportError:
                # 若无scipy，回退到简单的滑动窗口法
                if len(highs) < window:
                    local_highs = highs
                else:
                    rolling_max = highs.rolling(window=window, center=True).max()
                    local_highs = highs[highs == rolling_max].dropna()
                    
                    # 如果没有找到局部最大值，直接使用所有高点
                    if len(local_highs) == 0:
                        local_highs = highs

            # 按距离当前价格近的顺序排序
            sorted_highs = sorted(local_highs.tolist(),
                                key=lambda p: abs(p - current_price))

            valid_highs = [p for p in sorted_highs if 0 <
                        (p - current_price)/current_price <= 0.2]

            if not valid_highs and sorted_highs:
                valid_highs = [sorted_highs[0]]

            for i, price in enumerate(valid_highs[:3]):
                strength = '强' if i == 0 else '中' if i == 1 else '弱'
                resistance_zones.append({
                    'price': price,
                    'strength': strength
                })

        # 阻力位去重合并函数
        def merge_similar_zones(zones, price_tolerance=0.005):
            merged = []
            zones.sort(key=lambda x: x['price'])
            for zone in zones:
                if not merged:
                    merged.append(zone)
                else:
                    last_zone = merged[-1]
                    # 如果价格差距小于0.5%，则合并
                    if abs(zone['price'] - last_zone['price']) / last_zone['price'] < price_tolerance:
                        # 保留强度更高的阻力
                        strength_order = {'强': 3, '中': 2, '弱': 1, '历史最高': 4}
                        if strength_order.get(zone['strength'], 0) > strength_order.get(last_zone['strength'], 0):
                            merged[-1] = zone
                    else:
                        merged.append(zone)
            return merged

        # 最终合并去重后的阻力区
        resistance_zones = merge_similar_zones(resistance_zones)

        return resistance_zones

    def get_optimal_grid_layout(self, num_charts):
        """根据图表数量确定最佳的网格布局"""
        if num_charts <= 1:
            return 1, 1
        elif num_charts <= 2:
            return 1, 2  # 1行2列
        elif num_charts <= 4:
            return 2, 2  # 2行2列
        elif num_charts <= 6:
            return 2, 3  # 2行3列
        elif num_charts <= 8:
            return 2, 4  # 2行4列 - 优化8个股票的显示
        elif num_charts <= 9:
            return 3, 3  # 3行3列
        else:
            # 对于更多图表，计算最接近的矩形布局
            import math
            cols = math.ceil(math.sqrt(num_charts))
            rows = math.ceil(num_charts / cols)
            return rows, cols

    def export_stock_codes(self):
        """导出当前股票代码到文本文件"""
        stock_codes = [var.get().strip() for var in self.stock_code_vars if var.get().strip()]
        
        if not stock_codes:
            messagebox.showwarning("警告", "没有股票代码可导出")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="保存股票代码文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for code in stock_codes:
                    f.write(f"{code}\n")
                    
            self.status_var.set(f"已导出 {len(stock_codes)} 个股票代码到 {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"导出股票代码失败: {str(e)}")

    def save_screenshot(self):
        """保存当前图表为图片"""
        # 检查是否有图表
        if not hasattr(self, 'fig') or self.fig is None:
            messagebox.showwarning("警告", "没有可保存的图表")
            return
            
        # 获取当前显示的股票代码
        stock_codes = [var.get().strip() for var in self.stock_code_vars if var.get().strip()]
        if not stock_codes:
            stock_codes = ["未知股票"]
            
        # 生成默认文件名（使用当前日期时间和股票代码）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"股票K线图_{timestamp}_{'-'.join(stock_codes[:3])}"
        if len(stock_codes) > 3:
            default_filename += f"等{len(stock_codes)}只"
            
        # 弹出文件保存对话框
        file_path = filedialog.asksaveasfilename(
            title="保存图表截图",
            defaultextension=".png",
            initialfile=default_filename,
            filetypes=[("PNG图片", "*.png"), ("JPEG图片", "*.jpg"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            # 保存前更新图表，确保显示最新内容
            self.fig.tight_layout()
            
            # 保存图表为图片（使用高DPI以确保清晰度）
            self.fig.savefig(file_path, dpi=150, bbox_inches='tight')
            
            # 显示成功消息
            self.status_var.set(f"已保存图表截图到 {os.path.basename(file_path)}")
            
            # 询问是否打开图片
            if messagebox.askyesno("截图已保存", f"图表已保存到 {os.path.basename(file_path)}。\n是否打开查看？"):
                # 根据操作系统打开图片
                if sys.platform == 'darwin':  # macOS
                    os.system(f"open '{file_path}'")
                elif sys.platform == 'win32':  # Windows
                    os.system(f'start "" "{file_path}"')
                else:  # Linux
                    os.system(f"xdg-open '{file_path}'")
                    
        except Exception as e:
            messagebox.showerror("错误", f"保存截图失败: {str(e)}")
            
    def copy_to_clipboard(self):
        """将当前图表复制到系统剪贴板"""
        # 检查是否有图表
        if not hasattr(self, 'fig') or self.fig is None:
            messagebox.showwarning("警告", "没有可复制的图表")
            return
            
        try:
            # 更新图表布局，确保显示最新内容
            self.fig.tight_layout()
            
            # 将图表保存到内存中的BytesIO对象
            buf = io.BytesIO()
            self.fig.savefig(buf, format='png', dpi=150, bbox_inches='tight')
            buf.seek(0)
            
            # 使用PIL打开内存中的图像
            img = Image.open(buf)
            
            # 根据不同操作系统，使用不同方法复制到剪贴板
            if sys.platform == 'darwin':  # macOS
                # 使用临时文件和pbcopy命令
                temp_file = os.path.join(os.path.expanduser("~"), ".temp_clipboard_image.png")
                img.save(temp_file)
                os.system(f"osascript -e 'set the clipboard to (read (POSIX file \"{temp_file}\") as TIFF picture)'")
                # 删除临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass
            elif sys.platform == 'win32':  # Windows
                # 在Windows上使用PIL的ImageGrab模块
                from PIL import ImageGrab
                img_array = np.array(img)
                img = Image.fromarray(img_array)
                output = io.BytesIO()
                img.convert('RGB').save(output, 'BMP')
                data = output.getvalue()[14:]  # 去掉BMP文件头
                output.close()
                
                # 使用win32clipboard模块
                import win32clipboard
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                win32clipboard.CloseClipboard()
            else:  # Linux
                # 在Linux上使用xclip命令
                temp_file = "/tmp/.temp_clipboard_image.png"
                img.save(temp_file)
                os.system(f"xclip -selection clipboard -t image/png -i {temp_file}")
                # 删除临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass
                
            # 显示成功消息
            self.status_var.set("已复制图表到剪贴板")
            
        except ImportError as e:
            # 如果缺少必要的模块，提示用户安装
            if "win32clipboard" in str(e):
                messagebox.showerror("错误", "缺少必要的模块: pywin32\n请运行 'pip install pywin32' 安装")
            else:
                messagebox.showerror("错误", f"复制到剪贴板失败: {str(e)}\n可能需要安装额外的依赖")
        except Exception as e:
            messagebox.showerror("错误", f"复制到剪贴板失败: {str(e)}")
            
            # 提供备选方案
            if messagebox.askyesno("备选方案", "复制到剪贴板失败。是否尝试保存为文件？"):
                self.save_screenshot()

    def on_frame_configure(self, event=None):
        """当内部框架大小改变时，更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_canvas_configure(self, event=None):
        """当画布大小改变时，调整内部窗口大小"""
        width = event.width
        self.canvas.itemconfig(self.canvas_window, width=width)


def main():
    # 检查命令行参数
    initial_symbols = []

    if len(sys.argv) > 1:
        initial_symbols = sys.argv[1:8]  # 最多取前8个参数作为初始股票代码

    root = tk.Tk()
    app = MultiStockKLineApp(root, initial_symbols)
    root.mainloop()


if __name__ == "__main__":
    main()