import pandas as pd
import os
import numpy as np

class StockDataModel:
    def __init__(self):
        self.df = None
        self.filtered_df = None
        self.current_sort_column = None
        self.sort_ascending = True

    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            # 读取Excel文件
            self.df = pd.read_excel(file_path)
            
            # 初始化筛选后的数据框
            self.filtered_df = self.df.copy()
            
            # 重置排序状态
            self.current_sort_column = None
            self.sort_ascending = True
            
            return True, None
        except Exception as e:
            return False, str(e)

    def apply_filter(self, conditions):
        """应用筛选条件"""
        try:
            # 重置筛选
            self.filtered_df = self.df.copy()
            
            # 应用每个筛选条件
            for condition in conditions:
                column = condition['column']
                operator = condition['operator']
                value = condition['value']
                
                # 确保列存在
                if column not in self.df.columns:
                    continue
                    
                # 获取列的数据类型
                col_type = self.df[column].dtype
                
                # 根据操作符应用筛选
                if operator == "包含":
                    mask = self.filtered_df[column].astype(str).str.contains(str(value), case=False, na=False)
                elif operator == "不包含":
                    mask = ~self.filtered_df[column].astype(str).str.contains(str(value), case=False, na=False)
                else:
                    # 尝试将值转换为数值类型
                    try:
                        if col_type in [np.float64, np.int64]:
                            value = float(value)
                    except ValueError:
                        continue
                        
                    # 应用比较操作符
                    if operator == "=":
                        mask = self.filtered_df[column] == value
                    elif operator == ">":
                        mask = self.filtered_df[column] > value
                    elif operator == "<":
                        mask = self.filtered_df[column] < value
                    elif operator == ">=":
                        mask = self.filtered_df[column] >= value
                    elif operator == "<=":
                        mask = self.filtered_df[column] <= value
                    elif operator == "!=":
                        mask = self.filtered_df[column] != value
                    else:
                        continue
                        
                self.filtered_df = self.filtered_df[mask]
                
            # 保持当前的排序
            if self.current_sort_column:
                self.sort_data(self.current_sort_column, self.sort_ascending)
                
            return True, None
        except Exception as e:
            return False, str(e)

    def sort_data(self, column, ascending=True):
        """排序数据"""
        try:
            if column in self.filtered_df.columns:
                self.filtered_df = self.filtered_df.sort_values(by=column, ascending=ascending)
                self.current_sort_column = column
                self.sort_ascending = ascending
            return True, None
        except Exception as e:
            return False, str(e)

    def get_unique_values(self, column):
        """获取列的唯一值"""
        if column in self.df.columns:
            return sorted(self.df[column].unique().tolist())
        return []
        
    def get_column_type(self, column):
        """获取列的数据类型"""
        if column in self.df.columns:
            return self.df[column].dtype
        return None

    def sort_by_column(self, column):
        """按列排序"""
        if self.filtered_df is None or column not in self.filtered_df.columns:
            return False
            
        if self.current_sort_column == column:
            self.sort_ascending = not self.sort_ascending
        else:
            self.sort_ascending = True
            self.current_sort_column = column
        
        self.filtered_df = self.filtered_df.sort_values(by=column, ascending=self.sort_ascending)
        return True

    def get_selected_rows(self, indices):
        """获取选中行的数据"""
        if self.filtered_df is None or not indices:
            return None
        return self.filtered_df.iloc[indices].copy()

    def export_data(self, file_path, selected_indices=None):
        """导出数据到Excel"""
        try:
            if selected_indices is not None:
                data = self.get_selected_rows(selected_indices)
            else:
                data = self.filtered_df
                
            if data is not None and len(data) > 0:
                data.to_excel(file_path, index=False)
                return True, None
            return False, "没有数据可导出"
        except Exception as e:
            return False, str(e)

    @staticmethod
    def _format_stock_code(code):
        """格式化股票代码"""
        if isinstance(code, (int, float)):
            code = str(int(code))
        elif not isinstance(code, str):
            code = str(code)
            
        if code.isdigit():
            if len(code) < 6 and (code.startswith(('0', '3', '6', '9'))):
                code = code.zfill(6)
            elif len(code) < 5 and not code.startswith(('0', '3', '6', '9')):
                code = code.zfill(4)
        
        return code 