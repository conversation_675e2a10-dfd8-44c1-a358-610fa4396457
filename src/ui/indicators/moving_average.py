"""
移动平均线指标模块
提供计算和绘制各种移动平均线的功能
"""
import pandas as pd
import numpy as np


def calculate_moving_averages(data, periods):
    """
    计算移动平均线

    参数:
    data: DataFrame，包含'Close'收盘价数据
    periods: list, 需要计算的均线周期列表

    返回:
    DataFrame，包含所有计算的移动平均线
    """
    result = pd.DataFrame(index=data.index)
    
    for period in periods:
        result[f'MA{period}'] = data['Close'].rolling(window=period).mean()
    
    return result


def calculate_ma_groups(data, ma_groups):
    """
    根据均线组配置计算所有移动平均线

    参数:
    data: DataFrame，包含'Close'收盘价数据
    ma_groups: dict, 均线组配置

    返回:
    DataFrame，包含所有计算的移动平均线
    """
    result = pd.DataFrame(index=data.index)
    active_groups = []
    
    for group_key, group in ma_groups.items():
        if group["enabled"]:
            active_groups.append(group)
            for line in group["lines"]:
                period = line["period"]
                if len(data) >= period:
                    result[line["name"]] = data['Close'].rolling(window=period).mean()
    
    return result, active_groups


def plot_moving_averages(ax, data, ma_data, active_groups):
    """
    在图表上绘制移动平均线

    参数:
    ax: matplotlib轴对象
    data: DataFrame，原始数据
    ma_data: DataFrame，包含所有计算的移动平均线
    active_groups: list，活跃的均线组列表
    """
    # 绘制每个活跃均线组中的均线
    for group in active_groups:
        for line in group["lines"]:
            line_name = line["name"]
            if line_name in ma_data.columns:
                ax.plot(range(len(data)), ma_data[line_name], 
                        color=line["color"], linewidth=1, label=line_name,
                        alpha=0.8, linestyle='-')  # 均线使用实线


def calculate_exponential_moving_average(data, periods):
    """
    计算指数移动平均线

    参数:
    data: DataFrame，包含'Close'收盘价数据
    periods: list, 需要计算的EMA周期列表

    返回:
    DataFrame，包含所有计算的EMA
    """
    result = pd.DataFrame(index=data.index)
    
    for period in periods:
        result[f'EMA{period}'] = data['Close'].ewm(span=period, adjust=False).mean()
    
    return result


def calculate_weighted_moving_average(data, periods):
    """
    计算加权移动平均线

    参数:
    data: DataFrame，包含'Close'收盘价数据
    periods: list, 需要计算的WMA周期列表

    返回:
    DataFrame，包含所有计算的WMA
    """
    result = pd.DataFrame(index=data.index)
    
    for period in periods:
        weights = np.arange(1, period + 1)
        result[f'WMA{period}'] = data['Close'].rolling(period).apply(
            lambda x: np.sum(weights * x) / weights.sum(), raw=True)
    
    return result 