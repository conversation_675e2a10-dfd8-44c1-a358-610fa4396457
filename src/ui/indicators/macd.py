"""
MACD指标模块
提供计算和绘制MACD指标的功能，以及MACD背离检测
"""
import pandas as pd
import numpy as np
from scipy.signal import find_peaks


def calculate_macd(data, short=12, long=26, signal=9):
    """
    计算MACD指标
    
    参数:
    data: DataFrame，包含'Close'收盘价数据
    short: int，短期EMA周期，默认为12
    long: int，长期EMA周期，默认为26
    signal: int，信号线周期，默认为9
    
    返回:
    DataFrame，包含MACD指标数据
    """
    data = data.copy()
    data['EMA_short'] = data['Close'].ewm(span=short, adjust=False).mean()
    data['EMA_long'] = data['Close'].ewm(span=long, adjust=False).mean()
    data['DIF'] = data['EMA_short'] - data['EMA_long']
    data['DEA'] = data['DIF'].ewm(span=signal, adjust=False).mean()
    data['MACD'] = (data['DIF'] - data['DEA']) * 2
    
    return data[['DIF', 'DEA', 'MACD']]


def find_local_extremes(series, distance=5, prominence=None):
    """
    查找序列中的局部极值点
    
    参数:
    series: Series，要分析的数据序列
    distance: int，两个峰值/谷值之间的最小距离
    prominence: float，峰值/谷值的最小突出度
    
    返回:
    tuple: (peaks, troughs) 峰值和谷值的索引列表
    """
    # 检查数据集是否为空或只有一个元素
    if len(series) <= 1:
        return [], []  # 返回空列表，表示没有峰值和谷值
        
    # 找局部高点
    peaks, _ = find_peaks(series, distance=distance, prominence=prominence)
    # 找局部低点
    troughs, _ = find_peaks(-series, distance=distance, prominence=prominence)
    
    return peaks, troughs


def _find_closest_peak(price_idx, indicator_peaks, max_distance=15):
    """
    找到与价格峰值/谷值最接近的指标峰值/谷值
    
    参数:
    price_idx: int，价格峰值/谷值的索引
    indicator_peaks: list，指标峰值/谷值的索引列表
    max_distance: int，最大允许距离
    
    返回:
    int or None: 最接近的指标峰值/谷值索引，如果没有找到则返回None
    """
    if not len(indicator_peaks):
        return None
        
    # 计算与价格峰值的距离
    distances = [abs(peak - price_idx) for peak in indicator_peaks]
    min_distance_idx = np.argmin(distances)
    
    # 如果最小距离超过阈值，认为没有匹配的峰值
    if distances[min_distance_idx] > max_distance:
        return None
        
    return indicator_peaks[min_distance_idx]


def detect_macd_divergence(data, macd_data, lookback=120):
    """
    检测MACD与价格之间的背离
    
    参数:
    data: DataFrame，包含OHLC数据的股票历史数据
    macd_data: DataFrame，包含MACD指标数据
    lookback: int，回溯的数据点数量
    
    返回:
    list: 包含背离信息的字典列表
    """
    divergences = []
    
    # 确保数据足够
    if len(data) < lookback or len(macd_data) < lookback:
        return divergences
    
    closes = data['Close'].iloc[-lookback:].reset_index(drop=True)
    macd = macd_data['DIF'].iloc[-lookback:].reset_index(drop=True)

    price_peaks, price_troughs = find_local_extremes(
        closes, distance=5, prominence=(closes.max()-closes.min())*0.01)
    macd_peaks, macd_troughs = find_local_extremes(
        macd, distance=5, prominence=(macd.max()-macd.min())*0.01)

    # 顶背离检测
    for i in range(1, len(price_peaks)):
        # 当前价格峰值和前一个价格峰值
        curr_pp, prev_pp = price_peaks[i], price_peaks[i-1]
        curr_price, prev_price = closes.iloc[curr_pp], closes.iloc[prev_pp]
        
        # 如果当前价格没有创新高，则不是典型的顶背离情况
        if curr_price <= prev_price:
            continue
            
        # 为每个价格峰值找到最接近的MACD峰值
        curr_macd_peak = _find_closest_peak(curr_pp, macd_peaks)
        prev_macd_peak = _find_closest_peak(prev_pp, macd_peaks)
        
        # 如果找不到对应的MACD峰值，跳过
        if curr_macd_peak is None or prev_macd_peak is None:
            continue
            
        # 使用更宽松的时间顺序检查
        if prev_macd_peak > curr_macd_peak:
            continue
            
        macd_prev, macd_curr = macd.iloc[prev_macd_peak], macd.iloc[curr_macd_peak]

        # 验证背离：价格创新高但MACD走低
        if macd_curr < macd_prev:
            # 计算背离强度
            price_change_pct = (curr_price - prev_price) / prev_price
            macd_std = macd.iloc[-lookback:].std()
            macd_change_z = (macd_curr - macd_prev) / macd_std if macd_std != 0 else 0
            strength = abs(price_change_pct - macd_change_z)
            
            # 添加背离强度过滤，只保留强度大于阈值的背离
            min_strength = 0.03  # 设置最小强度阈值
            if strength > min_strength:
                divergences.append({
                    'type': '顶背离',
                    'index': curr_pp,
                    'strength': strength,
                    'price_points': (prev_pp, curr_pp),
                    'macd_points': (prev_macd_peak, curr_macd_peak)
                })

    # 底背离检测
    for i in range(1, len(price_troughs)):
        curr_pt, prev_pt = price_troughs[i], price_troughs[i-1]
        curr_price, prev_price = closes.iloc[curr_pt], closes.iloc[prev_pt]
        
        # 如果当前价格没有创新低，则不是典型的底背离情况
        if curr_price >= prev_price:
            continue
            
        # 改进的峰值匹配逻辑
        curr_macd_trough = _find_closest_peak(curr_pt, macd_troughs)
        prev_macd_trough = _find_closest_peak(prev_pt, macd_troughs)
        
        # 如果找不到对应的MACD谷值，跳过
        if curr_macd_trough is None or prev_macd_trough is None:
            continue
            
        # 使用更宽松的时间顺序检查
        if prev_macd_trough > curr_macd_trough:
            continue
            
        macd_prev, macd_curr = macd.iloc[prev_macd_trough], macd.iloc[curr_macd_trough]

        # 验证背离：价格创新低但MACD走高
        if macd_curr > macd_prev:
            # 计算背离强度
            price_change_pct = (curr_price - prev_price) / prev_price
            macd_std = macd.iloc[-lookback:].std()
            macd_change_z = (macd_curr - macd_prev) / macd_std if macd_std != 0 else 0
            strength = abs(price_change_pct - macd_change_z)
            
            # 添加背离强度过滤，只保留强度大于阈值的背离
            min_strength = 0.03  # 设置最小强度阈值
            if strength > min_strength:
                divergences.append({
                    'type': '底背离',
                    'index': curr_pt,
                    'strength': strength,
                    'price_points': (prev_pt, curr_pt),
                    'macd_points': (prev_macd_trough, curr_macd_trough)
                })

    return divergences


def plot_macd(ax, data, macd_data):
    """
    在指定的坐标轴上绘制MACD指标
    
    参数:
    ax: matplotlib坐标轴对象
    data: DataFrame，原始OHLC数据
    macd_data: DataFrame，包含MACD指标数据
    """
    # 绘制DIF和DEA线
    ax.plot(range(len(data)), macd_data['DIF'], color='blue', linewidth=1, label='DIF')
    ax.plot(range(len(data)), macd_data['DEA'], color='red', linewidth=1, label='DEA')
    
    # 绘制MACD柱状图
    for i in range(len(data)):
        if i < len(macd_data):
            macd_val = macd_data['MACD'].iloc[i]
            color = 'red' if macd_val >= 0 else 'green'
            ax.bar(i, macd_val, width=0.6, color=color, alpha=0.5)
    
    # 添加零线
    ax.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
    
    # 添加图例
    ax.legend(loc='upper left', fontsize='small')


def plot_macd_divergences(ax, data, divergences):
    """
    在价格图表上标记MACD背离
    
    参数:
    ax: matplotlib坐标轴对象
    data: DataFrame，原始OHLC数据
    divergences: list，背离信息列表
    """
    for div in divergences:
        idx = div['index']
        # 确保索引在有效范围内
        if idx < 0 or idx >= len(data):
            continue
            
        price = data['High'].iloc[idx] if div['type'] == '顶背离' else data['Low'].iloc[idx]
        offset = (ax.get_ylim()[1] - ax.get_ylim()[0]) * 0.02
        y_pos = price + offset if div['type']=='顶背离' else price - offset
        
        div_color = '#F44336' if div['type']=='顶背离' else '#4CAF50'
        
        ax.annotate(div['type'], xy=(idx, price), 
                    xytext=(idx, y_pos),
                    arrowprops=dict(facecolor=div_color, shrink=0.05, width=2),
                    fontsize=9, color=div_color, fontweight='bold',
                    horizontalalignment='center',
                    verticalalignment='bottom' if div['type']=='顶背离' else 'top',
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor=div_color, boxstyle='round,pad=0.2')) 