"""
一目均衡表(Ichimoku Cloud)指标模块
提供计算和绘制一目均衡表的功能
"""
import pandas as pd
import numpy as np


def calculate_ichimoku(data, short_period=9, mid_period=26, long_period=52, 
                       interval='1d', period='3mo'):
    """
    计算一目均衡表指标
    
    参数:
    data: DataFrame，包含OHLC数据的DataFrame
    short_period: 短期周期(转换线)，默认为9
    mid_period: 中期周期(基准线)，默认为26
    long_period: 长期周期(先行带B)，默认为52
    interval: 时间间隔，用于调整参数
    period: 时间周期，用于调整参数
    
    返回:
    包含一目均衡表指标的DataFrame
    """
    # 获取当前数据集大小
    data_length = len(data)
    
    # 日线数据特殊处理
    if interval == '1d':
        # 根据数据量自适应调整周期
        if data_length < 60:  # 3个月左右的日线数据
            # 大幅缩短周期以适应小数据集
            adjusted_factor = max(0.5, min(1.0, data_length / long_period))
            short_period = max(3, int(short_period * adjusted_factor))
            mid_period = max(5, int(mid_period * adjusted_factor))
            long_period = max(10, int(long_period * adjusted_factor))
            
            # 确保先行带计算有足够数据
            if mid_period + long_period > data_length:
                long_period = max(10, data_length - mid_period - 5)
        
    # 分钟线数据处理
    elif interval == '1m':
        # 对于分钟级别的数据，大幅缩短周期
        short_period = max(3, short_period // 6)
        mid_period = max(6, mid_period // 6)
        long_period = max(12, long_period // 6)
    elif interval == '5m':
        short_period = max(3, short_period // 5)
        mid_period = max(6, mid_period // 5)
        long_period = max(12, long_period // 5)
    elif interval == '15m':
        short_period = max(3, short_period // 4)
        mid_period = max(7, mid_period // 4)
        long_period = max(13, long_period // 4)
    elif interval == '30m':
        short_period = max(4, short_period // 3)
        mid_period = max(9, mid_period // 3)
        long_period = max(18, long_period // 3)
    elif interval == '60m' or interval == '1h':
        short_period = max(5, short_period // 2)
        mid_period = max(12, mid_period // 2)
        long_period = max(26, long_period // 2)
        
    # 最终安全检查 - 确保参数不会导致所有数据变为NaN
    if mid_period >= data_length:
        mid_period = max(5, data_length // 3)
    if long_period >= data_length:
        long_period = max(10, data_length // 2)
        
    # 检查数据是否足够计算指标
    if len(data) < max(short_period, mid_period, long_period):
        # 如果数据点不足，返回一个空的DataFrame，但保持相同的索引
        empty_df = pd.DataFrame(index=data.index)
        for col in ['conversion_line', 'base_line', 'lagging_span', 'leading_span_a', 'leading_span_b']:
            empty_df[col] = np.nan
        return empty_df
        
    # 创建结果DataFrame
    ichimoku = pd.DataFrame(index=data.index)
    
    # 计算转换线 (Conversion Line / Tenkan-sen)
    high_short = data['High'].rolling(window=short_period).max()
    low_short = data['Low'].rolling(window=short_period).min()
    ichimoku['conversion_line'] = (high_short + low_short) / 2
    
    # 计算基准线 (Base Line / Kijun-sen)
    high_mid = data['High'].rolling(window=mid_period).max()
    low_mid = data['Low'].rolling(window=mid_period).min()
    ichimoku['base_line'] = (high_mid + low_mid) / 2
    
    # 计算迟行带 (Lagging Span / Chikou Span)
    # 使用较小的偏移值，避免出现过多的NaN
    lag_shift = min(mid_period, data_length // 4)
    ichimoku['lagging_span'] = data['Close'].shift(-lag_shift) 
    
    # 计算先行带A (Leading Span A / Senkou Span A)
    # 使用较小的偏移值，避免出现过多的NaN
    span_shift = min(mid_period, data_length // 5)
    ichimoku['leading_span_a'] = ((ichimoku['conversion_line'] + ichimoku['base_line']) / 2).shift(span_shift)
    
    # 计算先行带B (Leading Span B / Senkou Span B)
    high_long = data['High'].rolling(window=long_period).max()
    low_long = data['Low'].rolling(window=long_period).min()
    ichimoku['leading_span_b'] = ((high_long + low_long) / 2).shift(span_shift)
    
    return ichimoku


def plot_ichimoku(ax, data, ichimoku):
    """
    在指定的坐标轴上绘制一目均衡表
    
    参数:
    ax: matplotlib坐标轴对象
    data: 原始OHLC数据
    ichimoku: 计算得到的一目均衡表指标
    """
    # 检查数据是否有效
    if ichimoku.empty or ichimoku['conversion_line'].dropna().empty:
        # 如果数据无效，在图表上显示提示信息
        ax.text(0.5, 0.5, "数据不足，无法显示一目均衡表", 
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=12, color='red',
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', boxstyle='round,pad=0.5'))
        return
        
    # 绘制转换线和基准线 - 使用虚线和点线以区分MA均线和阻力线
    ax.plot(range(len(data)), ichimoku['conversion_line'], color='#673AB7', linewidth=1.2, 
            linestyle='--', label='转换线(9)', alpha=0.8)
    ax.plot(range(len(data)), ichimoku['base_line'], color='#3F51B5', linewidth=1.2, 
            linestyle='-.', label='基准线(26)', alpha=0.8)
    
    # 绘制迟行带 - 使用点线
    ax.plot(range(len(data)), ichimoku['lagging_span'], color='#00BCD4', linewidth=1, 
            linestyle=':', label='迟行带', alpha=0.7)
    
    # 获取先行带A和B
    span_a = ichimoku['leading_span_a']
    span_b = ichimoku['leading_span_b']
    
    # 绘制先行带A和B - 使用特殊线条样式
    ax.plot(range(len(data)), span_a, color='#4CAF50', linewidth=1.2, 
            linestyle='-', label='先行带A', alpha=0.9)
    ax.plot(range(len(data)), span_b, color='#8BC34A', linewidth=1.2, 
            linestyle='-', label='先行带B', alpha=0.9)
    
    # 改进云层绘制，使用更稳健的方法
    valid_indices = ~(pd.isna(span_a) | pd.isna(span_b))
    valid_x = np.array(range(len(data)))[valid_indices]
    
    if len(valid_x) > 0:
        valid_span_a = np.array(span_a)[valid_indices]
        valid_span_b = np.array(span_b)[valid_indices]
        
        if len(valid_x) > 1:  # 确保至少有两个有效点才进行填充
            # 绿色区域 (看涨云)
            bull_cloud = valid_span_a >= valid_span_b
            if np.any(bull_cloud):
                ax.fill_between(valid_x[bull_cloud], 
                               valid_span_a[bull_cloud], 
                               valid_span_b[bull_cloud], 
                               color='#00C853', alpha=0.15)
            
            # 红色区域 (看跌云)
            bear_cloud = ~bull_cloud
            if np.any(bear_cloud):
                ax.fill_between(valid_x[bear_cloud], 
                               valid_span_a[bear_cloud], 
                               valid_span_b[bear_cloud], 
                               color='#FF1744', alpha=0.15) 