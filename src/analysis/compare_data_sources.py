import pandas as pd
import matplotlib.pyplot as plt
import sys
import os

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.data_providers.stock_data_provider import StockDataProviderFactory

def compare_data_sources(stock_code, period="3mo", interval="1d", show_plots=False):
    """比较Yahoo和Sina数据源的数据"""
    # 获取两个数据提供者
    yahoo_provider = StockDataProviderFactory.get_provider("yahoo")
    sina_provider = StockDataProviderFactory.get_provider("sina")
    
    # 获取数据
    print(f"正在从Yahoo获取 {stock_code} 的数据...")
    try:
        yahoo_data = yahoo_provider.get_stock_data(stock_code, period, interval)
        print(f"Yahoo数据获取成功，共 {len(yahoo_data)} 条记录")
    except Exception as e:
        print(f"从Yahoo获取数据失败: {str(e)}")
        yahoo_data = None
    
    print(f"正在从新浪获取 {stock_code} 的数据...")
    try:
        sina_data = sina_provider.get_stock_data(stock_code, period, interval)
        print(f"新浪数据获取成功，共 {len(sina_data)} 条记录")
    except Exception as e:
        print(f"从新浪获取数据失败: {str(e)}")
        sina_data = None
    
    # 如果两个数据源都获取成功，进行比较
    if yahoo_data is not None and sina_data is not None and not yahoo_data.empty and not sina_data.empty:
        # 打印数据基本信息
        print("\n数据基本信息比较:")
        print(f"Yahoo数据范围: {yahoo_data.index.min()} 至 {yahoo_data.index.max()}")
        print(f"新浪数据范围: {sina_data.index.min()} 至 {sina_data.index.max()}")
        
        # 打印最新的几条数据进行比较
        print("\nYahoo最新数据:")
        print(yahoo_data.tail().to_string())
        
        print("\n新浪最新数据:")
        print(sina_data.tail().to_string())
        
        # 处理日期索引格式不同的问题
        # 将Yahoo数据的日期索引转换为日期字符串，格式为YYYY-MM-DD
        yahoo_data_copy = yahoo_data.copy()
        yahoo_data_copy.index = yahoo_data_copy.index.strftime('%Y-%m-%d')
        
        # 将新浪数据的日期索引也转换为相同格式
        sina_data_copy = sina_data.copy()
        sina_data_copy.index = sina_data_copy.index.strftime('%Y-%m-%d')
        
        # 计算价格差异
        print("\n价格差异分析:")
        # 尝试找到共同的日期
        common_dates = set(yahoo_data_copy.index).intersection(set(sina_data_copy.index))
        if len(common_dates) > 0:
            print(f"找到 {len(common_dates)} 个共同交易日")
            
            # 提取共同日期的数据
            yahoo_common = yahoo_data_copy.loc[list(common_dates)]
            sina_common = sina_data_copy.loc[list(common_dates)]
            
            # 确保数据按日期排序
            yahoo_common = yahoo_common.sort_index()
            sina_common = sina_common.sort_index()
            
            # 计算收盘价差异
            close_diff = (yahoo_common['Close'] - sina_common['Close']).abs()
            close_diff_pct = close_diff / yahoo_common['Close'] * 100
            
            print(f"收盘价平均差异: {close_diff.mean():.4f}")
            print(f"收盘价最大差异: {close_diff.max():.4f}")
            print(f"收盘价差异百分比平均值: {close_diff_pct.mean():.2f}%")
            
            # 打印最大差异的日期
            max_diff_date = close_diff.idxmax()
            print(f"最大差异日期: {max_diff_date}")
            print(f"Yahoo收盘价: {yahoo_common.loc[max_diff_date, 'Close']}")
            print(f"新浪收盘价: {sina_common.loc[max_diff_date, 'Close']}")
            
            # 计算成交量差异
            volume_diff = (yahoo_common['Volume'] - sina_common['Volume']).abs()
            volume_diff_pct = volume_diff / yahoo_common['Volume'] * 100
            
            print(f"\n成交量平均差异: {volume_diff.mean():.0f}")
            print(f"成交量最大差异: {volume_diff.max():.0f}")
            print(f"成交量差异百分比平均值: {volume_diff_pct.mean():.2f}%")
            
            # 打印最大成交量差异的日期
            max_volume_diff_date = volume_diff.idxmax()
            print(f"最大成交量差异日期: {max_volume_diff_date}")
            print(f"Yahoo成交量: {yahoo_common.loc[max_volume_diff_date, 'Volume']}")
            print(f"新浪成交量: {sina_common.loc[max_volume_diff_date, 'Volume']}")
            
            # 只有在需要时才显示图表
            if show_plots:
                # 绘制收盘价对比图
                plt.figure(figsize=(12, 6))
                plt.plot(yahoo_common.index, yahoo_common['Close'], label='Yahoo收盘价')
                plt.plot(sina_common.index, sina_common['Close'], label='新浪收盘价')
                plt.title(f"{stock_code} 收盘价对比")
                plt.xlabel("日期")
                plt.ylabel("价格")
                plt.legend()
                plt.grid(True)
                plt.tight_layout()
                plt.show()
                
                # 绘制成交量对比图
                plt.figure(figsize=(12, 6))
                plt.bar(yahoo_common.index, yahoo_common['Volume'], alpha=0.5, label='Yahoo成交量')
                plt.bar(sina_common.index, sina_common['Volume'], alpha=0.5, label='新浪成交量')
                plt.title(f"{stock_code} 成交量对比")
                plt.xlabel("日期")
                plt.ylabel("成交量")
                plt.legend()
                plt.grid(True)
                plt.tight_layout()
                plt.show()
            
            # 打印数据列比较
            print("\n数据列比较:")
            print(f"Yahoo数据列: {', '.join(yahoo_data.columns)}")
            print(f"新浪数据列: {', '.join(sina_data.columns)}")
            
            # 检查是否有其他差异
            print("\n其他差异检查:")
            for col in ['Open', 'High', 'Low']:
                if col in yahoo_common.columns and col in sina_common.columns:
                    col_diff = (yahoo_common[col] - sina_common[col]).abs()
                    col_diff_pct = col_diff / yahoo_common[col] * 100
                    print(f"{col}价格平均差异: {col_diff.mean():.4f} ({col_diff_pct.mean():.2f}%)")
        else:
            print("两个数据源没有共同的交易日期")
            print("Yahoo日期:", yahoo_data_copy.index[:5].tolist(), "...")
            print("新浪日期:", sina_data_copy.index[:5].tolist(), "...")
    else:
        print("无法比较数据，至少有一个数据源获取失败或返回空数据")

if __name__ == "__main__":
    # 测试几个不同市场的股票
    stocks = [
        ("AAPL", "美股 - 苹果"),
        ("600000", "A股 - 浦发银行"),
        ("000001", "A股 - 平安银行"),
        ("00700", "港股 - 腾讯控股")
    ]
    
    for stock_code, desc in stocks:
        print(f"\n{'='*50}")
        print(f"比较 {desc} ({stock_code}) 的数据")
        print(f"{'='*50}")
        compare_data_sources(stock_code, show_plots=False)
        print("\n") 