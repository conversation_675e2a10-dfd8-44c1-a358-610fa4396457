import pandas as pd
import numpy as np
from src.utils.logger import Logger
from src.ui.indicators.ichimoku import calculate_ichimoku
from src.config.config_manager import config_manager

class TechnicalAnalyzer:
    """技术分析处理器"""
    def __init__(self, data=None):
        """
        初始化技术分析处理器
        :param data: DataFrame 数据
        """
        self.data = data
        # 从配置管理器获取一目均衡表参数
        self.ichimoku_params = config_manager.get_config('ichimoku')
        self.last_analysis_type = None  # 添加last_analysis_type属性，记录最近执行的分析类型
        if data is not None:
            Logger.debug("技术分析初始化:")
            Logger.debug(f"- 数据列: {', '.join(self.data.columns)}")
            Logger.debug(f"- 数据行数: {len(self.data)}")

    def set_data(self, data, data_source='market'):
        """
        设置要分析的数据
        :param data: DataFrame 数据
        :param data_source: 数据来源，默认为'market'
        """
        self.data = data
        self.data_source = data_source

    def _calculate_ichimoku_scores(self, recent_data, recent_ichimoku, price_col):
        """直接基于一目均衡表规则计算得分"""
        scores = {
            'cloud_score': [],
            'conversion_base_score': [],
            'price_base_score': [],
            'chikou_score': [],
            'future_cloud_score': []
        }
        
        for i in range(len(recent_data)):
            price = recent_data[price_col].iloc[i]
            span_a = recent_ichimoku['leading_span_a'].iloc[i]
            span_b = recent_ichimoku['leading_span_b'].iloc[i]
            conversion = recent_ichimoku['conversion_line'].iloc[i]
            base = recent_ichimoku['base_line'].iloc[i]
            
            # 1. 云层位置得分
            # 检查是否有NaN值
            if np.isnan(span_a) or np.isnan(span_b) or np.isnan(price):
                Logger.debug(f"第{i}天的云层数据不完整: span_a={span_a}, span_b={span_b}, price={price}")
                scores['cloud_score'].append(0)  # 给予中性分数
            else:
                cloud_top = max(span_a, span_b)
                cloud_bottom = min(span_a, span_b)
                
                if price > cloud_top:
                    scores['cloud_score'].append(100)
                elif price < cloud_bottom:
                    scores['cloud_score'].append(-100)
                else:
                    # 在云内，根据位置给予 -50 到 50 的分数
                    # 确保分母不为0
                    if cloud_top == cloud_bottom:
                        scores['cloud_score'].append(0)  # 云层厚度为0时给予中性分数
                    else:
                        relative_pos = (price - cloud_bottom) / (cloud_top - cloud_bottom)
                        scores['cloud_score'].append((relative_pos * 100) - 50)
                
            # 2. 转换线与基准线关系得分
            if np.isnan(conversion) or np.isnan(base):
                scores['conversion_base_score'].append(0)
            elif conversion > base:
                scores['conversion_base_score'].append(100)
            elif conversion < base:
                scores['conversion_base_score'].append(-100)
            else:
                scores['conversion_base_score'].append(0)
                
            # 3. 价格与基准线关系得分
            if np.isnan(price) or np.isnan(base):
                scores['price_base_score'].append(0)
            elif price > base:
                scores['price_base_score'].append(100)
            elif price < base:
                scores['price_base_score'].append(-100)
            else:
                scores['price_base_score'].append(0)
                
            # 4. 迟行带位置得分
            chikou_period = self.ichimoku_params['base']  # 使用 base (例如 15) 作为 N 周期

            # 确保能获取到 N 周期前的数据点
            if i >= chikou_period:
                # 获取 N 周期前的价格 (近似为迟行线在图表上的值)
                chikou_value_approx = recent_data[price_col].iloc[i - chikou_period]
                # 获取 N 周期前的云层数据
                past_span_a_approx = recent_ichimoku['leading_span_a'].iloc[i - chikou_period]
                past_span_b_approx = recent_ichimoku['leading_span_b'].iloc[i - chikou_period]
                
                # 检查NaN值
                if np.isnan(chikou_value_approx) or np.isnan(past_span_a_approx) or np.isnan(past_span_b_approx):
                    scores['chikou_score'].append(0)
                else:
                    past_cloud_top_approx = max(past_span_a_approx, past_span_b_approx)
                    past_cloud_bottom_approx = min(past_span_a_approx, past_span_b_approx)

                    if chikou_value_approx > past_cloud_top_approx:
                        scores['chikou_score'].append(100)  # 高于过去云层
                    elif chikou_value_approx < past_cloud_bottom_approx:
                        scores['chikou_score'].append(-100)  # 低于过去云层
                    else:
                        scores['chikou_score'].append(0)  # 在过去云层内
            else:
                # 对于没有足够历史数据的点，给予中性分数
                scores['chikou_score'].append(0)
                
            # 5. 未来云层形态得分
            if np.isnan(span_a) or np.isnan(span_b):
                scores['future_cloud_score'].append(0)
            elif span_a > span_b:
                scores['future_cloud_score'].append(100)
            elif span_a < span_b:
                scores['future_cloud_score'].append(-100)
            else:
                scores['future_cloud_score'].append(0)
                
        # 计算每个维度的平均分
        final_scores = {}
        for key in scores:
            valid_scores = [score for score in scores[key] if not np.isnan(score)]  # 过滤掉NaN值
            if valid_scores:
                final_scores[key] = sum(valid_scores) / len(valid_scores)
            else:
                final_scores[key] = 0
                Logger.warning(f"{key}没有有效的得分数据，使用默认值0")
                
        return final_scores

    def check_ichimoku_signal(self, days=30):
        """检查一目均衡表信号"""
        try:
            Logger.info(f"开始一目均衡表信号分析，检查天数: {days}")
            Logger.info(f"使用的一目均衡表参数: {self.ichimoku_params}")

            # 检查输入数据
            Logger.info(f"输入数据形状: {self.data.shape}")
            Logger.info(f"输入数据列: {self.data.columns.tolist()}")
            Logger.info(f"输入数据是否有空值: {self.data.isnull().sum().to_dict()}")

            # 计算一目均衡表指标
            ichimoku = calculate_ichimoku(
                self.data,
                short_period=self.ichimoku_params['conversion'],
                mid_period=self.ichimoku_params['base'],
                long_period=self.ichimoku_params['span_b']
            )
            
            Logger.info(f"一目均衡表计算结果形状: {ichimoku.shape}")
            Logger.info(f"一目均衡表计算结果列: {ichimoku.columns.tolist()}")
            Logger.info(f"一目均衡表是否有空值: {ichimoku.isnull().sum().to_dict()}")
            
            # 获取最近n天的数据
            recent_data = self.data.tail(days)
            recent_ichimoku = ichimoku.tail(days)
            
            Logger.info(f"数据验证 - 总数据量: {len(self.data)}, 最近数据量: {len(recent_data)}")
            
            # 统一查找价格列
            price_cols = ['Close', '价格', '收盘价', '当前价']
            price_col = next((col for col in price_cols if col in self.data.columns), None)
            if not price_col:
                raise ValueError("找不到价格列")
            
            Logger.info(f"使用的价格列: {price_col}")
            Logger.info(f"价格数据是否有空值: {recent_data[price_col].isnull().sum()}")

            # 计算在云层上方的天数
            days_above_cloud = 0
            for i in range(len(recent_data)):
                price = recent_data[price_col].iloc[i]
                span_a = recent_ichimoku['leading_span_a'].iloc[i]
                span_b = recent_ichimoku['leading_span_b'].iloc[i]
                cloud_top = max(span_a, span_b)
                if price > cloud_top:
                    days_above_cloud += 1
                    
            true_above_ratio = days_above_cloud / len(recent_data)
            
            # 计算各维度得分
            scores = self._calculate_ichimoku_scores(recent_data, recent_ichimoku, price_col)
            
            # 输出每个维度的原始得分
            Logger.info("原始维度得分:")
            for key, value in scores.items():
                Logger.info(f"- {key}: {value}")
            
            # 权重配置
            weights = {
                'cloud': 0.40,        # 云层位置最重要
                'conversion_base': 0.20,  # 转换线和基准线关系次之
                'price_base': 0.20,    # 价格与基准线关系同等重要
                'chikou': 0.15,       # 迟行带
                'future_cloud': 0.05  # 未来云层形态影响最小
            }
            
            # 计算总分
            total_score = (
                scores['cloud_score'] * weights['cloud'] +
                scores['conversion_base_score'] * weights['conversion_base'] +
                scores['price_base_score'] * weights['price_base'] +
                scores['chikou_score'] * weights['chikou'] +
                scores['future_cloud_score'] * weights['future_cloud']
            )
            
            Logger.info(f"计算一目均衡表总分的中间结果:")
            Logger.info(f"- cloud_score * {weights['cloud']} = {scores['cloud_score'] * weights['cloud']}")
            Logger.info(f"- conversion_base_score * {weights['conversion_base']} = {scores['conversion_base_score'] * weights['conversion_base']}")
            Logger.info(f"- price_base_score * {weights['price_base']} = {scores['price_base_score'] * weights['price_base']}")
            Logger.info(f"- chikou_score * {weights['chikou']} = {scores['chikou_score'] * weights['chikou']}")
            Logger.info(f"- future_cloud_score * {weights['future_cloud']} = {scores['future_cloud_score'] * weights['future_cloud']}")
            Logger.info(f"一目均衡表基础得分: {total_score:.2f}")

            # --- 结合 MA10 回踩支撑进行加分 ---
            ma10_pullback_bonus = 0
            scores['ma10_pullback_bonus'] = 0
            try:
                # 检查最近1天的MA10回踩 (check_last_n_days=1)
                # 使用与一目均衡表相同的回看天数或默认值
                ma_lookback = max(days, 30) # 确保有足够数据计算MA10
                ma_meets_criteria, ma_result = self.check_ma_pullback(
                    ma_period=10,
                    lookback_days=ma_lookback,
                    check_last_n_days=1 # 只检查最近一天
                )
                
                if ma_meets_criteria and ma_result.get('type') == 'completed_effective' and ma_result.get('details', {}).get('days_ago') == 0:
                    ma10_score = ma_result.get('score', 0)
                    # 加分规则：回踩得分的20%，最高加20分
                    ma10_pullback_bonus = min(20, ma10_score * 0.2)
                    scores['ma10_pullback_bonus'] = ma10_pullback_bonus
                    total_score += ma10_pullback_bonus
                    Logger.info(f"检测到MA10有效支撑，加分: {ma10_pullback_bonus:.2f} (基于回踩得分 {ma10_score:.1f})")
                else:
                    Logger.info("未检测到最近1天的MA10有效支撑，无加分。")

            except Exception as ma_e:
                Logger.warning(f"检查MA10回踩时发生错误，跳过加分: {str(ma_e)}")
            # --- MA10 加分结束 ---

            # 记录各维度得分和最终总分
            Logger.info("各维度得分:")
            Logger.info(f"- 云层位置得分: {scores['cloud_score']:.2f}")
            Logger.info(f"- 转换线与基准线关系得分: {scores['conversion_base_score']:.2f}")
            Logger.info(f"- 价格与基准线关系得分: {scores['price_base_score']:.2f}")
            Logger.info(f"- 迟行带位置得分: {scores['chikou_score']:.2f}")
            Logger.info(f"- 未来云层形态得分: {scores['future_cloud_score']:.2f}")
            Logger.info(f"- MA10回踩加分: {scores['ma10_pullback_bonus']:.2f}")
            Logger.info(f"- 实际在云层上方的比例: {true_above_ratio:.2%}")
            Logger.info(f"最终综合得分: {total_score:.2f}")
            
            # 判断标准 (基于调整后的总分)
            meets_criteria = (
                total_score > 30 and          # 总分为正且显著
                scores['cloud_score'] > 50 and # 价格明显在云层上方 (基础条件不变)
                true_above_ratio >= 0.6       # 保持至少60%时间在云层上方 (基础条件不变)
            )
            
            Logger.info(f"判断结果: {'符合条件' if meets_criteria else '不符合条件'}")
            Logger.info(f"- 最终总分 > 30: {total_score > 30}")
            Logger.info(f"- 云层得分 > 50: {scores['cloud_score'] > 50}")
            Logger.info(f"- 实际在云层上方比例 >= 60%: {true_above_ratio >= 0.6}")
            
            scores['total_score'] = total_score # 使用调整后的总分
            scores['true_above_ratio'] = true_above_ratio
            
            return meets_criteria, scores
            
        except Exception as e:
            Logger.error(f"检查一目均衡表信号失败: {str(e)}")
            raise

    def analyze_ichimoku(self, stock_code, stock_name, params):
        """
        一目均衡表分析的统一接口
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            params: 分析参数
            
        Returns:
            Dict: 分析结果
        """
        try:
            days = params.get('days', 5)
            meets_criteria, scores = self.check_ichimoku_signal(days)

            Logger.info(f"一目均衡表分析详细得分 ({stock_code} {stock_name}):")
            # scores 字典包含了所有计算出的详细评分项，
            # 例如: cloud_score, conversion_base_score, price_base_score, chikou_score,
            # future_cloud_score, ma10_pullback_bonus, total_score, true_above_ratio
            for key, value in scores.items():
                if isinstance(value, float):
                    Logger.info(f"  - {key}: {value:.2f}")
                else:
                    Logger.info(f"  - {key}: {value}")
            
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': meets_criteria,
                'total_score': scores.get('total_score', 0),
                'scores': scores
            }
        except Exception as e:
            Logger.error(f"一目均衡表分析失败: {str(e)}")
            return None

    def analyze_ichimoku_early(self, stock_code, stock_name, params):
        """
        一目均衡表早期信号分析，主要关注云层突破
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            params: 分析参数，包含：
                - days: 用于计算的历史天数
                - lookback_days: 检查最近几天的突破，默认3天
            
        Returns:
            Dict: 分析结果
        """
        try:
            days = params.get('days', 52)  # 默认使用更长的历史数据以确保计算准确
            lookback_days = params.get('lookback_days', 3)
            
            meets_criteria, scores = self.check_ichimoku_early_signal(days, lookback_days)
            
            result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': meets_criteria,
                'total_score': scores.get('total_score', 0),
                'scores': scores
            }
            
            # 记录分析结果
            Logger.info(f"一目均衡表早期信号分析结果 - {stock_code} {stock_name}:")
            Logger.info(f"- 符合条件: {meets_criteria}")
            if scores.get('breakout_details'):
                Logger.info("突破详情:")
                for key, value in scores['breakout_details'].items():
                    if isinstance(value, (int, float)):
                        Logger.info(f"  - {key}: {value:.2f}")
                    else:
                        Logger.info(f"  - {key}: {value}")
            
            return result
            
        except Exception as e:
            Logger.error(f"一目均衡表早期信号分析失败: {str(e)}")
            return None

    def check_ichimoku_early_signal(self, days=52, lookback_days=3):
        """
        检查一目均衡表早期信号，主要关注云层突破
        
        Args:
            days: 用于计算一目均衡表指标的历史天数，默认52天（建议 >= ichimoku_params['span_b'] * 2）
            lookback_days: 检查最近几天的突破，默认3天
            
        Returns:
            Tuple[bool, Dict]: (是否符合条件, 详细信息)
                - breakout_detected: 是否检测到突破
                - breakout_details: 突破详情（如果有）
                    - breakout_day: 突破日期
                    - breakout_price: 突破时的价格
                    - cloud_top: 突破时的云层顶部
                    - breakout_strength: 突破强度（百分比）
                    - days_since_breakout: 距今天数（0表示最后一天）
                - total_score: 综合得分（50-100）
        """
        try:
            Logger.info(f"开始检查云层突破信号，计算天数: {days}, 检查最近: {lookback_days}天")
            Logger.info(f"使用的一目均衡表参数: {self.ichimoku_params}")

            # 1. 数据量检查
            required_data_length = max(days, self.ichimoku_params['span_b'] * 2)
            if len(self.data) < required_data_length:
                raise ValueError(f"数据量不足 ({len(self.data)}行) 无法进行准确的分析 (需要至少{required_data_length}行)")

            # 2. 统一查找价格列
            price_cols = ['Close', '价格', '收盘价', '当前价']
            price_col = next((col for col in price_cols if col in self.data.columns), None)
            if not price_col:
                raise ValueError("找不到价格列")

            # 3. 计算一目均衡表指标
            # 使用足够长的数据进行计算，确保指标的准确性
            calc_data = self.data.tail(required_data_length).copy()
            ichimoku = calculate_ichimoku(
                calc_data,
                short_period=self.ichimoku_params['conversion'],
                mid_period=self.ichimoku_params['base'],
                long_period=self.ichimoku_params['span_b']
            )

            # 4. 合并数据，确保索引对齐
            analysis_data = pd.DataFrame(index=calc_data.index)
            analysis_data[price_col] = calc_data[price_col]
            analysis_data['leading_span_a'] = ichimoku['leading_span_a']
            analysis_data['leading_span_b'] = ichimoku['leading_span_b']

            # 5. 获取用于比较的数据
            check_data = analysis_data.tail(lookback_days + 1).copy()  # +1是为了包含第一天比较的前一天
            
            # 6. 初始化默认结果
            result = {
                'breakout_detected': False,
                'breakout_details': {},
                'total_score': 0
            }
            meets_criteria = False

            # 7. 检查是否有足够的数据进行比较
            if len(check_data) < 2:
                Logger.warning(f"在最近 {lookback_days + 1} 天内有效的价格/云层数据不足2条，无法比较突破。")
                return False, result

            # 8. 检查云层突破
            # 从第2行开始迭代，比较 i 和 i-1
            for i in range(1, len(check_data)):
                try:
                    # 获取当前和前一天的数据
                    curr_row = check_data.iloc[i]
                    prev_row = check_data.iloc[i-1]
                    
                    # 提取所需的值
                    curr_price = curr_row[price_col]
                    prev_price = prev_row[price_col]
                    curr_span_a = curr_row['leading_span_a']
                    curr_span_b = curr_row['leading_span_b']
                    prev_span_a = prev_row['leading_span_a']
                    prev_span_b = prev_row['leading_span_b']

                    # 检查数据有效性
                    if any(pd.isna(x) for x in [curr_price, prev_price, curr_span_a, curr_span_b, prev_span_a, prev_span_b]):
                        Logger.debug(f"跳过日期 {check_data.index[i]} 的检查，存在无效数据")
                        continue

                    # 计算云层顶部
                    curr_cloud_top = max(curr_span_a, curr_span_b)
                    prev_cloud_top = max(prev_span_a, prev_span_b)

                    # 检查突破条件
                    if prev_price <= prev_cloud_top and curr_price > curr_cloud_top:
                        # 计算突破强度（相对于云层顶部的超出百分比）
                        breakout_strength = ((curr_price - curr_cloud_top) / curr_cloud_top) * 100
                        
                        # 记录突破信息
                        result = {
                            'breakout_detected': True,
                            'breakout_details': {
                                'breakout_day': check_data.index[i].strftime('%Y-%m-%d'),
                                'breakout_price': float(curr_price),
                                'cloud_top': float(curr_cloud_top),
                                'breakout_strength': float(breakout_strength),
                                'days_since_breakout': len(check_data) - 1 - i  # 0表示发生在检查窗口的最后一天
                            },
                            'total_score': min(100, max(50, 50 + breakout_strength * 2))
                        }
                        
                        # 更新判断条件
                        meets_criteria = result['total_score'] >= 50
                        
                        # 记录详细日志
                        Logger.info(f"检测到云层突破：")
                        Logger.info(f"- 突破日期: {result['breakout_details']['breakout_day']}")
                        Logger.info(f"- 突破价格: {curr_price:.2f}")
                        Logger.info(f"- 云层顶部: {curr_cloud_top:.2f}")
                        Logger.info(f"- 突破强度: {breakout_strength:.2f}%")
                        Logger.info(f"- 距今天数: {result['breakout_details']['days_since_breakout']}")
                        Logger.info(f"- 得分: {result['total_score']:.2f}")
                        
                        # 找到第一个突破就返回
                        return meets_criteria, result

                except Exception as e:
                    Logger.warning(f"处理日期 {check_data.index[i]} 的数据时出错: {str(e)}")
                    continue

            # 如果循环结束都没有返回，说明没有找到突破
            Logger.info(f"在最近 {lookback_days} 天内未检测到价格向上突破云层事件。")
            return False, result

        except Exception as e:
            Logger.error(f"检查云层突破信号失败: {str(e)}")
            raise

    def check_ma_pullback(self, ma_period=10, lookback_days=30, check_last_n_days=3,
                         upper_proximity=1.0, allowed_negative_proximity=-0.3,
                         approaching_proximity=3.0, min_prev_distance=1.0,
                         min_ma_slope=0.0, min_lower_shadow_ratio=0.4,
                         min_volume_increase_ratio=1.1, volume_avg_period=5):
        """
        检查最近几天是否出现有效的均线支撑回踩。
        结合收盘价位置、K线形态（长下影线）、成交量进行判断。

        Args:
            ma_period (int): 均线周期，如10代表MA10，20代表MA20等.
            lookback_days (int): 计算均线/VMA等所需的总回看天数.
            check_last_n_days (int): 重点检查最近的 N 天.
            upper_proximity (float): 完成回踩时，收盘价允许高于均线的最大百分比.
            allowed_negative_proximity (float): 完成回踩时，收盘价允许低于均线的最大百分比 (通常为负数或0).
            approaching_proximity (float): 接近回踩的宽松距离阈值 (%).
            min_prev_distance (float): 要求之前价格至少高于均线多少 (%) 才算有效回落.
            min_ma_slope (float): 均线的最小上升斜率要求 (%).
            min_lower_shadow_ratio (float): K线长下影线的最小比例 (下影线长度 / K线总幅度).
            min_volume_increase_ratio (float): 支撑日成交量相对于前N日均量的最小倍数.
            volume_avg_period (int): 计算成交量均值的周期.

        Returns:
            Tuple[bool, Dict]: (是否检测到有效支撑信号, 详细信息)
        """
        try:
            Logger.info(f"开始检查最近{check_last_n_days}天的有效MA{ma_period}支撑，总回看天数: {lookback_days}")

            # --- Data Preparation ---
            # Need enough data for MA (ma_period-1 extra), VMA (volume_avg_period-1 extra), and slope (1 extra)
            required_data_length = lookback_days + max(ma_period - 1, volume_avg_period - 1) + 1
            if len(self.data) < required_data_length:
                raise ValueError(f"数据量不足 ({len(self.data)}行) 无法进行分析 (需要至少{required_data_length}行)")

            # 统一查找价格列
            price_cols = ['Close', '价格', '收盘价', '当前价']
            price_col = next((col for col in price_cols if col in self.data.columns), None)
            if not price_col:
                raise ValueError("找不到价格列")

            # Calculate MA and VMA on sufficient data
            ma = self.data[price_col].rolling(window=ma_period).mean()
            vma = self.data['Volume'].rolling(window=volume_avg_period).mean()

            # Prepare the full dataset for analysis
            recent_data_full = self.data.tail(lookback_days + 1).copy()
            recent_data_full[f'MA{ma_period}'] = ma.tail(lookback_days + 1)
            recent_data_full['VMA'] = vma.tail(lookback_days + 1)

            # Drop NaNs introduced by rolling calculations
            recent_data_full.dropna(subset=[f'MA{ma_period}', 'VMA'], inplace=True)
            if len(recent_data_full) < check_last_n_days + 1:
                Logger.warning(f"去除MA/VMA的NaN后，数据不足{check_last_n_days + 1}天，无法检查最近{check_last_n_days}天")
                return False, {'signal_detected': False, 'details': {}, 'score': 0, 'type': 'none'}

            # Select the final window for checking
            recent_data = recent_data_full.tail(check_last_n_days + 1).copy()

            # Calculate necessary metrics
            recent_data[f'MA{ma_period}_Distance'] = (recent_data[price_col] - recent_data[f'MA{ma_period}']) / recent_data[f'MA{ma_period}'] * 100
            recent_data[f'MA{ma_period}_Slope'] = recent_data[f'MA{ma_period}'].pct_change() * 100

            result = {'signal_detected': False, 'details': {}, 'score': 0, 'type': 'none'}

            # --- Main Loop (Reverse) ---
            for i in range(len(recent_data) - 1, 0, -1):
                # --- Get Current and Previous Day Data ---
                curr_row = recent_data.iloc[i]
                prev_row = recent_data.iloc[i-1]

                curr_idx = curr_row.name
                curr_open = curr_row['Open']
                curr_high = curr_row['High']
                curr_low = curr_row['Low']
                curr_close = curr_row[price_col]
                curr_volume = curr_row['Volume']
                curr_ma = curr_row[f'MA{ma_period}']
                curr_distance = curr_row[f'MA{ma_period}_Distance']
                ma_slope = curr_row[f'MA{ma_period}_Slope']
                curr_vma = curr_row['VMA']

                prev_distance = prev_row[f'MA{ma_period}_Distance']

                days_ago = len(recent_data) - 1 - i

                # --- Pre-conditions Check ---
                if ma_slope < min_ma_slope: continue
                if prev_distance < min_prev_distance: continue

                # --- Effective Support Check ("Completed" state) ---
                # 1. Price Condition: Closing price within the non-symmetrical threshold
                price_condition_met = (allowed_negative_proximity < curr_distance < upper_proximity)

                if price_condition_met:
                    # 2. Candlestick Condition: Check for long lower shadow
                    total_range = curr_high - curr_low
                    lower_shadow = min(curr_open, curr_close) - curr_low
                    is_supportive_candle = False
                    if total_range > 1e-6: # Avoid division by zero for flat candles
                        lower_shadow_ratio = lower_shadow / total_range
                        is_supportive_candle = (lower_shadow_ratio >= min_lower_shadow_ratio)

                    # 3. Volume Condition: Check for volume increase
                    # Compare current volume to the average volume *ending the previous day*
                    prev_vma = prev_row['VMA']
                    has_volume_confirmation = False
                    if prev_vma > 1e-6: # Avoid division by zero
                        volume_ratio = curr_volume / prev_vma
                        has_volume_confirmation = (volume_ratio >= min_volume_increase_ratio)

                    # --- Combine Conditions & Calculate Score ---
                    # Base requirement: Price condition must be met
                    # Candle and Volume act as confirmation boosters
                    if price_condition_met:
                        base_score = 50
                        candle_bonus = 25 if is_supportive_candle else 0
                        volume_bonus = 25 if has_volume_confirmation else 0

                        # Score reflects confidence: Price(50) + Candle(25) + Volume(25) = 100 max
                        score = min(100, base_score + candle_bonus + volume_bonus)

                        # Only consider it a strong signal if confirmations are present
                        is_strong_signal = (score >= 75) # Requires at least one confirmation

                        if is_strong_signal:
                            result = {
                                'signal_detected': True,
                                'details': {
                                    'date': curr_idx.strftime('%Y-%m-%d'),
                                    'price': float(curr_close),
                                    'ma': float(curr_ma),
                                    'distance': float(curr_distance),
                                    'ma_slope': float(ma_slope),
                                    'days_ago': days_ago,
                                    'open': float(curr_open),
                                    'high': float(curr_high),
                                    'low': float(curr_low),
                                    'volume': float(curr_volume),
                                    'is_supportive_candle': is_supportive_candle,
                                    'lower_shadow_ratio': float(lower_shadow_ratio) if total_range > 1e-6 else 0,
                                    'has_volume_confirmation': has_volume_confirmation,
                                    'volume_ratio': float(volume_ratio) if prev_vma > 1e-6 else 0,
                                    'prev_distance': float(prev_distance),
                                    'ma_period': ma_period
                                },
                                'score': score,
                                'type': 'completed_effective'
                            }
                            Logger.info(f"\n检测到最近MA{ma_period}有效支撑（于 {days_ago} 天前）:")
                            Logger.info(f"- 日期: {result['details']['date']}, 得分: {score:.1f}")
                            Logger.info(f"- 价格: C={curr_close:.3f}, MA{ma_period}={curr_ma:.3f}, 距离={curr_distance:.3f}%")
                            Logger.info(f"- K线支撑: {is_supportive_candle} (下影比率: {result['details']['lower_shadow_ratio']:.2f})")
                            Logger.info(f"- 成交量确认: {has_volume_confirmation} (倍率: {result['details']['volume_ratio']:.2f})")
                            Logger.info(f"- MA{ma_period}斜率: {ma_slope:.3f}%")
                            return True, result

                # --- Approaching Check ---
                if 0 < curr_distance < approaching_proximity and curr_distance < prev_distance:
                    if not result['signal_detected']:
                        score = max(10, min(49, 10 + 10 * (1 if ma_slope > 0.1 else 0) + 10 * (1 if prev_distance > 1 else 0) + (approaching_proximity - curr_distance) * 5))
                        result = {
                            'signal_detected': True,
                            'details': {
                                'date': curr_idx.strftime('%Y-%m-%d'),
                                'price': float(curr_close),
                                'ma': float(curr_ma),
                                'distance': float(curr_distance),
                                'ma_slope': float(ma_slope),
                                'days_ago': days_ago,
                                'prev_distance': float(prev_distance),
                                'ma_period': ma_period
                            },
                            'score': score,
                            'type': 'approaching'
                        }

            # --- Post-Loop Handling ---
            if result['signal_detected']:
                if result['type'] == 'approaching':
                    Logger.info(f"\n检测到最近MA{ma_period}回踩（仅接近中于 {result['details']['days_ago']} 天前）:")
                    Logger.info(f"- 日期: {result['details']['date']}, 得分: {result['score']:.1f}")
                    Logger.info(f"- 价格: C={result['details']['price']:.3f}, MA{ma_period}={result['details']['ma']:.3f}, 距离={result['details']['distance']:.3f}%")
                return True, result
            else:
                Logger.info(f"最近 {check_last_n_days} 天内未检测到MA{ma_period}有效支撑或接近信号。")
                return False, result

        except ValueError as ve:
            Logger.error(f"检查MA{ma_period}有效支撑失败 (数据错误): {str(ve)}")
            return False, {'signal_detected': False, 'details': {'error': str(ve)}, 'score': 0, 'type': 'error'}
        except Exception as e:
            Logger.error(f"检查MA{ma_period}有效支撑失败 (未知错误): {str(e)}", exc_info=True)
            return False, {'signal_detected': False, 'details': {'error': str(e)}, 'score': 0, 'type': 'error'}

    def analyze_ma_pullback(self, stock_code, stock_name, params):
        """
        均线有效支撑回踩分析的统一接口

        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            params: 分析参数，包含：
                - ma_period: 均线周期，如10代表MA10，20代表MA20等，默认10
                - lookback_days: 总回看天数，默认30天
                - check_last_n_days: 检查最近几天，默认3天
                - upper_proximity: 完成回踩时，收盘价允许高于均线的最大百分比，默认1%
                - allowed_negative_proximity: 完成回踩时，收盘价允许低于均线的最大百分比，默认-0.3%
                - approaching_proximity: 接近回踩的宽松距离阈值，默认3%
                - min_prev_distance: 之前价格需要高于均线的最小距离，默认1%
                - min_ma_slope: 均线的最小上升斜率要求，默认0%
                - min_lower_shadow_ratio: K线长下影线的最小比例，默认0.4
                - min_volume_increase_ratio: 支撑日成交量相对于前N日均量的最小倍数，默认1.1
                - volume_avg_period: 计算成交量均值的周期，默认5天

        Returns:
            Dict: 分析结果
        """
        try:
            # 从 params 获取参数，提供默认值
            ma_period = params.get('ma_period', 10)
            lookback_days = params.get('lookback_days', 30)
            check_last_n_days = params.get('check_last_n_days', 3)
            upper_proximity = params.get('upper_proximity', 1.0)
            allowed_negative_proximity = params.get('allowed_negative_proximity', -0.3)
            approaching_proximity = params.get('approaching_proximity', 3.0)
            min_prev_distance = params.get('min_prev_distance', 1.0)
            min_ma_slope = params.get('min_ma_slope', 0.0)
            min_lower_shadow_ratio = params.get('min_lower_shadow_ratio', 0.4)
            min_volume_increase_ratio = params.get('min_volume_increase_ratio', 1.1)
            volume_avg_period = params.get('volume_avg_period', 5)

            meets_criteria, result = self.check_ma_pullback(
                ma_period=ma_period,
                lookback_days=lookback_days,
                check_last_n_days=check_last_n_days,
                upper_proximity=upper_proximity,
                allowed_negative_proximity=allowed_negative_proximity,
                approaching_proximity=approaching_proximity,
                min_prev_distance=min_prev_distance,
                min_ma_slope=min_ma_slope,
                min_lower_shadow_ratio=min_lower_shadow_ratio,
                min_volume_increase_ratio=min_volume_increase_ratio,
                volume_avg_period=volume_avg_period
            )

            final_meets_criteria = result.get('signal_detected', False)

            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': final_meets_criteria,
                'signal_type': result.get('type', 'none'),
                'total_score': result.get('score', 0),
                'details': result.get('details', {})
            }
        except Exception as e:
            Logger.error(f"MA{ma_period}有效支撑分析接口失败 for {stock_code}: {str(e)}", exc_info=True)
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': False,
                'signal_type': 'error',
                'total_score': 0,
                'details': {'error': f'Analysis failed: {str(e)}'}
            }

    def analyze_low_vol_breakout(self, stock_code, stock_name, params):
        """
        低位放量突破分析
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            params: 分析参数，主要用于传递数据源等基础参数
                
        Returns:
            Dict: 分析结果
        """
        try:
            # 策略参数直接写死在这里
            strategy_params = {
                # 波动率参数
                'volatility_period': 10,      # 波动率计算周期
                'volatility_history': 60,     # 波动率历史数据长度
                'volatility_quantile': 0.2,   # 波动率分位数阈值
                
                # 成交量参数
                'volume_threshold_multiplier': 1.5,  # 成交量阈值倍数
                'volume_short_ma': 5,         # 成交量短期均线周期
                'volume_long_ma': 20,         # 成交量长期均线周期
                
                # 布林带参数
                'bbands_period': 20,          # 布林带周期
                'bbands_stddev': 2,           # 布林带标准差倍数
                
                # 趋势参数
                'ma_long_period': 30,         # 长期均线周期
                
                # RSI参数
                'rsi_period': 14,             # RSI周期
                'rsi_threshold': 50           # RSI阈值
            }
            
            meets_criteria, scores = self.check_low_vol_breakout_signal(strategy_params)
            
            # 记录详细分析日志
            Logger.info(f"股票 {stock_code} {stock_name} 低位放量突破分析详细结果:")
            Logger.info(f"- 总分: {scores.get('total_score', 0):.2f} - 是否符合条件: {meets_criteria}")
            Logger.info(f"- 波动率得分: {scores.get('volatility_score', 0):.2f}")
            Logger.info(f"- 成交量得分: {scores.get('volume_score', 0):.2f}")
            Logger.info(f"- 价格位置得分: {scores.get('price_position_score', 0):.2f}")
            Logger.info(f"- 趋势得分: {scores.get('trend_score', 0):.2f}")
            Logger.info(f"- RSI得分: {scores.get('rsi_score', 0):.2f}")
            
            # 显示一些原始指标值
            if 'raw_metrics' in scores:
                Logger.info("原始指标值:")
                raw = scores['raw_metrics']
                Logger.info(f"- 波动率: {raw.get('volatility')} (分位数阈值: {raw.get('vol_quantile')})")
                Logger.info(f"- 成交量比率: {raw.get('volume_ratio')}")
                Logger.info(f"- 布林带位置: {raw.get('bb_position')}")
                Logger.info(f"- MA趋势: {raw.get('ma_trend')}")
                Logger.info(f"- RSI值: {raw.get('rsi')}")
            
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': meets_criteria,
                'total_score': scores.get('total_score', 0),
                'volatility_score': scores.get('volatility_score', 0),
                'volume_score': scores.get('volume_score', 0),
                'price_position_score': scores.get('price_position_score', 0),
                'trend_score': scores.get('trend_score', 0),
                'rsi_score': scores.get('rsi_score', 0),
                'raw_metrics': scores.get('raw_metrics', {}),
                'scores': scores
            }
        except Exception as e:
            Logger.error(f"低位放量突破分析失败: {str(e)}")
            return None

    def check_low_vol_breakout_signal(self, params):
        """
        检查低位放量突破信号
        """
        try:
            Logger.info("开始低位放量突破信号分析")
            
            # 1. 参数提取
            volatility_period = params.get('volatility_period', 10)
            volatility_history = params.get('volatility_history', 60)
            volatility_quantile = params.get('volatility_quantile', 0.2)
            volume_threshold = params.get('volume_threshold_multiplier', 1.5)
            volume_short_ma = params.get('volume_short_ma', 5)
            volume_long_ma = params.get('volume_long_ma', 20)
            bbands_period = params.get('bbands_period', 20)
            bbands_stddev = params.get('bbands_stddev', 2)
            ma_long_period = params.get('ma_long_period', 30)
            rsi_period = params.get('rsi_period', 14)
            rsi_threshold = params.get('rsi_threshold', 50)
            
            # 2. 数据检查
            if len(self.data) < max(volatility_history, ma_long_period):
                raise ValueError(f"数据量不足，需要至少 {max(volatility_history, ma_long_period)} 个数据点")
            
            # 3. 计算技术指标
            # 波动率
            returns = self.data['Close'].pct_change()
            volatility = returns.rolling(window=volatility_period).std()
            historical_volatility = volatility.rolling(window=volatility_history)
            
            # 计算波动率分位数时确保使用标量值
            valid_volatility = volatility[~pd.isna(volatility)].values  # 转换为numpy数组
            if len(valid_volatility) > 0:
                vol_quantile = np.quantile(valid_volatility, volatility_quantile)
            else:
                vol_quantile = np.nan
            
            # 成交量指标
            volume_sma = self.data['Volume'].rolling(window=volume_short_ma).mean()
            volume_lma = self.data['Volume'].rolling(window=volume_long_ma).mean()
            
            # 避免除以零的情况
            volume_ratio = pd.Series(np.nan, index=self.data.index)
            mask = (volume_lma > 0) & (~pd.isna(volume_sma)) & (~pd.isna(volume_lma))
            volume_ratio[mask] = volume_sma[mask] / volume_lma[mask]
            
            # 布林带
            typical_price = (self.data['High'] + self.data['Low'] + self.data['Close']) / 3
            bb_middle = typical_price.rolling(window=bbands_period).mean()
            bb_std = typical_price.rolling(window=bbands_period).std()
            bb_upper = bb_middle + bbands_stddev * bb_std
            bb_lower = bb_middle - bbands_stddev * bb_std
            
            # 长期均线
            ma_long = self.data['Close'].rolling(window=ma_long_period).mean()
            
            # RSI
            delta = self.data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
            
            # 避免除以零的情况
            rsi = pd.Series(np.nan, index=self.data.index)
            mask = (loss > 0) & (~pd.isna(gain)) & (~pd.isna(loss))
            rs = pd.Series(np.nan, index=self.data.index)
            rs[mask] = gain[mask] / loss[mask]
            rsi[mask] = 100 - (100 / (1 + rs[mask]))
            # 处理特殊情况：如果loss为0且gain>0，则RSI=100
            rsi[(loss == 0) & (gain > 0)] = 100
            # 处理特殊情况：如果gain为0，则RSI=0
            rsi[(gain == 0)] = 0
            
            # 4. 计算信号得分
            scores = {}
            
            # 波动率得分
            current_vol = volatility.iloc[-1]
            if pd.isna(vol_quantile) or pd.isna(current_vol):
                scores['volatility_score'] = 0
            else:
                scores['volatility_score'] = 100 if current_vol <= vol_quantile else max(0, 100 * (1 - current_vol/vol_quantile))
            
            # 成交量得分
            current_vol_ratio = volume_ratio.iloc[-1]
            if pd.isna(current_vol_ratio):
                scores['volume_score'] = 0
            else:
                scores['volume_score'] = min(100, max(0, (current_vol_ratio/volume_threshold) * 100))
            
            # 价格位置得分
            current_price = self.data['Close'].iloc[-1]
            current_bb_upper = bb_upper.iloc[-1]
            current_bb_lower = bb_lower.iloc[-1]
            
            if pd.isna(current_price) or pd.isna(current_bb_upper) or pd.isna(current_bb_lower) or (current_bb_upper - current_bb_lower) == 0:
                scores['price_position_score'] = 0
            else:
                current_bb_pos = (current_price - current_bb_lower) / (current_bb_upper - current_bb_lower)
                scores['price_position_score'] = max(0, 100 * (1 - current_bb_pos))
            
            # 趋势得分
            current_ma = ma_long.iloc[-1]
            past_ma = ma_long.iloc[-5] if len(ma_long) >= 5 else ma_long.iloc[0]
            
            if pd.isna(current_ma) or pd.isna(past_ma) or past_ma == 0:
                scores['trend_score'] = 0
            else:
                ma_trend = (current_ma / past_ma - 1) * 100
                scores['trend_score'] = min(100, max(0, 50 + ma_trend * 10))
            
            # RSI得分
            current_rsi = rsi.iloc[-1]
            if pd.isna(current_rsi):
                scores['rsi_score'] = 0
            else:
                scores['rsi_score'] = min(100, max(0, (current_rsi/rsi_threshold) * 100))
            
            # 记录原始指标值，为结果展示提供更多信息
            scores['raw_metrics'] = {
                'volatility': float(current_vol) if not pd.isna(current_vol) else None,
                'vol_quantile': float(vol_quantile) if not pd.isna(vol_quantile) else None,
                'volume_ratio': float(current_vol_ratio) if not pd.isna(current_vol_ratio) else None,
                'bb_position': float(current_bb_pos) if 'current_bb_pos' in locals() and not pd.isna(current_bb_pos) else None,
                'ma_trend': float(ma_trend) if 'ma_trend' in locals() and not pd.isna(ma_trend) else None,
                'rsi': float(current_rsi) if not pd.isna(current_rsi) else None
            }
            
            # 5. 计算总分
            weights = {
                'volatility': 0.25,
                'volume': 0.25,
                'price_position': 0.20,
                'trend': 0.20,
                'rsi': 0.10
            }
            
            total_score = (
                scores['volatility_score'] * weights['volatility'] +
                scores['volume_score'] * weights['volume'] +
                scores['price_position_score'] * weights['price_position'] +
                scores['trend_score'] * weights['trend'] +
                scores['rsi_score'] * weights['rsi']
            )
            
            scores['total_score'] = total_score
            scores['weights'] = weights  # 添加权重信息到结果中
            
            # 6. 判断条件
            meets_criteria = (
                total_score >= 70 and
                scores['volume_score'] >= 60 and
                scores['volatility_score'] >= 60
            )
            
            # 7. 记录分析结果
            Logger.info("低位放量突破分析结果:")
            Logger.info(f"- 总分: {total_score:.2f}")
            Logger.info(f"- 波动率得分: {scores['volatility_score']:.2f}")
            Logger.info(f"- 成交量得分: {scores['volume_score']:.2f}")
            Logger.info(f"- 价格位置得分: {scores['price_position_score']:.2f}")
            Logger.info(f"- 趋势得分: {scores['trend_score']:.2f}")
            Logger.info(f"- RSI得分: {scores['rsi_score']:.2f}")
            Logger.info(f"- 是否符合条件: {meets_criteria}")
            
            return meets_criteria, scores
            
        except Exception as e:
            Logger.error(f"检查低位放量突破信号失败: {str(e)}")
            raise

# python run.py --mode cli \    --input '/Users/<USER>/Desktop/stock/20250411/资金复盘_1744371803554.xlsx' \
#     -f "申万板块" "包含" "元件" \
#     --analysis above_cloud \
#     --days 30 --source sina --verbose --quantity 100 --min-score=60.0


#  python run.py --mode cli --input '/Users/<USER>/Desktop/stock/20250424/ 资金复盘_1745490122218.xlsx' -f "申万板块" "包含" "商用车|航海装备|航天装备|服装家纺|橡胶|化学原料|专用装备|轨交装备|医 疗美容|工程机械|元件|小金属|工程机械" --analysis low_vol_breakout