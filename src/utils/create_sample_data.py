import pandas as pd
import numpy as np
import random
import os

def create_sample_stock_data(output_path=None):
    """
    创建示例股票数据
    
    参数:
        output_path (str, optional): 输出文件路径，默认为当前目录下的sample_stock_data.xlsx
    """
    # 股票代码和名称
    stock_codes = ['605066', '300183', '603278', '600118', '000901', 
                  '600797', '603166', '603178', '603809', '601012']
    
    stock_names = ['天正电气', '东软载波', '大业股份', '中国卫星', '航天科技',
                  '浙大网新', '福达股份', '圣龙股份', '豪能股份', '隆基股份']
    
    # 生成随机数据
    data = {
        '股票代码': stock_codes,
        '股票名称': stock_names,
        '流通市值': [round(random.uniform(30, 350), 5) for _ in range(10)],
        '涨跌幅': [f"{round(random.uniform(1, 20), 5)}(%)" for _ in range(10)],
        '成交额（亿）': [round(random.uniform(5, 30), 5) for _ in range(10)],
        '当天最低价': [round(random.uniform(8, 30), 2) for _ in range(10)],
        '当日大单买入': [round(random.uniform(1000, 5000), 2) for _ in range(10)]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 确定输出路径
    if output_path is None:
        # 默认保存在utils目录下
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_path = os.path.join(current_dir, 'sample_stock_data.xlsx')
    
    # 保存为Excel文件
    df.to_excel(output_path, index=False)
    print(f"示例股票数据已生成: {output_path}")
    
    return df

if __name__ == "__main__":
    create_sample_stock_data() 