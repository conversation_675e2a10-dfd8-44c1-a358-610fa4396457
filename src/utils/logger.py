"""
日志工具模块
提供日志配置和自定义异常类
"""
import logging
import sys
from typing import Optional
from datetime import datetime
import os
from pathlib import Path

class Logger:
    """统一的日志管理类"""
    
    _logger = None

    @classmethod
    def setup_logging(cls, log_level='INFO', log_file=None):
        if cls._logger is not None:
            return cls._logger

        # 创建logger
        logger = logging.getLogger('StockAnalysis')
        logger.setLevel(getattr(logging, log_level.upper()))

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 如果指定了日志文件，添加文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        # 避免日志重复
        logger.propagate = False

        cls._logger = logger
        return logger

    @classmethod
    def get_logger(cls):
        if cls._logger is None:
            return cls.setup_logging()
        return cls._logger

    @staticmethod
    def format_number(number, decimal_places=4):
        """格式化数字，去除尾随零"""
        if isinstance(number, (int, float)):
            formatted = f"{number:.{decimal_places}f}"
            # 去除尾随零和不必要的小数点
            formatted = formatted.rstrip('0').rstrip('.')
            return formatted
        return str(number)

    @staticmethod
    def debug(msg: str):
        """输出调试日志"""
        Logger.get_logger().debug(msg)
    
    @staticmethod
    def info(msg: str):
        """输出信息日志"""
        Logger.get_logger().info(msg)
    
    @staticmethod
    def warning(msg: str):
        """输出警告日志"""
        Logger.get_logger().warning(msg)
    
    @staticmethod
    def error(msg: str, exc_info: bool = False):
        """输出错误日志"""
        if exc_info:
            # 如果需要异常信息，使用logging模块的方式
            Logger.get_logger().error(msg, exc_info=exc_info)
        else:
            Logger.get_logger().error(msg)
    
    @staticmethod
    def critical(msg: str):
        """输出严重错误日志"""
        Logger.get_logger().critical(msg)
    
    @staticmethod
    def section(title: str, char: str = '=', length: int = 50):
        """输出分节日志"""
        line = char * length
        Logger.get_logger().info(f"\n{line}")
        Logger.get_logger().info(title)
        Logger.get_logger().info(f"{line}\n")
    
    @staticmethod
    def progress(current: int, total: int, prefix: str = '', suffix: str = ''):
        """输出进度日志"""
        msg = f"[进度 {current}/{total}]"
        if prefix:
            msg = f"{prefix} {msg}"
        if suffix:
            msg = f"{msg} {suffix}"
        Logger.get_logger().info(msg)

def setup_logger(log_file='logs/app.log', level=logging.INFO):
    """
    配置日志记录器
    
    参数:
    log_file: 日志文件路径
    level: 日志级别
    """
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)  # 同时输出到控制台
        ]
    )

class DataSourceError(Exception):
    """数据源错误的基类"""
    pass

class DataNotFoundError(DataSourceError):
    """当找不到特定股票/日期的数据时抛出"""
    pass

class InvalidStockCodeError(DataSourceError):
    """当股票代码格式无效时抛出"""
    pass

class APIRequestError(DataSourceError):
    """当API请求失败时抛出"""
    pass 