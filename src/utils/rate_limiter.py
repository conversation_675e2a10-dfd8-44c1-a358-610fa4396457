import threading
import time
from functools import wraps
from src.utils.logger import Logger

class RateLimiter:
    """请求频率限制器"""
    def __init__(self, max_requests=3, time_window=1):
        self.max_requests = max_requests  # 时间窗口内最大请求数
        self.time_window = time_window    # 时间窗口（秒）
        self.requests = []
        self.lock = threading.Lock()
        self.min_interval = max(1.0, time_window / max_requests)  # 强制最小间隔，至少1秒
        self.last_request_time = 0
        Logger.info(f"频率限制初始化: 每{self.time_window}秒最多{self.max_requests}个请求")
        Logger.info(f"最小请求间隔: {self.min_interval:.2f}秒")

    def acquire(self):
        """获取请求许可"""
        with self.lock:
            now = time.time()
            
            # 强制最小间隔
            if self.last_request_time > 0:
                elapsed = now - self.last_request_time
                if elapsed < self.min_interval:
                    wait_time = self.min_interval - elapsed
                    Logger.debug(f"强制最小间隔，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)
                    now = time.time()
            
            # 清理过期的请求记录
            self.requests = [req for req in self.requests if now - req < self.time_window]
            current_requests = len(self.requests)
            
            Logger.debug(f"当前时间窗口内请求数: {current_requests}/{self.max_requests}")
            
            if current_requests >= self.max_requests:
                # 计算需要等待的时间
                wait_time = self.requests[0] + self.time_window - now
                if wait_time > 0:
                    Logger.debug(f"达到限制，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)
                # 重新清理过期请求记录
                now = time.time()
                self.requests = [req for req in self.requests if now - req < self.time_window]
                Logger.debug(f"等待后的请求数: {len(self.requests)}/{self.max_requests}")
            
            self.requests.append(now)
            self.last_request_time = now
            if len(self.requests) > 1:
                interval = now - self.requests[-2]
                Logger.debug(f"距离上次请求间隔: {interval:.2f}秒")

def rate_limit(max_requests=1, time_window=3):
    """请求频率限制装饰器"""
    limiter = RateLimiter(max_requests, time_window)
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                limiter.acquire()
                return func(*args, **kwargs)
            except Exception as e:
                # 即使发生错误也要遵循频率限制
                Logger.error(str(e))
                limiter.acquire()  # 错误后也要等待
                raise
        return wrapper
    return decorator 