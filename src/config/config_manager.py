"""
项目配置管理模块
"""
import os
import json
from typing import Dict, Any

class ConfigManager:
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """初始化配置管理器"""
        # 获取项目根目录
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
        
        # 配置文件路径（在项目根目录下的config文件夹中）
        self.config_dir = os.path.join(self.project_root, 'config')
        self.config_file = os.path.join(self.config_dir, 'settings.json')
        
        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """从文件加载配置"""
        # 默认配置
        default_config = {
            'ichimoku': {
                'conversion': 9,
                'base': 26,
                'span_a': 26,
                'span_b': 52,
                'lagging': 26
            },
            'indicators': {
                'show_resistance': True,
                'show_ichimoku': False,
                'show_cost_line': False,
                'ma_groups': {
                    '短期': True,
                    '中期': False,
                    '长期': False,
                    '最长': False
                }
            }
            # 其他默认配置项可以在这里添加
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    stored_config = json.load(f)
                    # 深度更新配置，保留默认值
                    self._config = self._deep_update(default_config, stored_config)
            else:
                self._config = default_config
                # 保存默认配置
                self.save_config()
        except Exception as e:
            print(f"加载配置文件时出错: {e}")
            self._config = default_config
    
    def _deep_update(self, d1: Dict[str, Any], d2: Dict[str, Any]) -> Dict[str, Any]:
        """深度更新字典，保留默认值"""
        result = d1.copy()
        for k, v in d2.items():
            if k in result and isinstance(result[k], dict) and isinstance(v, dict):
                result[k] = self._deep_update(result[k], v)
            else:
                result[k] = v
        return result
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件时出错: {e}")
    
    def get_config(self, section: str = None) -> Dict[str, Any]:
        """获取配置
        
        Args:
            section: 配置节名称，如果为None则返回整个配置
        
        Returns:
            配置字典
        """
        if section is None:
            return self._config
        return self._config.get(section, {})
    
    def update_config(self, section: str, values: Dict[str, Any]):
        """更新配置的特定部分
        
        Args:
            section: 配置节名称
            values: 要更新的值
        """
        if section not in self._config:
            self._config[section] = {}
        self._config[section].update(values)
        self.save_config()

# 创建全局配置管理器实例
config_manager = ConfigManager() 