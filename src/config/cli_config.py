"""
命令行参数配置
"""
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class CliConfig:
    """CLI模式配置"""
    input_path: Optional[str] = None # 改为 Optional，因为可以从 category_input 获取
    output_path: Optional[str] = None
    mode: str = 'cli'
    category_input: Optional[str] = None # 新增：用于从分类加载股票
    limit_rows: Optional[int] = None # 新增：限制读取的数据行数
    calc_daily_change_from_csv_path: Optional[str] = None # 新增：用于从CSV文件计算股票当日涨幅
    
    # 数据源选项
    is_stock: bool = False
    period: str = '3mo'
    interval: str = '1d'
    data_source: str = 'sina'
    realtime: bool = False  # 是否获取实时数据
    
    # 筛选条件
    filters: List[tuple] = None
    
    # 技术分析参数
    analysis_type: Optional[str] = None
    days: int = 5
    ma_period: int = 10
    trend: str = 'up'
    min_score: float = 60.0
    verbose: bool = False
    
    # 左侧打分参数
    left_score: bool = False
    
    # 交易模拟参数
    quantity: int = 100
    signal_dates: List[str] = None
    
    # 频率控制
    max_requests: int = 3
    batch_size: int = 3
    batch_delay: float = 1.0

    # 股票管理
    add_stock: Optional[str] = None
    remove_stock: Optional[str] = None
    list_stocks: bool = False
    category: str = '观察'

def get_cli_parser_config():
    """获取命令行参数配置"""
    return {
        'main_args': {
            'description': "股票数据分析工具",
            'arguments': [
                {
                    'name': ['--mode'], # 移除了 '-m'
                    'choices': ['gui', 'cli', 'excel'],
                    'default': 'cli',
                    'help': "运行模式：gui(K线图)、cli(命令行)或excel(Excel查看器)"
                }
            ]
        },
        'groups': {
            'cli_args': {
                'title': 'CLI模式参数',
                'arguments': [
                    {
                        'name': ['--input', '-i'],
                        'dest': 'input_path',
                        'required': False, # 不再绝对 required，因为可以有 category_input
                        'help': "输入的Excel文件路径或股票代码（多个用逗号分隔）。与 --category-input 互斥使用。"
                    },
                    {
                        'name': ['--category-input', '-ci'],
                        'dest': 'category_input',
                        'help': "指定一个分类名称，加载该分类下的所有股票进行分析。与 --input 互斥使用。"
                    },
                    {
                        'name': ['--output', '-o'],
                        'dest': 'output_path',
                        'help': "输出的Excel文件路径"
                    },
                    {
                        'name': ['--limit-rows', '-lr'],
                        'dest': 'limit_rows',
                        'type': int,
                        'help': "限制从文件或数据源读取的数据行数/条目数"
                    },
                    {
                        'name': ['--calc-daily-change-from-csv'],
                        'dest': 'calc_daily_change_from_csv_path',
                        'help': "指定一个CSV文件路径。程序将读取此CSV（需包含'股票代码'和'命中日期'列），计算每只股票在其中'命中日期'当天的开盘价、收盘价及涨幅，并将结果输出到新的CSV文件。"
                    }
                ]
            },
            'filter_group': {
                'title': '筛选条件',
                'arguments': [
                    {
                        'name': ['-f', '--filter'],
                        'dest': 'filters',
                        'nargs': 3,
                        'action': 'append',
                        'metavar': ('列名', '操作符', '值'),
                        'help': "筛选条件，格式：'列名 操作符 值'。操作符可以是：大于、小于、等于、包含"
                    }
                ]
            },
            'source_group': {
                'title': '数据源选项',
                'arguments': [
                    {
                        'name': ['--stock'],
                        'dest': 'is_stock',
                        'action': 'store_true',
                        'help': "指定input为股票代码而不是Excel文件"
                    },
                    {
                        'name': ['--period'],
                        'dest': 'period',
                        'default': '3mo',
                        'help': "数据周期，如：1d,5d,1mo,3mo,6mo,1y,2y,5y,10y,max"
                    },
                    {
                        'name': ['--interval'],
                        'dest': 'interval',
                        'default': '1d',
                        'help': "数据间隔，如：1m,2m,5m,15m,30m,60m,90m,1h,1d,5d,1wk,1mo,3mo"
                    },
                    {
                        'name': ['--source'],
                        'dest': 'data_source',
                        'default': 'sina',
                        'help': "数据源：sina或yahoo"
                    },
                    {
                        'name': ['--realtime'],
                        'dest': 'realtime',
                        'action': 'store_true',
                        'help': "是否获取实时数据（仅对sina数据源有效）"
                    }
                ]
            },
            'analysis_group': {
                'title': '技术分析',
                'arguments': [
                    {
                        'name': ['--analysis'],
                        'dest': 'analysis_type',
                        'choices': ['above_ma', 'ma_trend', 'score', 'above_cloud', 'above_cloud_early', 'ma_pullback', 'low_vol_breakout'],
                        'help': "技术分析类型：above_ma(价格在均线之上)，ma_trend(均线趋势)，score(综合打分)，above_cloud(价格在云层之上)，above_cloud_early(价格在云层之上的早期信号)，ma_pullback(均线回踩)，low_vol_breakout(低位放量突破)"
                    },
                    {
                        'name': ['--days'],
                        'dest': 'days',
                        'type': int,
                        'default': 5,
                        'help': "分析的天数"
                    },
                    {
                        'name': ['--ma-period'],
                        'dest': 'ma_period',
                        'type': int,
                        'default': 10,
                        'help': "均线周期，如10代表MA10，20代表MA20等"
                    },
                    {
                        'name': ['--trend'],
                        'dest': 'trend',
                        'choices': ['up', 'down'],
                        'default': 'up',
                        'help': "趋势方向：up(上升)或down(下降)"
                    },
                    {
                        'name': ['--min-score'],
                        'dest': 'min_score',
                        'type': float,
                        'default': 60.0,
                        'help': "最低得分要求（0-100），默认60分"
                    },
                    {
                        'name': ['--verbose'],
                        'dest': 'verbose',
                        'action': 'store_true',
                        'help': "是否打印详细的分析日志"
                    }
                ]
            },
            'left_score_group': {
                'title': '左侧打分',
                'arguments': [
                    {
                        'name': ['--left-score'],
                        'dest': 'left_score',
                        'action': 'store_true',
                        'help': "是否进行左侧打分分析"
                    }
                ]
            },
            'trading_group': {
                'title': '交易模拟',
                'arguments': [
                    {
                        'name': ['--quantity'],
                        'dest': 'quantity',
                        'type': int,
                        'default': 100,
                        'help': "每次买入的固定数量，默认100股"
                    },
                    {
                        'name': ['--signal-dates'],
                        'dest': 'signal_dates',
                        'nargs': '+',
                        'help': "买入信号日期列表，格式：YYYYMMDD，多个日期用空格分隔"
                    }
                ]
            },
            'rate_group': {
                'title': '频率控制',
                'arguments': [
                    {
                        'name': ['--max-requests'],
                        'dest': 'max_requests',
                        'type': int,
                        'default': 3,
                        'help': "每秒最大请求数"
                    },
                    {
                        'name': ['--batch-size'],
                        'dest': 'batch_size',
                        'type': int,
                        'default': 3,
                        'help': "批处理大小"
                    },
                    {
                        'name': ['--batch-delay'],
                        'dest': 'batch_delay',
                        'type': float,
                        'default': 1.0,
                        'help': "批处理间隔（秒）"
                    }
                ]
            },
           'stock_management_group': {
               'title': '股票管理',
               'arguments': [
                   {
                       'name': ['-a', '--add-stock'],
                       'dest': 'add_stock',
                       'metavar': '股票代码',
                       'help': "添加股票到管理列表（默认添加到'观察'分类）"
                   },
                   {
                       'name': ['-r', '--remove-stock'],
                       'dest': 'remove_stock',
                       'metavar': '股票代码',
                       'help': "从管理列表中移除股票（从所有分类中移除）"
                   },
                   {
                       'name': ['-l', '--list-stocks'],
                       'dest': 'list_stocks',
                       'action': 'store_true',
                       'help': "列出管理列表中的所有股票"
                   },
                   {
                       'name': ['-c', '--category'],
                       'dest': 'category',
                       'default': '观察',
                       # 'choices': ['观察', '候选'], # 移除 choices 限制
                       'help': "指定股票分类（用于添加、移除、列出），如果分类不存在则会创建"
                   }
               ]
           }
        }
    }