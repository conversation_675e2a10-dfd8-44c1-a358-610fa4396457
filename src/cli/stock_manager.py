import json
import os
from src.utils.logger import Logger

DATA_DIR = 'data'
STOCKS_FILE = os.path.join(DATA_DIR, 'managed_stocks.json')

class StockManager:
    def __init__(self):
        """初始化股票管理器，加载现有股票列表"""
        self.stocks = self._load_stocks()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(DATA_DIR):
            try:
                os.makedirs(DATA_DIR)
                Logger.info(f"创建数据目录: {DATA_DIR}")
            except OSError as e:
                Logger.error(f"无法创建数据目录 {DATA_DIR}: {e}")
                # 如果无法创建目录，后续保存会失败，这里可以选择抛出异常或继续（可能导致错误）

    def _load_stocks(self):
        """从JSON文件加载股票数据"""
        self._ensure_data_dir()
        default_stocks = {} # 改为空字典，不再预设分类
        if os.path.exists(STOCKS_FILE):
            try:
                with open(STOCKS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 数据验证
                    if not isinstance(data, dict):
                        Logger.warning(f"{STOCKS_FILE} 格式错误，不是一个字典。将使用空列表。")
                        return default_stocks
                    # 确保所有值都是列表
                    valid_data = {}
                    for k, v in data.items():
                        if isinstance(v, list):
                            # 过滤掉列表中的非字符串元素（可选，增加健壮性）
                            valid_data[k] = [str(item) for item in v if isinstance(item, (str, int, float))]
                        else:
                            Logger.warning(f"分类 '{k}' 的值不是列表，已忽略。")
                    return valid_data
            except json.JSONDecodeError:
                Logger.error(f"无法解析 {STOCKS_FILE}，将使用空列表。")
                return default_stocks
            except Exception as e:
                Logger.error(f"加载股票列表时出错: {e}")
                return default_stocks
        else:
            Logger.info(f"股票列表文件 {STOCKS_FILE} 不存在，将创建新列表。")
            return default_stocks # 返回空字典

    def _save_stocks(self):
        """将股票数据保存到JSON文件"""
        self._ensure_data_dir()
        try:
            with open(STOCKS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.stocks, f, ensure_ascii=False, indent=4)
            Logger.debug(f"股票列表已保存到 {STOCKS_FILE}")
            return True
        except Exception as e:
            Logger.error(f"保存股票列表时出错: {e}")
            return False

    def add_stock(self, code, category='观察'):
        """
        添加股票到指定分类。如果分类不存在，则创建。

        Args:
            code (str): 股票代码。
            category (str): 股票分类，默认为 '观察'。

        Returns:
            bool: 如果成功添加或已存在，返回 True；如果保存失败，返回 False。
        """
        if not category: # 如果传入空分类名，则使用默认值
             category = '观察'
             Logger.warning("未指定分类名，将使用默认分类 '观察'")

        stock_list = self.stocks.setdefault(category, []) # 确保分类存在，如果不存在则创建
        Logger.info(f"准备将股票 {code} 添加到分类: '{category}'")

        # 检查是否已在其他分类中
        moved = False
        for cat, codes in self.stocks.items():
            if cat != category and code in codes:
                Logger.warning(f"股票 {code} 已存在于 '{cat}' 分类。将从 '{cat}' 移动到 '{category}'。")
                codes.remove(code)
                moved = True

        # 添加到目标分类
        if code not in stock_list:
            stock_list.append(code)
            if moved:
                Logger.info(f"股票 {code} 已移动到 '{category}' 分类。") # 修正缩进
            else:
                Logger.info(f"股票 {code} 已添加到 '{category}' 分类。") # 修正缩进
            return self._save_stocks()
        else:
            Logger.info(f"股票 {code} 已存在于 '{category}' 分类。")
            return True # 即使未修改，也认为操作成功

    def remove_stock(self, code, category=None):
        """
        从指定分类或所有分类中移除股票。

        Args:
            code (str): 要移除的股票代码。
            category (str, optional): 指定要从中移除的分类。如果为 None，则从所有分类中移除。

        Returns:
            bool: 如果成功移除并保存，返回 True；如果未找到或保存失败，返回 False。
        """
        removed = False
        if category:
            if category in self.stocks and code in self.stocks[category]:
                self.stocks[category].remove(code)
                Logger.info(f"股票 {code} 已从 '{category}' 分类移除。")
                removed = True
                # 如果分类变空，可以选择删除该分类
                # if not self.stocks[category]:
                #     del self.stocks[category]
                #     Logger.info(f"分类 '{category}' 已空，已被移除。")
            else:
                Logger.warning(f"在分类 '{category}' 中未找到股票 {code}。")
        else:
            categories_removed_from = []
            for cat, codes in self.stocks.items():
                if code in codes:
                    codes.remove(code)
                    categories_removed_from.append(cat)
                    removed = True
                    # 如果分类变空，可以选择删除该分类
                    # if not codes:
                    #     # 需要注意迭代时修改字典的问题，最好先记录再删除
                    #     pass
            if categories_removed_from:
                 Logger.info(f"股票 {code} 已从以下分类移除: {', '.join(categories_removed_from)}。")
            elif not removed: # 只有在指定了category且未找到时才打印上面的warning，否则打印这个
                 Logger.warning(f"在任何分类中都未找到股票 {code}。")

        if removed:
            return self._save_stocks()
        return False # 未找到股票，无需保存

    def list_stocks(self, category=None):
        """列出指定分类或所有分类的股票"""
        Logger.section("管理的股票列表")
        listed_something = False
        if category:
            if category in self.stocks:
                Logger.info(f"分类 '{category}':")
                if self.stocks[category]:
                    for code in sorted(self.stocks[category]): # 按代码排序
                        Logger.info(f"- {code}")
                    listed_something = True
                else:
                    Logger.info("  (空)")
                    listed_something = True # 列出了空分类也算列出
            else:
                Logger.warning(f"未找到分类 '{category}'。")
        else:
            if not self.stocks:
                 Logger.info("当前没有管理任何股票。")
                 listed_something = True
            else:
                for cat in sorted(self.stocks.keys()): # 按分类名称排序
                    codes = self.stocks[cat]
                    Logger.info(f"分类 '{cat}':")
                    if codes:
                        for code in sorted(codes): # 按代码排序
                            Logger.info(f"- {code}")
                        listed_something = True
                    else:
                        Logger.info("  (空)")
                        listed_something = True # 即使为空也算列出

        if not listed_something and not category: # 再次检查，确保在没有分类时有输出
             Logger.info("当前没有管理任何股票。")

        Logger.section("列表结束")

    def get_stocks_by_category(self, category):
        """
        获取指定分类下的所有股票代码。

        Args:
            category (str): 分类名称。

        Returns:
            list: 包含股票代码的列表，如果分类不存在则返回空列表。
        """
        if category in self.stocks:
            return self.stocks[category][:] # 返回列表的副本
        else:
            Logger.warning(f"未找到分类 '{category}'。")
            return []