import argparse
import os
import pandas as pd
from collections import defaultdict, deque
import sys

# ANSI 颜色代码
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

    @staticmethod
    def colorize(text, color):
        """给文本添加颜色"""
        return f"{color}{text}{Colors.END}"

    @staticmethod
    def red(text):
        return Colors.colorize(text, Colors.RED)

    @staticmethod
    def green(text):
        return Colors.colorize(text, Colors.GREEN)

    @staticmethod
    def yellow(text):
        return Colors.colorize(text, Colors.YELLOW)

    @staticmethod
    def blue(text):
        return Colors.colorize(text, Colors.BLUE)

    @staticmethod
    def bold(text):
        return Colors.colorize(text, Colors.BOLD)

    @staticmethod
    def cyan(text):
        return Colors.colorize(text, Colors.CYAN)

# 趋势符号
class Symbols:
    UP = "🔺"
    DOWN = "🔻"
    NEW = "🆕"
    FIRE = "🔥"
    STAR = "⭐"
    ARROW_UP = "↗️"
    ARROW_DOWN = "↘️"
    EQUAL = "➡️"
    CHART = "📈"
    WARNING = "⚠️"

def print_section_header(title, symbol="=", color=Colors.CYAN):
    """打印美化的章节标题"""
    line = symbol * 60
    print(f"\n{color}{line}{Colors.END}")
    print(f"{color}{Colors.BOLD}{title.center(60)}{Colors.END}")
    print(f"{color}{line}{Colors.END}\n")

def print_stock_info(stock_code, stock_name, additional_info="", symbol="", color=Colors.WHITE):
    """打印美化的股票信息"""
    if symbol:
        symbol_part = f"{symbol} "
    else:
        symbol_part = ""

    if stock_name and stock_name != "未知名称":
        stock_display = f"{stock_code} ({stock_name})"
    else:
        stock_display = stock_code

    if additional_info:
        print(f"  {symbol_part}{color}{stock_display}{Colors.END} - {additional_info}")
    else:
        print(f"  {symbol_part}{color}{stock_display}{Colors.END}")

def format_rank_change(old_rank, new_rank):
    """格式化排名变化显示"""
    if old_rank is None:
        return f"{Symbols.NEW} 新上榜 (排名{new_rank})"

    change = old_rank - new_rank  # 排名数字越小越好，所以这样计算
    if change > 0:
        return f"{Symbols.UP} {Colors.green(f'↑{change}')} (从{old_rank}→{new_rank})"
    elif change < 0:
        return f"{Symbols.DOWN} {Colors.red(f'↓{abs(change)}')} (从{old_rank}→{new_rank})"
    else:
        return f"{Symbols.EQUAL} {Colors.yellow('持平')} (排名{new_rank})"

def print_summary_stats(title, count, total=None, color=Colors.BLUE):
    """打印汇总统计信息"""
    if total:
        percentage = (count / total) * 100
        print(f"{color}{title}: {count} 只 ({percentage:.1f}%){Colors.END}")
    else:
        print(f"{color}{title}: {count} 只{Colors.END}")

def calculate_price_change_for_period(stock_code, start_date, end_date, all_daily_data):
    """
    计算股票在指定期间的涨幅信息，同时获取板块和概念信息

    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        all_daily_data: 所有日期的数据字典

    Returns:
        dict: 包含涨幅信息和板块概念信息的字典
    """
    try:
        # 获取开始日期的涨幅
        start_df = all_daily_data.get(start_date)
        end_df = all_daily_data.get(end_date)

        if start_df is None or end_df is None:
            return {
                'total_change': None,
                'daily_changes': [],
                'avg_daily_change': None,
                'sector': None,
                'concepts': None,
                'market_cap': None,
                'error': '数据不完整'
            }

        # 确保股票代码为字符串类型
        start_df = start_df.copy()
        end_df = end_df.copy()
        start_df['股票代码'] = start_df['股票代码'].astype(str)
        end_df['股票代码'] = end_df['股票代码'].astype(str)

        # 获取股票的板块和概念信息（从最新日期获取）
        latest_stock_info = end_df[end_df['股票代码'] == stock_code]
        sector = None
        concepts = None
        market_cap = None

        if not latest_stock_info.empty:
            sector = latest_stock_info['申万板块'].iloc[0] if '申万板块' in latest_stock_info.columns else None
            concepts = latest_stock_info['开盘啦概念'].iloc[0] if '开盘啦概念' in latest_stock_info.columns else None
            market_cap = latest_stock_info['流通市值'].iloc[0] if '流通市值' in latest_stock_info.columns else None

        # 获取期间内所有日期的涨幅数据
        daily_changes = []
        dates_list = sorted(all_daily_data.keys())
        start_idx = dates_list.index(start_date)
        end_idx = dates_list.index(end_date)

        total_change = 0.0
        valid_days = 0

        for i in range(start_idx, end_idx + 1):
            date = dates_list[i]
            df = all_daily_data[date]
            df = df.copy()
            df['股票代码'] = df['股票代码'].astype(str)

            stock_row = df[df['股票代码'] == stock_code]
            if not stock_row.empty and '当日涨幅(%)' in stock_row.columns:
                daily_change = stock_row['当日涨幅(%)'].iloc[0]
                if pd.notna(daily_change):
                    daily_changes.append({
                        'date': date,
                        'change': float(daily_change)
                    })
                    total_change += float(daily_change)
                    valid_days += 1

        avg_daily_change = total_change / valid_days if valid_days > 0 else 0

        return {
            'total_change': round(total_change, 2),
            'daily_changes': daily_changes,
            'avg_daily_change': round(avg_daily_change, 2),
            'valid_days': valid_days,
            'sector': sector,
            'concepts': concepts,
            'market_cap': market_cap,
            'error': None
        }

    except Exception as e:
        return {
            'total_change': None,
            'daily_changes': [],
            'avg_daily_change': None,
            'sector': None,
            'concepts': None,
            'market_cap': None,
            'error': str(e)
        }

def format_price_change_display(price_change_info):
    """
    格式化涨幅信息显示

    Args:
        price_change_info: 涨幅信息字典

    Returns:
        str: 格式化后的涨幅显示字符串
    """
    if price_change_info.get('error'):
        return f"{Colors.red('涨幅计算失败')}: {price_change_info['error']}"

    total_change = price_change_info.get('total_change')
    avg_change = price_change_info.get('avg_daily_change')
    valid_days = price_change_info.get('valid_days', 0)

    if total_change is None:
        return Colors.yellow('无涨幅数据')

    # 选择颜色和趋势符号
    if total_change > 0:
        color_func = Colors.green
        trend_symbol = Symbols.UP
    elif total_change < 0:
        color_func = Colors.red
        trend_symbol = Symbols.DOWN
    else:
        color_func = Colors.yellow
        trend_symbol = Symbols.EQUAL

    # 格式化显示
    total_str = color_func(f"{total_change:+.2f}%")
    avg_str = color_func(f"{avg_change:+.2f}%")

    return f"{trend_symbol} 总涨幅: {total_str}, 平均日涨幅: {avg_str} ({valid_days}天)"

def find_csv_files(input_dir):
    """
    Finds all 'above_cloud_daily_change.csv' files in date-named subdirectories.

    Args:
        input_dir (str): The root directory to search in.

    Returns:
        list: A sorted list of tuples (date_str, file_path).
    """
    csv_files = []
    if not os.path.isdir(input_dir):
        print(f"错误：输入目录 '{input_dir}' 不存在或不是一个目录。")
        return csv_files

    for date_folder in sorted(os.listdir(input_dir)):
        date_folder_path = os.path.join(input_dir, date_folder)
        if os.path.isdir(date_folder_path):
            # 假设文件夹名称是 YYYYMMDD 格式的日期
            # 你可能需要添加更严格的日期格式校验
            target_csv_name = "above_cloud_daily_change.csv"
            csv_file_path = os.path.join(date_folder_path, target_csv_name)
            if os.path.isfile(csv_file_path):
                csv_files.append((date_folder, csv_file_path))
            else:
                print(f"警告：在目录 '{date_folder_path}' 中未找到文件 '{target_csv_name}'。")
    
    # 按日期排序 (文件夹名称即日期)
    csv_files.sort(key=lambda x: x[0])
    return csv_files

def load_stock_data(file_path):
    """
    Loads stock data from a CSV file.
    Assumes '股票代码' is the first column and can be used as an index.
    The order in the CSV is considered as the rank.
    """
    try:
        # 使用 lineterminator 参数处理可能的行尾问题
        # 将'股票代码'列显式指定为字符串类型，以保留前导零
        df = pd.read_csv(file_path, lineterminator='\n', dtype={'股票代码': str})
        # 添加排名列，基于原始文件中的行号（1-based）
        df['排名'] = range(1, len(df) + 1)
        return df
    except Exception as e:
        print(f"读取文件 '{file_path}' 时出错: {e}")
        return pd.DataFrame()

def analyze_stock_trends(input_dir):
    """
    Analyzes stock trends based on daily CSV files.
    """
    csv_files_info = find_csv_files(input_dir)
    if not csv_files_info:
        print(f"在目录 '{input_dir}' 中没有找到有效的CSV文件进行分析。")
        return

    all_daily_data = {} # key: date_str, value: DataFrame
    all_daily_stocks = {} # key: date_str, value: set of stock codes

    print_section_header(f"{Symbols.CHART} 数据加载中", "=", Colors.BLUE)

    for i, (date_str, file_path) in enumerate(csv_files_info, 1):
        print(f"{Colors.cyan(f'[{i}/{len(csv_files_info)}]')} 处理文件: {Colors.yellow(date_str)}")
        df = load_stock_data(file_path)
        if not df.empty:
            all_daily_data[date_str] = df
            all_daily_stocks[date_str] = set(df['股票代码'].astype(str))
            print(f"  {Colors.green('✓')} 成功加载 {Colors.bold(str(len(df)))} 只股票")
        else:
            print(f"  {Colors.red('✗')} 加载失败")
    
    if not all_daily_data:
        print("未能加载任何股票数据。")
        return

    dates = sorted(all_daily_data.keys())

    # 分析1: 新上榜和今日未出现
    print_section_header(f"{Symbols.NEW} 新上榜与今日未出现分析", "=", Colors.MAGENTA)

    total_new_stocks = 0
    total_disappeared_stocks = 0

    for i in range(len(dates)):
        current_date = dates[i]
        current_stocks = all_daily_stocks[current_date]

        if i == 0:
            print(f"{Colors.bold(f'📅 日期: {current_date}')}")
            print_summary_stats("初始上榜股票", len(current_stocks), color=Colors.BLUE)

            # 显示前10只股票
            stock_list = list(current_stocks)[:10]
            for j, stock_code in enumerate(stock_list, 1):
                stock_name = all_daily_data[current_date][all_daily_data[current_date]['股票代码'].astype(str) == stock_code]['股票名称'].iloc[0] if '股票名称' in all_daily_data[current_date].columns else "未知名称"
                print_stock_info(stock_code, stock_name, f"排名 {j}", Symbols.STAR, Colors.CYAN)

            if len(current_stocks) > 10:
                print(f"  {Colors.yellow(f'... 还有 {len(current_stocks) - 10} 只股票')}")
            continue

        prev_date = dates[i-1]
        prev_stocks = all_daily_stocks[prev_date]

        newly_listed = current_stocks - prev_stocks
        disappeared = prev_stocks - current_stocks

        total_new_stocks += len(newly_listed)
        total_disappeared_stocks += len(disappeared)

        print(f"\n{Colors.bold(f'📅 日期: {current_date}')} {Colors.cyan(f'(与 {prev_date} 比较)')}")

        # 新上榜股票
        if newly_listed:
            print_summary_stats("新上榜", len(newly_listed), len(current_stocks), Colors.GREEN)
            for stock_code in list(newly_listed)[:10]:
                stock_name = all_daily_data[current_date][all_daily_data[current_date]['股票代码'].astype(str) == stock_code]['股票名称'].iloc[0] if '股票名称' in all_daily_data[current_date].columns else "未知名称"
                rank = all_daily_data[current_date][all_daily_data[current_date]['股票代码'].astype(str) == stock_code]['排名'].iloc[0]
                print_stock_info(stock_code, stock_name, f"排名 {rank}", Symbols.NEW, Colors.GREEN)

            if len(newly_listed) > 10:
                print(f"    {Colors.yellow(f'... 还有 {len(newly_listed) - 10} 只新上榜股票')}")
        else:
            print(f"  {Colors.yellow('无新上榜股票')}")

        # 今日未出现股票
        if disappeared:
            print_summary_stats("今日未出现", len(disappeared), len(prev_stocks), Colors.RED)
            for stock_code in list(disappeared)[:10]:
                stock_name = all_daily_data[prev_date][all_daily_data[prev_date]['股票代码'].astype(str) == stock_code]['股票名称'].iloc[0] if '股票名称' in all_daily_data[prev_date].columns else "未知名称"
                print_stock_info(stock_code, stock_name, "已退出榜单", Symbols.DOWN, Colors.RED)

            if len(disappeared) > 10:
                print(f"    {Colors.yellow(f'... 还有 {len(disappeared) - 10} 只退出股票')}")
        else:
            print(f"  {Colors.green('✓ 所有昨日股票今日均出现')}")

    # 打印总体统计
    print(f"\n{Colors.bold('📊 总体统计:')}")
    print_summary_stats("总新上榜股票", total_new_stocks, color=Colors.GREEN)
    print_summary_stats("总退出股票", total_disappeared_stocks, color=Colors.RED)

    # 分析2: 连续出现N天 (例如3天)
    print_section_header(f"{Symbols.FIRE} 连续上榜分析", "=", Colors.YELLOW)
    N_DAYS = 3
    consecutive_appearance = defaultdict(int)
    stocks_consecutive_n_days = defaultdict(list) # key: stock_code, value: list of dates appeared consecutively

    for date_str in dates:
        current_stocks_on_date = all_daily_stocks[date_str]

        # 更新连续出现天数
        for stock_code in list(consecutive_appearance.keys()):
            if stock_code not in current_stocks_on_date:
                # 如果股票今天没出现，则中断连续计数
                if consecutive_appearance[stock_code] >= N_DAYS:
                     stocks_consecutive_n_days[stock_code].append(f"截至 {dates[dates.index(date_str)-1]} 连续 {consecutive_appearance[stock_code]} 天")
                del consecutive_appearance[stock_code]

        for stock_code in current_stocks_on_date:
            consecutive_appearance[stock_code] += 1
            if consecutive_appearance[stock_code] == N_DAYS:
                 # 记录首次达到N天时的日期区间
                 start_date_index = dates.index(date_str) - N_DAYS + 1
                 if start_date_index >= 0:
                    stocks_consecutive_n_days[stock_code].append(f"{dates[start_date_index]} 至 {date_str} (共 {N_DAYS} 天)")


    # 处理在最后一个日期仍然满足连续N天的情况
    for stock_code, count in consecutive_appearance.items():
        if count >= N_DAYS and not any(dates[-1] in period for period in stocks_consecutive_n_days.get(stock_code, [])):
            start_date_index = dates.index(dates[-1]) - count + 1
            if start_date_index >=0: #确保起始日期有效
                 stocks_consecutive_n_days[stock_code].append(f"{dates[start_date_index]} 至 {dates[-1]} (共 {count} 天)")


    if stocks_consecutive_n_days:
        print_summary_stats(f"连续出现 {N_DAYS} 天或以上的股票", len(stocks_consecutive_n_days), color=Colors.YELLOW)
        print()

        # 按连续天数排序显示
        sorted_stocks = []
        for stock, periods in stocks_consecutive_n_days.items():
            if periods:
                # 提取最长连续天数
                max_days = 0
                for period in periods:
                    if "共" in period and "天" in period:
                        try:
                            days = int(period.split("共")[1].split("天")[0].strip())
                            max_days = max(max_days, days)
                        except:
                            pass
                sorted_stocks.append((stock, periods, max_days))

        # 按连续天数降序排列
        sorted_stocks.sort(key=lambda x: x[2], reverse=True)

        for i, (stock, periods, max_days) in enumerate(sorted_stocks, 1):
            stock_name = all_daily_data[dates[-1]][all_daily_data[dates[-1]]['股票代码'].astype(str) == stock]['股票名称'].iloc[0] if stock in all_daily_stocks[dates[-1]] else "未知名称"

            # 根据连续天数选择不同的符号和颜色
            if max_days >= 5:
                symbol = f"{Symbols.FIRE}{Symbols.FIRE}"
                color = Colors.RED
            elif max_days >= 4:
                symbol = Symbols.FIRE
                color = Colors.YELLOW
            else:
                symbol = Symbols.STAR
                color = Colors.GREEN

            print_stock_info(stock, stock_name, f"连续上榜时段: {'; '.join(periods)}", symbol, color)
    else:
        print(f"{Colors.yellow(f'没有股票连续出现 {N_DAYS} 天或以上')}")


    # 分析3: 排名持续上升 (例如连续2天或以上)
    print_section_header(f"{Symbols.ARROW_UP} 排名持续上升分析", "=", Colors.GREEN)
    RANK_RISE_DAYS = 2 # 根据用户反馈调整为2天
    # stocks_rank_rising_data 存储每个股票的排名历史，用于判断
    stocks_rank_rising_data = defaultdict(list)
    rising_stocks_info = [] # 存储上升股票信息用于排序显示

    for date_str in dates:
        df_current_day = all_daily_data[date_str]
        # 确保 '股票代码' 列是字符串类型进行比较
        df_current_day['股票代码'] = df_current_day['股票代码'].astype(str)
        current_stocks_on_date = set(df_current_day['股票代码'])

        processed_stocks_in_loop = set() # 用于避免在同一天内对同一股票的同一上升序列多次处理

        for stock_code in current_stocks_on_date:
            if stock_code in processed_stocks_in_loop:
                continue

            current_rank = df_current_day[df_current_day['股票代码'] == stock_code]['排名'].iloc[0]

            history = stocks_rank_rising_data[stock_code]

            # 如果没有历史或者今天的日期不连续（不是前一天的后一天），则重置历史
            if not history or dates.index(date_str) != dates.index(history[-1][0]) + 1:
                history.clear()

            history.append((date_str, current_rank))

            # 保持历史记录的长度，只关注最近 RANK_RISE_DAYS 天的窗口
            while len(history) > RANK_RISE_DAYS:
                history.pop(0)

            if len(history) == RANK_RISE_DAYS:
                # 检查这个窗口内的排名是否持续上升
                is_rising_segment = True
                for i in range(RANK_RISE_DAYS - 1):
                    # 排名数字变小表示上升。如果后一天排名 >= 前一天排名，则不是持续上升。
                    if history[i+1][1] >= history[i][1]:
                        is_rising_segment = False
                        break

                if is_rising_segment:
                    stock_name_series = df_current_day[df_current_day['股票代码'] == stock_code]['股票名称']
                    stock_name = stock_name_series.iloc[0] if not stock_name_series.empty else "未知名称"

                    # 计算排名上升幅度
                    rank_improvement = history[0][1] - history[-1][1]

                    # 计算涨幅信息
                    price_change_info = calculate_price_change_for_period(
                        stock_code, history[0][0], history[-1][0], all_daily_data
                    )

                    # 构建排名详情字符串
                    rank_details_str_list = []
                    for i in range(RANK_RISE_DAYS):
                        rank_details_str_list.append(f"{history[i][0]}:排名{history[i][1]}")

                    rising_stocks_info.append({
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'rank_improvement': rank_improvement,
                        'start_date': history[0][0],
                        'end_date': history[-1][0],
                        'start_rank': history[0][1],
                        'end_rank': history[-1][1],
                        'rank_details': rank_details_str_list,
                        'price_change_info': price_change_info
                    })

                    processed_stocks_in_loop.add(stock_code)
                    # 为了能够检测到例如连续4天上升的情况 (先报1-3, 再报2-4)
                    # 我们在处理完当前窗口后，将历史记录中的最早一条移除
                    # 这样，如果后续仍然构成上升，可以被再次检测到
                    # 注意：这可能导致一个较长的连续上升被多次报告（以RANK_RISE_DAYS为窗口滑动报告）
                    # 如果只想报告一次最长的，逻辑会更复杂。目前按窗口报告。
                    history.pop(0)

    if rising_stocks_info:
        print_summary_stats(f"排名持续上升 {RANK_RISE_DAYS} 天的股票", len(rising_stocks_info), color=Colors.GREEN)
        print()

        # 按总涨幅排序（涨幅越大越靠前），如果涨幅相同则按排名上升幅度排序
        rising_stocks_info.sort(key=lambda x: (
            x['price_change_info'].get('total_change', 0) if x['price_change_info'].get('total_change') is not None else -999,
            x['rank_improvement']
        ), reverse=True)

        # 添加涨幅统计
        total_changes = [info['price_change_info'].get('total_change') for info in rising_stocks_info
                        if info['price_change_info'].get('total_change') is not None]
        if total_changes:
            avg_total_change = sum(total_changes) / len(total_changes)
            max_change = max(total_changes)
            min_change = min(total_changes)
            positive_count = len([c for c in total_changes if c > 0])

            print(f"{Colors.bold('📊 涨幅统计:')}")
            print(f"  平均总涨幅: {Colors.cyan(f'{avg_total_change:.2f}%')}")
            print(f"  最大涨幅: {Colors.green(f'{max_change:.2f}%')}")
            print(f"  最小涨幅: {Colors.red(f'{min_change:.2f}%')}")
            print(f"  上涨股票: {Colors.green(str(positive_count))} / {len(total_changes)} 只")
            print()

        for i, stock_info in enumerate(rising_stocks_info, 1):
            # 根据上升幅度选择符号和颜色
            improvement = stock_info['rank_improvement']
            price_change_info = stock_info['price_change_info']
            total_change = price_change_info.get('total_change', 0)

            # 根据涨幅和排名上升幅度选择符号和颜色
            if total_change is not None and total_change >= 10:
                symbol = f"{Symbols.ARROW_UP}{Symbols.FIRE}{Symbols.FIRE}"
                color = Colors.RED
            elif total_change is not None and total_change >= 5:
                symbol = f"{Symbols.ARROW_UP}{Symbols.FIRE}"
                color = Colors.GREEN
            elif improvement >= 10:
                symbol = f"{Symbols.ARROW_UP}{Symbols.FIRE}"
                color = Colors.RED
            elif improvement >= 5:
                symbol = Symbols.ARROW_UP
                color = Colors.GREEN
            else:
                symbol = Symbols.UP
                color = Colors.YELLOW

            rank_change_info = f"排名从 {stock_info['start_date']} 至 {stock_info['end_date']} 持续上升 {RANK_RISE_DAYS} 天"
            rank_details = f"排名详情: {', '.join(stock_info['rank_details'])}"
            improvement_text = Colors.green(f"上升 {improvement} 位")
            price_change_text = format_price_change_display(price_change_info)

            # 获取板块和概念信息
            sector = price_change_info.get('sector', '未知板块')
            concepts = price_change_info.get('concepts', '')
            market_cap = price_change_info.get('market_cap', 0)

            # 格式化市值显示
            if market_cap and pd.notna(market_cap):
                if float(market_cap) >= 1000:
                    market_cap_str = f"{float(market_cap)/1000:.1f}千亿"
                else:
                    market_cap_str = f"{float(market_cap):.1f}亿"
            else:
                market_cap_str = "未知"

            # 格式化概念显示
            concepts_str = concepts if concepts and pd.notna(concepts) and concepts.strip() else "无特殊概念"

            print_stock_info(
                stock_info['stock_code'],
                stock_info['stock_name'],
                f"{rank_change_info} ({improvement_text})",
                symbol,
                color
            )
            print(f"    📊 {Colors.cyan('申万板块')}: {sector} | {Colors.yellow('流通市值')}: {market_cap_str}")
            print(f"    🎯 {Colors.colorize('概念标签', Colors.MAGENTA)}: {concepts_str}")
            print(f"    📈 {rank_details}")
            print(f"    💰 {price_change_text}")
            print()  # 添加空行分隔
    else:
        print(f"{Colors.yellow(f'没有发现排名持续上升 {RANK_RISE_DAYS} 天的股票')}")

    # 添加分析总结
    print_section_header(f"{Symbols.CHART} 分析总结", "=", Colors.BLUE)

    total_dates = len(dates)
    total_unique_stocks = len(set().union(*all_daily_stocks.values()))
    avg_stocks_per_day = sum(len(stocks) for stocks in all_daily_stocks.values()) / len(all_daily_stocks)

    print(f"{Colors.bold('📊 数据概览:')}")
    print(f"  分析日期范围: {Colors.cyan(dates[0])} 至 {Colors.cyan(dates[-1])}")
    print(f"  总分析天数: {Colors.yellow(str(total_dates))}")
    print(f"  涉及股票总数: {Colors.yellow(str(total_unique_stocks))}")
    print(f"  平均每日股票数: {Colors.yellow(f'{avg_stocks_per_day:.1f}')}")

    print(f"\n{Colors.bold('📈 趋势统计:')}")
    print(f"  总新上榜股票: {Colors.green(str(total_new_stocks))}")
    print(f"  总退出股票: {Colors.red(str(total_disappeared_stocks))}")
    print(f"  连续上榜股票: {Colors.yellow(str(len(stocks_consecutive_n_days)))}")
    print(f"  排名上升股票: {Colors.green(str(len(rising_stocks_info)))}")

    # 计算排名上升股票的涨幅统计
    if rising_stocks_info:
        rising_total_changes = [info['price_change_info'].get('total_change') for info in rising_stocks_info
                               if info['price_change_info'].get('total_change') is not None]
        if rising_total_changes:
            rising_avg_change = sum(rising_total_changes) / len(rising_total_changes)
            rising_positive_count = len([c for c in rising_total_changes if c > 0])
            rising_positive_rate = (rising_positive_count / len(rising_total_changes)) * 100

            print(f"\n{Colors.bold('💰 排名上升股票涨幅统计:')}")
            print(f"  平均涨幅: {Colors.cyan(f'{rising_avg_change:.2f}%')}")
            print(f"  上涨比例: {Colors.green(f'{rising_positive_rate:.1f}%')} ({rising_positive_count}/{len(rising_total_changes)})")

            # 涨幅区间分布
            high_gain = len([c for c in rising_total_changes if c >= 10])
            medium_gain = len([c for c in rising_total_changes if 5 <= c < 10])
            low_gain = len([c for c in rising_total_changes if 0 < c < 5])
            negative = len([c for c in rising_total_changes if c <= 0])

            print(f"  涨幅分布:")
            print(f"    {Colors.red('≥10%')}: {high_gain} 只")
            print(f"    {Colors.green('5-10%')}: {medium_gain} 只")
            print(f"    {Colors.yellow('0-5%')}: {low_gain} 只")
            print(f"    {Colors.red('≤0%')}: {negative} 只")

        # 板块分析
        print(f"\n{Colors.bold('🏭 申万板块表现分析:')}")
        sector_stats = defaultdict(list)
        for info in rising_stocks_info:
            sector = info['price_change_info'].get('sector', '未知板块')
            total_change = info['price_change_info'].get('total_change')
            if total_change is not None:
                sector_stats[sector].append(total_change)

        # 按平均涨幅排序
        sector_performance = []
        for sector, changes in sector_stats.items():
            avg_change = sum(changes) / len(changes)
            sector_performance.append((sector, avg_change, len(changes), max(changes), min(changes)))

        sector_performance.sort(key=lambda x: x[1], reverse=True)

        for sector, avg_change, count, max_change, min_change in sector_performance[:10]:
            if avg_change > rising_avg_change:
                print(f"  🔥 {Colors.green(sector)}: 平均 {avg_change:.2f}% ({count}只) 最高 {max_change:.2f}%")
            else:
                print(f"  📊 {Colors.cyan(sector)}: 平均 {avg_change:.2f}% ({count}只) 最高 {max_change:.2f}%")

        # 概念分析
        print(f"\n{Colors.bold('🎯 热门概念表现分析:')}")
        concept_stats = defaultdict(list)
        for info in rising_stocks_info:
            concepts = info['price_change_info'].get('concepts', '')
            total_change = info['price_change_info'].get('total_change')
            if concepts and pd.notna(concepts) and concepts.strip() and total_change is not None:
                # 分割概念
                concept_list = [c.strip() for c in concepts.split(',') if c.strip()]
                for concept in concept_list:
                    concept_stats[concept].append(total_change)

        # 按平均涨幅排序，只显示出现2次以上的概念
        concept_performance = []
        for concept, changes in concept_stats.items():
            if len(changes) >= 2:  # 至少2只股票
                avg_change = sum(changes) / len(changes)
                concept_performance.append((concept, avg_change, len(changes), max(changes)))

        concept_performance.sort(key=lambda x: x[1], reverse=True)

        if concept_performance:
            for concept, avg_change, count, max_change in concept_performance[:10]:
                if avg_change >= 10:
                    print(f"  🚀 {Colors.red(concept)}: 平均 {avg_change:.2f}% ({count}只) 最高 {max_change:.2f}%")
                elif avg_change >= 5:
                    print(f"  🔥 {Colors.green(concept)}: 平均 {avg_change:.2f}% ({count}只) 最高 {max_change:.2f}%")
                else:
                    print(f"  📈 {Colors.yellow(concept)}: 平均 {avg_change:.2f}% ({count}只) 最高 {max_change:.2f}%")
        else:
            print(f"  {Colors.yellow('暂无重复出现的概念')}")

        # 市值分析
        print(f"\n{Colors.bold('💎 市值分布分析:')}")
        market_caps = []
        for info in rising_stocks_info:
            market_cap = info['price_change_info'].get('market_cap')
            total_change = info['price_change_info'].get('total_change')
            if market_cap and pd.notna(market_cap) and total_change is not None:
                market_caps.append((float(market_cap), total_change))

        if market_caps:
            # 按市值分组
            large_cap = [(mc, tc) for mc, tc in market_caps if mc >= 500]  # 大盘股
            mid_cap = [(mc, tc) for mc, tc in market_caps if 100 <= mc < 500]  # 中盘股
            small_cap = [(mc, tc) for mc, tc in market_caps if mc < 100]  # 小盘股

            for cap_data, cap_name, cap_color in [
                (large_cap, "大盘股(≥500亿)", Colors.blue),
                (mid_cap, "中盘股(100-500亿)", Colors.green),
                (small_cap, "小盘股(<100亿)", Colors.yellow)
            ]:
                if cap_data:
                    avg_change = sum(tc for _, tc in cap_data) / len(cap_data)
                    print(f"  {cap_color(cap_name)}: 平均涨幅 {avg_change:.2f}% ({len(cap_data)}只)")
                else:
                    print(f"  {cap_color(cap_name)}: 无数据")

    # 计算市场活跃度
    if total_dates > 1:
        turnover_rate = (total_new_stocks + total_disappeared_stocks) / (total_dates - 1) / avg_stocks_per_day * 100
        print(f"\n{Colors.bold('📊 市场活跃度:')}")
        print(f"  市场换手率: {Colors.cyan(f'{turnover_rate:.1f}%')} (每日平均)")

    print(f"\n{Colors.green('✅ 分析完成!')}")


def main():
    # 打印欢迎信息
    print_section_header(f"{Symbols.CHART} 股票趋势分析工具", "=", Colors.CYAN)
    print(f"{Colors.bold('欢迎使用股票趋势分析工具!')}")
    print(f"本工具将分析股票的上榜趋势、连续表现和排名变化")
    print(f"支持多日数据对比，提供详细的趋势分析报告\n")

    parser = argparse.ArgumentParser(description="分析股票每日数据的趋势。")
    parser.add_argument("--input_dir", type=str, required=True,
                        help="包含每日数据的根目录路径 (例如 'output')，"
                             "该目录下应有以日期命名的子文件夹 (例如 '20250529')，"
                             "每个子文件夹下包含 'above_cloud_daily_change.csv' 文件。")

    args = parser.parse_args()

    try:
        analyze_stock_trends(args.input_dir)
    except KeyboardInterrupt:
        print(f"\n{Colors.yellow('⚠️  用户中断了分析过程')}")
    except Exception as e:
        print(f"\n{Colors.red(f'❌ 分析过程中发生错误: {str(e)}')}")
        raise

if __name__ == "__main__":
    main()