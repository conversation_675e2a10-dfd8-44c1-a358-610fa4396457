"""
板块分析工具 (命令行版)
"""
import pandas as pd
import argparse
from datetime import datetime
from src.utils.logger import Logger

class SectorAnalyzer:
    def __init__(self):
        """初始化板块分析器"""
        self.df = None
        self.filtered_df = None
        self.current_date_filter = "全部"

    def load_data(self, file_path):
        """加载数据文件"""
        try:
            Logger.info(f"加载数据文件: {file_path}")
            self.df = pd.read_excel(file_path)
            
            # 验证必要的列
            required_columns = ['命中日期', '涨跌幅']
            missing_columns = [col for col in required_columns if col not in self.df.columns]
            if missing_columns:
                Logger.error(f"数据缺少必要的列: {', '.join(missing_columns)}")
                return False
                
            # 预处理数据
            self._preprocess_data()
            self.filtered_df = self.df.copy()
            Logger.info(f"加载完成，共 {len(self.df)} 行数据")
            return True
            
        except Exception as e:
            Logger.error(f"加载数据失败: {str(e)}")
            return False

    def _preprocess_data(self):
        """数据预处理，确保列类型正确"""
        # 处理涨跌幅
        def parse_percentage(value):
            if pd.isna(value):
                return pd.NA
            try:
                cleaned_value = str(value).strip().rstrip('%')
                return float(cleaned_value)
            except ValueError:
                Logger.warning(f"无法解析涨跌幅值: {value}")
                return pd.NA

        self.df['涨跌幅_数值'] = self.df['涨跌幅'].apply(parse_percentage)
        self.df['涨跌幅_数值'] = self.df['涨跌幅_数值'].fillna(0.0)

    def _get_all_unique_dates(self):
        """从'命中日期'列提取所有唯一的日期并排序"""
        all_dates_set = set()
        if '命中日期' in self.df.columns:
            for date_str in self.df['命中日期'].dropna().unique():
                try:
                    dates = [d.strip() for d in str(date_str).split(',') if d.strip()]
                    for date in dates:
                        datetime.strptime(date, '%Y%m%d')
                        all_dates_set.add(date)
                except (ValueError, TypeError):
                    continue
        return sorted(list(all_dates_set), reverse=True)

    def analyze_sector(self, sector_column, date_filter="全部"):
        """分析特定板块的数据"""
        if self.df is None:
            Logger.error("没有数据可分析")
            return None

        try:
            # 应用日期筛选
            if date_filter != "全部":
                filtered_df = self.df[self.df['命中日期'].str.contains(date_filter, na=False)]
            else:
                filtered_df = self.df

            if filtered_df.empty:
                Logger.warning(f"在日期 {date_filter} 没有找到数据")
                return None

            # 分析数据
            data = self._analyze_data_for_date(filtered_df, sector_column)
            
            # 转换为DataFrame以便输出
            results = []
            for item, item_data in data.items():
                avg_change = sum(item_data['changes']) / len(item_data['changes']) if item_data['changes'] else 0.0
                date_dist = [f"{date}({count})" for date, count in 
                           sorted(item_data['hit_dates'].items(), key=lambda x: x[0], reverse=True)]
                
                results.append({
                    '名称': item,
                    '股票数量': item_data['count'],
                    '平均涨跌幅': f"{avg_change:.2f}%",
                    '最大涨幅': f"{item_data['max_change']:.2f}%" if item_data['max_change'] > float('-inf') else "N/A",
                    '最小涨幅': f"{item_data['min_change']:.2f}%" if item_data['min_change'] < float('inf') else "N/A",
                    '命中日期分布': ", ".join(date_dist)
                })

            return pd.DataFrame(results)

        except Exception as e:
            Logger.error(f"分析数据时发生错误: {str(e)}")
            return None

    def _analyze_data_for_date(self, df_to_analyze, column_name):
        """分析特定日期的数据"""
        data = {}
        
        valid_rows = df_to_analyze[column_name].notna()
        valid_df = df_to_analyze[valid_rows]
        
        for items_str, change_pct, hit_date in zip(
            valid_df[column_name],
            valid_df['涨跌幅_数值'],
            valid_df['命中日期']
        ):
            items = str(items_str).split(',')
            hit_date_str = str(hit_date) if pd.notna(hit_date) else ''
            hit_dates = [d.strip() for d in hit_date_str.split(',') if d.strip()]
            
            for item in items:
                item = item.strip()
                if not item or item.lower() == 'nan':
                    continue
                    
                if item not in data:
                    data[item] = {
                        'count': 0,
                        'changes': [],
                        'max_change': float('-inf'),
                        'min_change': float('inf'),
                        'hit_dates': {}
                    }
                
                data[item]['count'] += 1
                data[item]['changes'].append(change_pct)
                data[item]['max_change'] = max(data[item]['max_change'], change_pct)
                data[item]['min_change'] = min(data[item]['min_change'], change_pct)
                
                for date in hit_dates:
                    data[item]['hit_dates'][date] = data[item]['hit_dates'].get(date, 0) + 1
                    
        return data

def main():
    parser = argparse.ArgumentParser(description="板块分析工具 (命令行版)")
    parser.add_argument("-i", "--input", required=True, help="输入的Excel文件路径")
    parser.add_argument("-d", "--date", default="全部", help="筛选日期，格式：YYYYMMDD，默认为全部")
    parser.add_argument("-t", "--type", choices=['申万板块', '同花顺概念', '开盘啦概念'], 
                      required=True, help="分析的板块类型")
    parser.add_argument("-s", "--sort", choices=['count', 'avg_change', 'max_change', 'min_change'],
                      help="排序字段")
    parser.add_argument("-o", "--output", help="输出的Excel文件路径")
    
    args = parser.parse_args()
    
    # 创建分析器实例
    analyzer = SectorAnalyzer()
    
    # 加载数据
    if not analyzer.load_data(args.input):
        return 1
        
    # 执行分析
    results_df = analyzer.analyze_sector(args.type, args.date)
    if results_df is None:
        return 1
        
    # 排序（如果指定）
    if args.sort:
        if args.sort == 'count':
            results_df = results_df.sort_values('股票数量', ascending=False)
        elif args.sort == 'avg_change':
            results_df['排序值'] = results_df['平均涨跌幅'].str.rstrip('%').astype(float)
            results_df = results_df.sort_values('排序值', ascending=False)
            results_df = results_df.drop('排序值', axis=1)
        elif args.sort == 'max_change':
            results_df['排序值'] = results_df['最大涨幅'].str.rstrip('%').astype(float)
            results_df = results_df.sort_values('排序值', ascending=False)
            results_df = results_df.drop('排序值', axis=1)
        elif args.sort == 'min_change':
            results_df['排序值'] = results_df['最小涨幅'].str.rstrip('%').astype(float)
            results_df = results_df.sort_values('排序值', ascending=False)
            results_df = results_df.drop('排序值', axis=1)
    
    # 输出结果
    if args.output:
        try:
            results_df.to_excel(args.output, index=False)
            Logger.info(f"结果已保存到: {args.output}")
        except Exception as e:
            Logger.error(f"保存结果失败: {str(e)}")
            return 1
    else:
        # 打印到控制台
        Logger.section(f"分析结果 - {args.type}")
        Logger.info(f"日期筛选: {args.date}")
        Logger.info(f"找到 {len(results_df)} 个板块\n")
        
        # 获取每列的最大宽度
        col_widths = {}
        for col in results_df.columns:
            max_width = max(
                len(str(col)),
                results_df[col].astype(str).str.len().max()
            )
            col_widths[col] = min(max_width + 2, 50)  # 限制最大宽度
        
        # 打印表头
        header = ""
        separator = ""
        for col in results_df.columns:
            width = col_widths[col]
            header += f"{str(col):<{width}}"
            separator += "-" * width + "+"
        Logger.info(header)
        Logger.info(separator)
        
        # 打印数据行
        for _, row in results_df.iterrows():
            row_str = ""
            for col in results_df.columns:
                width = col_widths[col]
                value = str(row[col])
                if len(value) > width - 2:
                    value = value[:width-5] + "..."
                row_str += f"{value:<{width}}"
            Logger.info(row_str)
    
    return 0

if __name__ == "__main__":
    exit(main()) 