"""
命令行参数解析器
"""
import argparse
from src.config.cli_config import CliConfig, get_cli_parser_config

class ArgumentParser:
    """命令行参数解析器"""

    def __init__(self):
        """初始化解析器"""
        parser_config = get_cli_parser_config()
        self.parser = argparse.ArgumentParser(description=parser_config['main_args']['description'])

        # 添加主要参数
        for arg_config in parser_config['main_args']['arguments']:
            name = arg_config.pop('name')
            self.parser.add_argument(*name, **arg_config)

        # 添加参数组
        for group_name, group_config in parser_config['groups'].items():
            group = self.parser.add_argument_group(group_config['title'])
            for arg_config in group_config['arguments']:
                name = arg_config.pop('name')
                is_explicitly_not_required = arg_config.get('required') is False
                
                if 'required' in arg_config: # Remove 'required' key as argparse uses it differently for groups
                    del arg_config['required']
                
                # If the original config said required=False and it doesn't have a default,
                # set default to None to make it truly optional for argparse.
                if is_explicitly_not_required and 'default' not in arg_config:
                    arg_config['default'] = None
                    
                group.add_argument(*name, **arg_config)

    def parse_args(self) -> CliConfig:
        """解析命令行参数"""
        args = self.parser.parse_args()
        # 将解析后的 Namespace 对象转换为字典，然后传递给 CliConfig
        config = CliConfig(**vars(args))
        return config