import pandas as pd
import argparse
import sys
import os
import numpy as np
from datetime import datetime, timedelta
import time
import re
import threading
import traceback # Import traceback for better error logging

# 从StockKLineApp中提取的数据获取逻辑
from src.data_providers.stock_data_provider import StockDataProviderFactory, TushareProvider, DataNotFoundError, DataSourceError
from src.stock_scoring.scoring_system import StockScorer
from src.utils.logger import Logger
from src.utils.rate_limiter import RateLimiter, rate_limit
from src.analysis.technical_analyzer import TechnicalAnalyzer
from src.trading.trading_simulator import TradingSimulator

class BatchProcessor:
    """批量处理器"""
    def __init__(self, max_concurrent=3, delay=1, config=None): # Add config
        self.max_concurrent = max_concurrent
        self.delay = delay
        self.results = {}
        self.config = config # Store config
        self.errors = {}
        self.lock = threading.Lock()

    def process_stock(self, stock_code, processor_instance, period, interval, data_source): # Renamed processor to processor_instance
        """处理单个股票"""
        try:
            # processor_instance is now always an instance passed from process_batch,
            # and it should have been initialized with config.
            # ExcelProcessor's load_stock_data will use its self.config.limit_rows
            if processor_instance.load_stock_data(stock_code, period, interval, data_source):
                with self.lock:
                    # Store a copy to avoid modifying the original processor's df
                    self.results[stock_code] = processor_instance.filtered_df.copy() # Use processor_instance
            else:
                with self.lock:
                    self.errors[stock_code] = "加载数据失败"
        except Exception as e:
            with self.lock:
                self.errors[stock_code] = str(e)

    def process_batch(self, stock_codes, period, interval, data_source):
        """批量处理股票数据"""
        # 清空之前的结果
        self.results.clear()
        self.errors.clear()

        # 创建线程池
        threads = []
        active_threads = []

        Logger.section("开始批量处理")
        Logger.info(f"- 股票数量: {len(stock_codes)}")
        Logger.info(f"- 并发数量: {self.max_concurrent}")
        Logger.info(f"- 数据周期: {period}")
        Logger.info(f"- 数据来源: {data_source}")

        processed_count = 0
        total_stocks = len(stock_codes)

        for stock_code in stock_codes:
            # Create a new ExcelProcessor instance for each thread, passing the config
            processor_instance = ExcelProcessor(config=self.config)
            thread = threading.Thread(
                target=self.process_stock,
                args=(stock_code, processor_instance, period, interval, data_source) # Pass instance
            )
            threads.append(thread)
            active_threads.append(thread)
            thread.start()

            processed_count += 1
            Logger.progress(processed_count, total_stocks, suffix=f"启动处理 {stock_code}")

            # 控制并发数
            if len(active_threads) >= self.max_concurrent:
                # 等待当前批次的线程完成
                for t in active_threads:
                    t.join()
                active_threads = [] # 清空活动线程列表

                # 打印当前进度
                success_count = len(self.results)
                error_count = len(self.errors)
                Logger.info(f"\n当前状态:")
                Logger.info(f"- 已完成: {success_count + error_count}")
                Logger.info(f"- 成功: {success_count}")
                Logger.info(f"- 失败: {error_count}")
                if error_count > 0:
                    Logger.info("- 错误详情:")
                    # Iterate over a copy of items to avoid runtime errors if dict changes
                    for code, error in list(self.errors.items()):
                        Logger.error(f"  - {code}: {error}")

                # 短暂延时，避免过于频繁的请求（如果需要）
                time.sleep(self.delay)


        # 等待剩余线程完成
        for t in active_threads:
            t.join()

        # 合并结果
        if self.results:
            # 将所有DataFrame合并
            combined_df = pd.DataFrame()
            for stock_code, df in self.results.items():
                # 确保每个股票数据都有股票代码列
                if df is not None and not df.empty:
                    df_with_code = df.copy()
                    # Add stock code if not present (might be missing if loaded from Excel without it)
                    if '股票代码' not in df_with_code.columns:
                         df_with_code['股票代码'] = stock_code
                    # Ensure '股票代码' is the first column if desired
                    # cols = ['股票代码'] + [col for col in df_with_code.columns if col != '股票代码']
                    # df_with_code = df_with_code[cols]
                    combined_df = pd.concat([combined_df, df_with_code], axis=0, ignore_index=True)
                else:
                     Logger.warning(f"股票 {stock_code} 的结果为空或无效，跳过合并。")


            # 重置索引 (already handled by ignore_index=True in concat)
            # combined_df.reset_index(drop=True, inplace=True)

            Logger.section("批量处理完成")
            Logger.info(f"- 总计处理: {total_stocks} 只股票")
            Logger.info(f"- 成功获取: {len(self.results)} 只")
            Logger.info(f"- 处理失败: {len(self.errors)} 只")
            if self.errors:
                Logger.info("\n失败详情:")
                for code, error in self.errors.items():
                    Logger.error(f"- {code}: {error}")

            return combined_df, self.results, self.errors
        else:
            Logger.section("批量处理完成，但没有成功获取任何数据")
            Logger.info(f"- 总计处理: {total_stocks} 只股票")
            Logger.info(f"- 全部失败: {len(self.errors)} 只")
            if self.errors:
                 Logger.info("\n失败详情:")
                 for code, error in self.errors.items():
                     Logger.error(f"- {code}: {error}")

            return pd.DataFrame(), {}, self.errors

class ExcelProcessor:
    """Excel数据处理器"""
    def __init__(self, config=None): # Add config
        """初始化Excel处理器"""
        self.df = None # Store original loaded data if needed
        self.filtered_df = None
        self.data_provider = None
        self.scorer = None
        self.min_interval = 1.0
        self.trading_results = {}
        self.analyzer = TechnicalAnalyzer()
        self.simulator = TradingSimulator()
        self.stock_data_cache = {}  # 添加数据缓存
        self.config = config # Store config
        # 为不同数据源配置特定的频率限制
        # RateLimiter内部有最小1秒的间隔强制, time_window / max_requests 低于1秒也会按1秒算
        self.datasource_specific_rate_limit_configs = {
            'tushare': {'max_requests': 100, 'time_window': 60}, # Tushare: 每分钟100次
            # 其他未在此处配置的数据源将使用 _get_market_data 中的默认值 (每3秒1次)
        }
        self.datasource_limiters = {} # 缓存特定数据源的RateLimiter实例

    def _cache_stock_data(self, stock_code, data, data_source='market'):
        """缓存股票数据"""
        self.stock_data_cache[stock_code] = {
            'data': data.copy(), # Store a copy
            'source': data_source,
            'timestamp': time.time()
        }

    def _get_cached_data(self, stock_code, max_age=3600):
        """获取缓存的股票数据，如果数据过期则返回None"""
        if stock_code in self.stock_data_cache:
            cache_entry = self.stock_data_cache[stock_code]
            if time.time() - cache_entry['timestamp'] < max_age:
                Logger.debug(f"命中缓存: {stock_code}")
                return cache_entry['data'].copy(), cache_entry['source'] # Return a copy
        return None, None

    def load_file(self, file_path):
        """加载Excel文件"""
        try:
            Logger.info(f"加载Excel文件: {file_path}")
            # 检查是否有股票代码列
            df_headers = pd.read_excel(file_path, nrows=0)
            Logger.debug(f"文件列名: {', '.join(df_headers.columns)}")

            stock_code_col = None
            stock_name_col = None
            for col in df_headers.columns:
                col_lower = col.lower()
                if '代码' in col_lower and stock_code_col is None: # Prioritize '代码'
                    stock_code_col = col
                if '名称' in col_lower and stock_name_col is None: # Prioritize '名称'
                    stock_name_col = col

            converters = {}
            if stock_code_col is not None:
                Logger.info(f"检测到股票代码列: {stock_code_col}")
                converters[stock_code_col] = str # Read codes as string
            else:
                Logger.warning("未检测到明确的股票代码列 (如 '股票代码', '代码')")

            if stock_name_col is not None:
                 Logger.info(f"检测到股票名称列: {stock_name_col}")
                 # converters[stock_name_col] = str # Usually names are already strings
 
            limit_rows = self.config.limit_rows if self.config and hasattr(self.config, 'limit_rows') else None
            if limit_rows is not None:
                Logger.info(f"限制读取行数为: {limit_rows}")
                self.df = pd.read_excel(file_path, converters=converters, nrows=limit_rows)
            else:
                self.df = pd.read_excel(file_path, converters=converters)

            # Standardize column names if detected
            rename_map = {}
            if stock_code_col and stock_code_col != '股票代码':
                 rename_map[stock_code_col] = '股票代码'
            if stock_name_col and stock_name_col != '股票名称':
                 rename_map[stock_name_col] = '股票名称'
            if rename_map:
                 self.df.rename(columns=rename_map, inplace=True)
                 Logger.info(f"已将列重命名为: {rename_map}")

            # Ensure '股票代码' column exists if possible, even if empty
            if '股票代码' not in self.df.columns:
                 self.df['股票代码'] = None # Add empty column
                 Logger.warning("添加了空的 '股票代码' 列")


            Logger.info(f"加载完成，共 {len(self.df)} 行数据")
            self.filtered_df = self.df.copy()
            return True
        except FileNotFoundError:
             Logger.error(f"文件未找到: {file_path}")
             return False
        except Exception as e:
            Logger.error(f"无法打开或处理文件 '{file_path}': {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    @rate_limit(max_requests=1, time_window=3)
    def load_stock_data(self, stock_code, period='3mo', interval='1d', data_source='sina'):
        """从数据源加载股票数据"""
        try:
            Logger.info(f"加载股票数据:")
            Logger.info(f"- 股票代码: {stock_code}")
            Logger.info(f"- 周期: {period}")
            Logger.info(f"- 间隔: {interval}")
            Logger.info(f"- 数据来源: {data_source}")

            # 检查缓存
            cached_data, cached_source = self._get_cached_data(stock_code)
            if cached_data is not None:
                Logger.info("使用缓存数据")
                self.df = cached_data # Use the cached DataFrame
                self.filtered_df = self.df.copy()
                return True

            self.data_provider = StockDataProviderFactory.get_provider(data_source)
            # Assuming get_stock_data returns a DataFrame with standard columns
            stock_df = self.data_provider.get_stock_data(stock_code, period, interval)
            if stock_df is None or stock_df.empty:
                 Logger.warning(f"未能从 {data_source} 获取到股票 {stock_code} 的数据。")
                 # Set empty df to avoid errors later, but indicate failure
                 self.df = pd.DataFrame()
                 self.filtered_df = pd.DataFrame()
                 return False # Indicate failure to load data
            
            # Apply limit_rows if configured
            limit_rows = self.config.limit_rows if self.config and hasattr(self.config, 'limit_rows') else None
            if limit_rows is not None and not stock_df.empty:
                if len(stock_df) > limit_rows:
                    stock_df = stock_df.head(limit_rows)
                    Logger.info(f"数据已根据 limit_rows={limit_rows} 限制为前 {len(stock_df)} 行")
                else:
                    Logger.info(f"数据行数 {len(stock_df)} 未超过 limit_rows={limit_rows}，无需限制")


            Logger.debug(f"数据列: {', '.join(stock_df.columns)}")
            Logger.info(f"获取到 {len(stock_df)} 行数据 (可能已受 limit_rows 限制)")
 
            # Add '股票代码' column
            stock_df['股票代码'] = stock_code
            # Try to get stock name (optional)
            try:
                 info = self.data_provider.get_stock_info(stock_code)
                 stock_df['股票名称'] = info.get('name', stock_code)
            except Exception as info_e:
                 Logger.warning(f"获取股票 {stock_code} 名称失败: {info_e}, 将使用代码作为名称。")
                 stock_df['股票名称'] = stock_code


            self.df = stock_df # Store the loaded data
            # 缓存数据
            self._cache_stock_data(stock_code, self.df, data_source)

            self.filtered_df = self.df.copy()
            return True
        except Exception as e:
            Logger.error(f"无法获取股票 {stock_code} 数据: {str(e)}")
            Logger.error(traceback.format_exc())
            self.df = pd.DataFrame() # Ensure df is empty on error
            self.filtered_df = pd.DataFrame()
            return False

    def apply_filter(self, filters):
        """应用筛选条件，增加对申万板块的处理（支持'|'分隔符和'包含'操作符）"""
        if self.filtered_df is None:
            # 检查是否只有申万板块筛选作为初始条件
            is_sw_filter_first = False
            if filters:
                # 检查第一个筛选条件是否为申万板块（支持分开的参数形式）
                first_filter = filters[0]
                if isinstance(first_filter, str):
                    # 检查完整字符串格式
                    first_filter_match = re.match(r'(申万板块|sw_industry)\s*[:=]\s*(.+)', first_filter, re.IGNORECASE)
                    if first_filter_match:
                        Logger.info("检测到首个筛选条件为申万板块")
                        is_sw_filter_first = True
                elif isinstance(first_filter, (list, tuple)) and len(first_filter) >= 1:
                    # 检查分开的参数形式
                    if first_filter[0].strip().lower() in ['申万板块', 'sw_industry']:
                        Logger.info("检测到首个筛选条件为申万板块")
                        is_sw_filter_first = True

            if is_sw_filter_first:
                self.filtered_df = pd.DataFrame()
                Logger.info("检测到首个筛选条件为申万板块，初始化数据框。")
            else:
                # 如果没有初始数据集，且首个筛选不是申万板块，则报错
                if self.filtered_df is None:
                    Logger.error("没有可筛选的数据（或首个筛选不是申万板块）。")
                    return False

        # 如果此时仍然没有数据集，初始化一个空的
        if self.filtered_df is None:
            self.filtered_df = pd.DataFrame()
            Logger.warning("初始化了一个空的数据集")

        try:
            remaining_filters = []
            shenwan_filters = []

            # === Step 1: Parse and separate Shenwan filters from others ===
            for filter_item in filters:
                # 处理两种可能的输入格式
                if isinstance(filter_item, str):
                    # 完整字符串格式
                    sw_match = re.match(r'(申万板块|sw_industry)\s*[:=]\s*(.+)', filter_item, re.IGNORECASE)
                    if sw_match:
                        Logger.info(f"解析到申万板块筛选条件: {filter_item}")
                        # 将申万板块筛选添加到专门的列表
                        column, operator = sw_match.groups()
                        value = sw_match.group(2)
                        shenwan_filters.append((column, operator, value))
                    else:
                        remaining_filters.append(filter_item)
                elif isinstance(filter_item, (list, tuple)) and len(filter_item) >= 3:
                    # 分开的参数形式
                    col, op, val = filter_item[0].strip(), filter_item[1].strip(), filter_item[2].strip()
                    if col.lower() in ['申万板块', 'sw_industry']:
                        Logger.info(f"解析到申万板块筛选条件: {col} {op} {val}")
                        # 标准化操作符
                        if op in ['包含', 'contains', ':']:
                            op = ':'
                        elif op in ['等于', 'equals', '=']:
                            op = '='
                        # 将申万板块筛选添加到专门的列表
                        shenwan_filters.append((col, op, val))
                    else:
                        # 将分开的参数组合成标准格式
                        if op in ['包含', 'contains', ':']:
                            op = ':'
                        elif op in ['等于', 'equals', '=']:
                            op = '='
                        elif op in ['大于', 'greater', '>']:
                            op = '>'
                        elif op in ['小于', 'less', '<']:
                            op = '<'
                        remaining_filters.append(f"{col}{op}{val}")

            # === Step 2: Apply Shenwan filters first if any ===
            if shenwan_filters:
                Logger.info("开始应用申万板块筛选条件...")
                for col, op, val in shenwan_filters:
                    # 处理可能的多个板块值 (用'|'分隔)
                    if '|' in val:
                        industry_list = [v.strip() for v in val.split('|')]
                        Logger.info(f"检测到多个申万板块: {industry_list}")
                    else:
                        industry_list = [val.strip()]
                        
                    # 调用申万板块处理方法
                    if not self._handle_sw_industry_filter(industry_list, op):
                        Logger.error(f"应用申万板块筛选 '{col} {op} {val}' 失败")
                        return False
                
                Logger.info(f"申万板块筛选后记录数: {len(self.filtered_df)}")
                if self.filtered_df.empty:
                    Logger.warning("申万板块筛选后数据为空，跳过其他筛选条件。")
                    return True

            # === Step 3: Apply remaining standard filters ===
            if remaining_filters:
                if self.filtered_df.empty:
                     Logger.warning("没有数据可以应用普通筛选条件。")
                     return True

                Logger.info("开始应用普通筛选条件...")
                for filter_str in remaining_filters:
                    match = re.match(r'([^><:=]+)([><:=])(.+)', filter_str)
                    if not match:
                        Logger.warning(f"无效的普通筛选条件格式: {filter_str}，跳过。")
                        continue

                    column, operator, value = match.groups()
                    column = column.strip()
                    value = value.strip()

                    if column not in self.filtered_df.columns:
                        Logger.warning(f"列 '{column}' 不存在于当前数据中，跳过此筛选条件: {filter_str}")
                        continue

                    try:
                        original_count = len(self.filtered_df)
                        col_data = self.filtered_df[column]

                        if operator == '>':
                            self.filtered_df = self.filtered_df[pd.to_numeric(col_data, errors='coerce') > float(value)]
                        elif operator == '<':
                            self.filtered_df = self.filtered_df[pd.to_numeric(col_data, errors='coerce') < float(value)]
                        elif operator == '=':
                            try:
                                num_value = float(value)
                                self.filtered_df = self.filtered_df[pd.to_numeric(col_data, errors='coerce') == num_value]
                            except ValueError:
                                self.filtered_df = self.filtered_df[col_data.astype(str) == value]
                        elif operator == ':':
                            self.filtered_df = self.filtered_df[col_data.astype(str).str.contains(value, case=False, na=False)]

                        Logger.info(f"应用筛选条件 '{filter_str}' 后记录数: {original_count} -> {len(self.filtered_df)}")

                    except ValueError:
                         Logger.warning(f"筛选条件 '{filter_str}' 的值 '{value}' 无法转换为数值进行比较，跳过。")
                         continue
                    except Exception as e:
                         Logger.warning(f"应用筛选条件 '{filter_str}' 时出错: {e}, 跳过。")
                         Logger.debug(traceback.format_exc())
                         continue

                    if self.filtered_df.empty:
                        Logger.warning("应用普通筛选条件后数据为空。")
                        break
            else:
                Logger.info("没有其他普通筛选条件需要应用。")

            # === Step 4: Final check and return ===
            if self.filtered_df.empty:
                Logger.warning("最终筛选结果为空。")
            else:
                 Logger.info(f"所有筛选条件应用完毕，最终剩余 {len(self.filtered_df)} 条记录。")

            return True

        except Exception as e:
            Logger.error(f"应用筛选条件时发生意外错误: {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    def _handle_sw_industry_filter(self, industry_identifiers, operator):
        """处理（可能多个）申万板块筛选"""
        if not isinstance(industry_identifiers, list):
            industry_identifiers = [industry_identifiers] # Ensure it's a list

        # 临时解决方案：不使用Tushare，直接基于名称筛选
        try:
            Logger.info(f"使用简化方案筛选可能属于板块的股票: {industry_identifiers}")
            
            # 确保filtered_df不为None
            if self.filtered_df is None or self.filtered_df.empty:
                if self.df is not None and not self.df.empty:
                    # 如果有原始数据，使用原始数据
                    self.filtered_df = self.df.copy()
                    Logger.info(f"使用原始数据进行筛选，共 {len(self.filtered_df)} 条记录")
                else:
                    Logger.error("没有可用的数据进行筛选")
                    return False
            
            # 确保有'股票名称'列
            if '股票名称' not in self.filtered_df.columns:
                Logger.warning("数据中没有'股票名称'列，将仅使用'股票代码'进行筛选")
            
            # 创建筛选条件
            original_count = len(self.filtered_df)
            # 创建一个布尔掩码，初始为False
            mask = pd.Series(False, index=self.filtered_df.index)
            
            # 对每个行业标识符，检查股票名称和代码是否包含
            for identifier in industry_identifiers:
                Logger.info(f"筛选关键词: {identifier}")
                
                # 先检查股票名称
                if '股票名称' in self.filtered_df.columns:
                    name_mask = self.filtered_df['股票名称'].str.contains(identifier, case=False, na=False)
                    Logger.info(f"名称匹配: {name_mask.sum()} 条记录")
                    mask = mask | name_mask
                
                # 再检查股票代码（可选）
                if '股票代码' in self.filtered_df.columns:
                    code_mask = self.filtered_df['股票代码'].astype(str).str.contains(identifier, case=False, na=False)
                    Logger.info(f"代码匹配: {code_mask.sum()} 条记录")
                    mask = mask | code_mask
                
                # 检查其他可能的列
                other_columns = ['行业', '所属行业', '板块', '所属板块']
                for col in other_columns:
                    if col in self.filtered_df.columns:
                        col_mask = self.filtered_df[col].astype(str).str.contains(identifier, case=False, na=False)
                        Logger.info(f"{col}匹配: {col_mask.sum()} 条记录")
                        mask = mask | col_mask
            
            # 应用筛选
            self.filtered_df = self.filtered_df[mask]
            Logger.info(f"板块筛选结果: {original_count} -> {len(self.filtered_df)} 条记录")
            
            # 如果筛选后没有结果，给出提示
            if self.filtered_df.empty:
                Logger.warning(f"使用关键词 {industry_identifiers} 筛选后没有匹配的记录")
                # 根据操作符决定是否视为错误
                if operator == '=':  # 精确匹配要求更严格
                    return False
            
            return True

        except Exception as e:
            # 捕获处理过程中的任何错误
            Logger.error(f"处理申万板块筛选时发生意外错误: {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    def _get_market_data(self, stock_code, period, interval, data_source):
        """获取市场数据（已实现基于数据源的动态频率限制）"""
        # 根据数据源应用特定的频率限制
        # 从 self.datasource_specific_rate_limit_configs 获取配置，如果特定数据源没有配置，则使用默认值
        limiter_config = self.datasource_specific_rate_limit_configs.get(
            data_source, {'max_requests': 1, 'time_window': 3} # 默认配置: 每3秒1次
        )
        
        # 为每个数据源维护一个独立的RateLimiter实例
        if data_source not in self.datasource_limiters:
            Logger.debug(f"为数据源 {data_source} 初始化频率限制器: 每 {limiter_config['time_window']}秒 最多 {limiter_config['max_requests']}次")
            self.datasource_limiters[data_source] = RateLimiter(
                max_requests=limiter_config['max_requests'],
                time_window=limiter_config['time_window']
            )
        
        limiter = self.datasource_limiters[data_source]
        limiter.acquire() # 在实际操作前获取许可，确保遵循频率限制

        try:
            provider = StockDataProviderFactory.get_provider(data_source)
            data = provider.get_stock_data(stock_code, period, interval)

            if data is None or data.empty: # Check for None as well
                Logger.warning(f"获取{stock_code}的数据失败或返回为空")
                return None

            # Ensure standard column names (case-insensitive matching)
            column_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }
            # Create a case-insensitive map for renaming
            rename_map = {col: column_mapping[col.lower()] for col in data.columns if col.lower() in column_mapping}
            data.rename(columns=rename_map, inplace=True)


            # Validate necessary columns exist after renaming
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                Logger.error(f"数据 {stock_code} 缺少必要的列: {', '.join(missing_columns)}")
                return None

            # Ensure correct data types
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                 data[col] = pd.to_numeric(data[col], errors='coerce')

            # Drop rows with NaN in essential columns
            original_len = len(data)
            data.dropna(subset=required_columns, inplace=True)
            if len(data) < original_len:
                 Logger.warning(f"移除了 {original_len - len(data)} 行包含NaN值的数据 for {stock_code}")

            if data.empty:
                 Logger.warning(f"数据清理后 {stock_code} 为空")
                 return None

            return data

        except Exception as e:
            Logger.error(f"获取市场数据失败 for {stock_code}: {str(e)}")
            Logger.debug(traceback.format_exc())
            return None

    def export_data(self, output_file=None):
        """导出筛选后的数据"""
        if self.filtered_df is None:
            Logger.error("没有可导出的数据 (filtered_df is None)")
            return False
        if self.filtered_df.empty:
             Logger.info("\n筛选结果为空，无需导出。")
             return True # Not an error if result is empty

        try:
            # Ensure essential columns exist before exporting/printing
            required_export_cols = ['股票代码', '股票名称', '申万板块', '流通市值', '成交额', '命中日期', '开盘啦概念', 'total_score'] # 更新推荐列
            # 打印当前数据框中所有可用的列，帮助诊断
            Logger.info(f"当前数据框中可用的列: {list(self.filtered_df.columns)}")
            
            # 不再强制要求特定列存在，改为打印提示信息
            missing_export_cols = [col for col in required_export_cols if col not in self.filtered_df.columns]
            if missing_export_cols:
                 Logger.warning(f"数据中缺少以下常用列: {missing_export_cols}，但仍将继续导出。")
            
            # 优化命中日期列的显示格式
            if '命中日期' in self.filtered_df.columns:
                Logger.info("优化命中日期列的显示格式...")
                # 直接替换原列，不保留原始数据
                self.filtered_df['命中日期'] = self.filtered_df['命中日期'].apply(
                    lambda x: self._format_hit_dates(x) if pd.notna(x) else ""
                )
                Logger.info("命中日期列格式优化完成")

            # 根据当前日期创建输出目录
            from datetime import datetime
            current_date = datetime.now().strftime('%Y%m%d')
            base_output_dir = os.path.join(os.getcwd(), 'output', current_date)
            
            # 确保输出目录存在
            if not os.path.exists(base_output_dir):
                os.makedirs(base_output_dir)
                Logger.info(f"创建输出目录: {base_output_dir}")
            
            # 获取策略名（分析类型）
            strategy_name = "default"
            if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'last_analysis_type') and self.analyzer.last_analysis_type:
                strategy_name = self.analyzer.last_analysis_type
            
            # 准备输出文件路径，使用.csv格式而不是.xlsx
            default_output_file = os.path.join(base_output_dir, f"{strategy_name}.csv")

            # 如果提供了外部输出路径，使用它
            if output_file:
                # Ensure directory exists
                output_dir = os.path.dirname(output_file)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    Logger.info(f"创建输出目录: {output_dir}")

                # 判断输出文件的扩展名，如果是.xlsx则转换为.csv
                if output_file.endswith('.xlsx') or output_file.endswith('.xls'):
                    output_file = output_file[:-len(output_file.split('.')[-1])] + 'csv'
                    Logger.info(f"将输出文件格式从Excel转换为CSV: {output_file}")

                # 导出为CSV而不是Excel
                self.filtered_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                Logger.info(f"\n数据已成功导出到：{output_file}")
            
            # 无论是否提供了外部输出路径，都写入到默认位置
            self.filtered_df.to_csv(default_output_file, index=False, encoding='utf-8-sig')
            Logger.info(f"\n数据已同时导出到默认位置：{default_output_file}")
            
            # 打印到控制台
            Logger.section("分析结果")
            
            # 检查是否有 low_vol_breakout 分析的详细分数列
            detailed_cols = ['volatility_score', 'volume_score', 'price_position_score', 'trend_score', 'rsi_score']
            has_detailed_scores = all(col in self.filtered_df.columns for col in detailed_cols)
            
            # 如果有详细得分列，则调整列顺序，将它们放在总分后面
            if has_detailed_scores:
                # 确定列的优先顺序
                priority_cols = ['股票代码', '股票名称', 'total_score']
                priority_cols.extend(detailed_cols)
                
                # 找出所有列，并按优先级重新排序
                all_cols = list(self.filtered_df.columns)
                ordered_cols = [col for col in priority_cols if col in all_cols]
                other_cols = [col for col in all_cols if col not in priority_cols]
                ordered_cols.extend(other_cols)
                
                # 使用重新排序的列显示数据
                display_df = self.filtered_df[ordered_cols]
                
                # 添加详细的得分解释
                Logger.info("\n得分解释:")
                Logger.info("- volatility_score: 波动率得分，越高表示波动率越低")
                Logger.info("- volume_score: 成交量得分，越高表示放量程度越大")
                Logger.info("- price_position_score: 价格位置得分，越高表示位置越低")
                Logger.info("- trend_score: 趋势得分，越高表示趋势更强")
                Logger.info("- rsi_score: RSI得分，越高表示更接近买入区域")
                Logger.info("- total_score: 综合得分，各维度加权平均\n")
            else:
                display_df = self.filtered_df
            
            # 打印所有行的数据
            print(display_df.to_string(index=False))

            Logger.section(f"总计: {len(self.filtered_df)} 条记录")
            return True
        except Exception as e:
            Logger.error(f"导出数据时发生错误 - {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    def apply_technical_analysis(self, analysis_type, **params):
        """应用技术分析"""
        if self.filtered_df is None or self.filtered_df.empty:
            Logger.error("没有可分析的数据 (来自 filtered_df)")
            return False

        try:
            # Ensure '股票代码' column exists
            if '股票代码' not in self.filtered_df.columns:
                Logger.error("筛选后的数据中没有 '股票代码' 列，无法进行技术分析。")
                return False

            # Initialize StockScorer if needed for 'score' analysis
            if analysis_type == 'score':
                verbose = params.get('verbose', False)
                if self.scorer is None:
                    self.scorer = StockScorer(verbose=verbose)
                elif hasattr(self.scorer, 'verbose'):
                    self.scorer.verbose = verbose

            # 记录当前分析类型到分析器
            if hasattr(self.analyzer, 'last_analysis_type'):
                self.analyzer.last_analysis_type = analysis_type

            # Get unique stock codes from the filtered DataFrame
            stock_codes = self.filtered_df['股票代码'].unique()
            total_stocks = len(stock_codes)
            if total_stocks == 0:
                 Logger.warning("筛选结果中没有有效的股票代码进行分析。")
                 return True # No stocks to analyze is not an error state

            Logger.section("开始技术分析")
            Logger.info(f"- 分析类型: {analysis_type}")
            Logger.info(f"- 股票数量: {total_stocks}")
            Logger.info(f"- 数据周期: {params.get('period', 'N/A')}") # Get period used for data loading
            Logger.info(f"- 数据来源: {params.get('data_source', 'N/A')}") # Get source used

            # Store all analysis results (dictionaries)
            all_results = []
            processed_count = 0

            # Create a mapping from stock code to stock name from filtered_df
            name_map = {}
            if '股票名称' in self.filtered_df.columns:
                 name_map = pd.Series(self.filtered_df['股票名称'].values, index=self.filtered_df['股票代码']).to_dict()


            for stock_code in stock_codes:
                processed_count += 1
                stock_name = name_map.get(stock_code, stock_code) # Get name from map or use code
                Logger.info(f"\n[进度 {processed_count}/{total_stocks}] 分析股票: {stock_code} ({stock_name})")

                try:
                    # Get stock data (prefer cache, else fetch)
                    stock_df = self._get_stock_data_for_analysis(stock_code, params)
                    if stock_df is None or stock_df.empty:
                        Logger.warning(f"无法获取或准备股票 {stock_code} 的数据，跳过分析。")
                        continue

                    # Set data for the TechnicalAnalyzer instance
                    # Pass data source to help analyzer interpret columns if needed
                    self.analyzer.set_data(stock_df, params.get('data_source', 'market'))

                    # Execute the specific analysis type
                    result = self._execute_analysis(analysis_type, stock_code, stock_name, params)
                    if result: # result should be a dictionary or None
                        all_results.append(result)
                    else:
                         Logger.warning(f"分析类型 '{analysis_type}' 未返回 {stock_code} 的有效结果。")


                except Exception as e:
                    Logger.error(f"处理股票 {stock_code} 分析时失败: {str(e)}")
                    Logger.debug(traceback.format_exc())
                    continue # Continue with the next stock

            # Update the filtered_df based on analysis results
            self._update_filtered_results(all_results, analysis_type, stock_codes)

            return len(self.filtered_df) > 0 # Return True if analysis resulted in some stocks

        except Exception as e:
            Logger.error(f"执行技术分析时发生意外错误: {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    def _get_stock_data_for_analysis(self, stock_code, params):
        """获取用于分析的股票数据 (优先缓存, 否则获取)"""
        # Prefer cached data
        stock_df, cached_source = self._get_cached_data(stock_code)

        if stock_df is not None:
            Logger.info(f"使用缓存数据进行分析: {stock_code} (来源: {cached_source})")
        else:
            # If not cached, fetch new data using parameters provided for analysis
            data_source = params.get('data_source', 'sina') # Default to sina if not specified
            period = params.get('period', '3mo')
            interval = params.get('interval', '1d')
            Logger.info(f"缓存未命中，获取新数据进行分析: {stock_code} (来源: {data_source}, 周期: {period})")

            try:
                # Use the internal _get_market_data which handles formatting and caching
                stock_df = self._get_market_data(
                    stock_code,
                    period,
                    interval,
                    data_source
                )
                if stock_df is None or stock_df.empty:
                    Logger.warning(f"无法获取用于分析的市场数据: {stock_code}")
                    return None

                # Handle realtime data addition if requested (specific to Sina for now)
                if data_source == 'sina' and params.get('realtime', False):
                    try:
                        provider = StockDataProviderFactory.get_provider('sina')
                        realtime_data = provider.get_realtime_data(stock_code)

                        if realtime_data and 'current_price' in realtime_data and realtime_data['current_price'] > 0:
                            today = pd.Timestamp.now().normalize()
                            # Check if today's data already exists (e.g., from historical fetch)
                            if today in stock_df.index:
                                Logger.info(f"今日 ({today.date()}) 数据已存在于历史数据中，更新收盘价。")
                                # Update the close price and potentially other fields if available
                                stock_df.loc[today, 'Close'] = float(realtime_data['current_price'])
                                if 'high' in realtime_data: stock_df.loc[today, 'High'] = max(stock_df.loc[today, 'High'], float(realtime_data['high']))
                                if 'low' in realtime_data: stock_df.loc[today, 'Low'] = min(stock_df.loc[today, 'Low'], float(realtime_data['low']))
                                if 'volume' in realtime_data: stock_df.loc[today, 'Volume'] = float(realtime_data['volume']) # Overwrite volume? Or add? Check logic.
                            else:
                                # Append today's data if not present
                                Logger.info(f"添加今日 ({today.date()}) 实时数据。")
                                today_data = pd.Series({
                                    'Open': float(realtime_data['open']),
                                    'High': float(realtime_data['high']),
                                    'Low': float(realtime_data['low']),
                                    'Close': float(realtime_data['current_price']),
                                    'Volume': float(realtime_data['volume'])
                                }, name=today) # Set the name of the Series to the date index

                                # Use concat instead of loc for appending a new row
                                stock_df = pd.concat([stock_df, today_data.to_frame().T])
                                # Ensure index is DatetimeIndex
                                stock_df.index = pd.to_datetime(stock_df.index)


                        else:
                             Logger.warning(f"获取到无效的实时数据: {stock_code}")

                    except Exception as rt_e:
                        Logger.warning(f"获取或处理实时数据失败 for {stock_code}: {str(rt_e)}")

                # Cache the newly fetched data
                self._cache_stock_data(stock_code, stock_df, data_source)

            except Exception as e:
                Logger.error(f"获取用于分析的市场数据失败 for {stock_code}: {str(e)}")
                Logger.debug(traceback.format_exc())
                return None

        # Log latest data point before returning
        if stock_df is not None and not stock_df.empty:
            try:
                latest_date = stock_df.index.max()
                latest_data = stock_df.loc[latest_date]
                Logger.section(f"股票 {stock_code} 最新数据 ({latest_date.strftime('%Y-%m-%d')})")
                log_str = ", ".join([f"{col}: {latest_data[col]:.2f}" if isinstance(latest_data[col], (int, float)) else f"{col}: {latest_data[col]}" for col in ['Open', 'High', 'Low', 'Close', 'Volume'] if col in latest_data])
                Logger.info(log_str)
            except Exception as log_e:
                 Logger.warning(f"记录最新数据时出错: {log_e}")


        return stock_df

    def _execute_analysis(self, analysis_type, stock_code, stock_name, params):
        """执行具体的技术分析 (调用 TechnicalAnalyzer 的方法)"""
        try:
            # 记录最近执行的分析类型
            if hasattr(self.analyzer, 'last_analysis_type'):
                self.analyzer.last_analysis_type = analysis_type
            
            # Map analysis_type to TechnicalAnalyzer methods
            if analysis_type == 'above_cloud':
                # Assuming analyze_ichimoku exists and returns the required dict format
                return self.analyzer.analyze_ichimoku(stock_code, stock_name, params)
            elif analysis_type == 'above_cloud_early':
                 # Assuming analyze_ichimoku_early exists
                 return self.analyzer.analyze_ichimoku_early(stock_code, stock_name, params)
            elif analysis_type == 'ma_pullback':
                 # Assuming analyze_ma_pullback exists
                 return self.analyzer.analyze_ma_pullback(stock_code, stock_name, params)
            elif analysis_type == 'low_vol_breakout':
                 # Assuming analyze_low_vol_breakout exists
                 return self.analyzer.analyze_low_vol_breakout(stock_code, stock_name, params)
            elif analysis_type == 'score':
                 # Score analysis might be handled differently, maybe directly by StockScorer?
                 # Or TechnicalAnalyzer has a 'score' method. Assuming the latter for now.
                 if hasattr(self.analyzer, 'score'):
                      # Pass scorer instance if needed by the analyzer's score method
                      params['scorer'] = self.scorer
                      return self.analyzer.score(stock_code, stock_name, params)
                 else:
                      Logger.error(f"TechnicalAnalyzer 没有找到 'score' 方法。")
                      return None
            # Add other analysis types here...
            else:
                Logger.error(f"不支持的技术分析类型: {analysis_type}")
                return None
        except AttributeError as ae:
             Logger.error(f"调用分析方法时出错 (方法 '{analysis_type}' 可能不存在于 TechnicalAnalyzer): {ae}")
             return None
        except Exception as e:
            Logger.error(f"分析股票 {stock_code} ({analysis_type}) 失败: {str(e)}")
            Logger.debug(traceback.format_exc())
            return None

    def _update_filtered_results(self, results, analysis_type, original_codes):
        """根据分析结果更新 filtered_df"""
        total_analyzed = len(original_codes)
        if not results:
            Logger.info("技术分析未返回任何结果。")
            self.filtered_df = pd.DataFrame() # Clear filtered results
            return

        # Filter results based on 'meets_criteria' flag
        passed_results = [r for r in results if isinstance(r, dict) and r.get('meets_criteria', False)]

        if not passed_results:
            Logger.info("没有股票符合技术分析条件。")
            self.filtered_df = pd.DataFrame() # Clear filtered results
            return

        # Create a DataFrame from the passed results
        new_df = pd.DataFrame(passed_results)

        # Ensure essential columns exist
        if 'stock_code' not in new_df.columns:
             Logger.error("分析结果缺少 'stock_code'，无法更新筛选列表。")
             self.filtered_df = pd.DataFrame()
             return
        new_df.rename(columns={'stock_code': '股票代码'}, inplace=True) # Standardize name

        if 'stock_name' not in new_df.columns:
             Logger.warning("分析结果缺少 'stock_name'，将尝试从原始数据映射。")
             # Try to map from original filtered_df if it exists and has names
             if self.filtered_df is not None and '股票名称' in self.filtered_df.columns:
                  name_map = pd.Series(self.filtered_df['股票名称'].values, index=self.filtered_df['股票代码']).to_dict()
                  new_df['股票名称'] = new_df['股票代码'].map(name_map)
             else:
                  new_df['股票名称'] = new_df['股票代码'] # Fallback to code
        else:
             new_df.rename(columns={'stock_name': '股票名称'}, inplace=True)


        # Add analysis-specific columns (e.g., score)
        if analysis_type in ['score', 'above_cloud', 'above_cloud_early', 'ma_pullback', 'low_vol_breakout']: # Add other types if they produce scores
            if 'total_score' in new_df.columns:
                # Sort by score if the column exists
                new_df = new_df.sort_values('total_score', ascending=False)
            else:
                 Logger.warning(f"分析类型 '{analysis_type}' 的结果中未找到 'total_score' 列进行排序。")
                 
        # 低位放量突破分析时，特别处理从scores字典中提取的各维度得分
        if analysis_type == 'low_vol_breakout':
            # 检查是否有各维度得分
            score_cols = ['volatility_score', 'volume_score', 'price_position_score', 'trend_score', 'rsi_score']
            detailed_cols_exist = any(col in new_df.columns for col in score_cols)
            
            # 如果不存在详细得分列但有原始scores字典，则从中提取
            if not detailed_cols_exist and 'scores' in new_df.columns:
                Logger.info("从scores字典中提取各维度详细得分...")
                try:
                    for col in score_cols:
                        new_df[col] = new_df['scores'].apply(lambda x: x.get(col, 0) if isinstance(x, dict) else 0)
                    
                    # 尝试提取原始指标值
                    if 'raw_metrics' in new_df.columns or any(x.get('raw_metrics', None) for x in new_df['scores'] if isinstance(x, dict)):
                        new_df['volatility'] = new_df['scores'].apply(
                            lambda x: x.get('raw_metrics', {}).get('volatility', None) if isinstance(x, dict) else None)
                        new_df['volume_ratio'] = new_df['scores'].apply(
                            lambda x: x.get('raw_metrics', {}).get('volume_ratio', None) if isinstance(x, dict) else None)
                        new_df['rsi'] = new_df['scores'].apply(
                            lambda x: x.get('raw_metrics', {}).get('rsi', None) if isinstance(x, dict) else None)
                        
                    Logger.info("各维度得分提取完成")
                except Exception as ex:
                    Logger.error(f"提取详细得分时出错: {str(ex)}")

        # Select and reorder columns for the final filtered_df
        # Prioritize essential columns, then score, then others from analysis
        final_cols = ['股票代码', '股票名称']
        if 'total_score' in new_df.columns:
             final_cols.append('total_score')
             
        # 如果是低位放量突破分析，添加各维度得分列
        if analysis_type == 'low_vol_breakout':
            score_cols = ['volatility_score', 'volume_score', 'price_position_score', 'trend_score', 'rsi_score']
            for col in score_cols:
                if col in new_df.columns:
                    final_cols.append(col)
                    
            # 添加原始指标值列
            raw_metric_cols = ['volatility', 'volume_ratio', 'rsi']
            for col in raw_metric_cols:
                if col in new_df.columns:
                    final_cols.append(col)
                    
        # Add other relevant columns from the analysis results if needed
        # Example: final_cols.extend([col for col in new_df.columns if col not in final_cols])
        # For now, keep it simple:
        final_cols.extend([col for col in new_df.columns if col not in final_cols and col != 'scores' and col != 'meets_criteria' and col != 'raw_metrics'])

        # 创建最终数据框
        self.filtered_df = new_df[final_cols].copy()
        
        # 从原始数据中保留更多重要列
        if self.df is not None and '股票代码' in self.df.columns and len(self.filtered_df) > 0:
            Logger.info("尝试从原始数据中提取额外的列...")
            # 需要保留的重要列
            important_cols = ['申万板块', '流通市值', '成交额', '命中日期', '开盘啦概念']
            # 找出原始数据中存在的列
            available_cols = [col for col in important_cols if col in self.df.columns]
            
            if available_cols:
                Logger.info(f"找到可用的额外列: {available_cols}")
                # 确保股票代码列格式一致用于合并
                self.filtered_df['股票代码'] = self.filtered_df['股票代码'].astype(str)
                orig_df_temp = self.df.copy()
                orig_df_temp['股票代码'] = orig_df_temp['股票代码'].astype(str)
                
                # 合并额外列
                merge_cols = ['股票代码']
                self.filtered_df = pd.merge(
                    self.filtered_df, 
                    orig_df_temp[merge_cols + available_cols], 
                    on=merge_cols, 
                    how='left'
                )
                Logger.info(f"成功添加额外列，现有列: {list(self.filtered_df.columns)}")
            else:
                Logger.warning(f"原始数据中没有找到以下重要列: {important_cols}")

        # Log summary
        passed_count = len(self.filtered_df)
        pass_rate = (passed_count / total_analyzed * 100) if total_analyzed > 0 else 0
        Logger.info(f"\n技术分析 ({analysis_type}) 完成")
        Logger.info(f"- 总计分析: {total_analyzed} 只股票")
        Logger.info(f"- 符合条件: {passed_count} 只")
        Logger.info(f"- 通过率: {pass_rate:.1f}%")


    def _run_trading_simulation(self):
        """运行交易模拟 (Placeholder/Example)"""
        # This needs significant refinement based on actual simulation logic
        if self.filtered_df is None or self.filtered_df.empty:
             Logger.warning("没有数据进行交易模拟。")
             return
        if '命中日期' not in self.filtered_df.columns: # Example column needed for simulation
            Logger.warning("筛选结果缺少 '命中日期' 列，无法运行此示例交易模拟。")
            return

        # Example: Extract signals based on a specific column
        stock_signal_dates = {}
        # Example target dates - replace with dynamic logic
        target_dates = ['20250328', '20250327', '20250326', '20250321', '20250319']

        for _, row in self.filtered_df.iterrows():
            stock_code = row['股票代码']
            hit_dates_str = row['命中日期']
            if pd.notna(hit_dates_str):
                hit_dates = [date.strip() for date in str(hit_dates_str).split(',')]
                valid_dates = [date for date in hit_dates if date in target_dates] # Filter by target dates
                if valid_dates:
                    stock_signal_dates[stock_code] = valid_dates # Store signals

        if stock_signal_dates:
            Logger.info("\n执行交易模拟...")
            # Ensure the simulator and data fetching callback are correctly set up
            # The callback _get_market_data needs to match simulator's expected signature
            try:
                 self.simulator.simulate_trading(
                     stock_signal_dates,
                     self.filtered_df, # Pass current filtered data
                     self._get_market_data # Pass the data fetching method
                 )
                 # Store or display simulation results from self.simulator.results if needed
                 self.trading_results = self.simulator.results
                 Logger.info("交易模拟完成。")
                 # print(self.trading_results) # Optional: print results
            except Exception as sim_e:
                 Logger.error(f"交易模拟执行失败: {sim_e}")
                 Logger.debug(traceback.format_exc())

        else:
             Logger.info("没有找到符合交易模拟条件的信号。")


    def apply_left_score_analysis(self):
        """应用左侧打分分析 (Placeholder)"""
        if self.filtered_df is None or self.filtered_df.empty:
            Logger.error("没有可分析的数据 (for left score)")
            return False

        try:
            Logger.section("开始左侧打分分析")

            if '股票代码' not in self.filtered_df.columns:
                Logger.error("数据中没有股票代码列 (for left score)")
                return False

            stock_codes = self.filtered_df['股票代码'].unique()
            total_stocks = len(stock_codes)
            Logger.info(f"- 分析股票数量: {total_stocks}")

            all_results = []
            processed_count = 0

            name_map = {}
            if '股票名称' in self.filtered_df.columns:
                 name_map = pd.Series(self.filtered_df['股票名称'].values, index=self.filtered_df['股票代码']).to_dict()


            for stock_code in stock_codes:
                processed_count += 1
                stock_name = name_map.get(stock_code, stock_code)
                Logger.info(f"\n[进度 {processed_count}/{total_stocks}] 分析股票: {stock_code} {stock_name}")

                try:
                    # TODO: Implement actual left scoring logic here
                    # This likely involves fetching historical data and applying specific rules
                    # Example placeholder result:
                    left_score_value = np.random.randint(0, 100) # Replace with actual calculation
                    meets_criteria_flag = left_score_value > 50 # Example criteria

                    result = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'left_score': left_score_value,
                        'meets_criteria': meets_criteria_flag
                    }
                    all_results.append(result)

                except Exception as e:
                    Logger.error(f"处理股票 {stock_code} 左侧打分失败: {str(e)}")
                    Logger.debug(traceback.format_exc())
                    continue

            # Update filtered results based on left score analysis
            if all_results:
                score_df = pd.DataFrame([
                    {'股票代码': r['stock_code'], 'left_score': r['left_score']}
                    for r in all_results if r.get('meets_criteria', False) # Filter by criteria
                ])

                if not score_df.empty:
                    # Merge the scores back into the main filtered dataframe
                    # Ensure '股票代码' is string for merging
                    self.filtered_df['股票代码'] = self.filtered_df['股票代码'].astype(str)
                    score_df['股票代码'] = score_df['股票代码'].astype(str)

                    # Keep only rows that passed the criteria and add the score
                    self.filtered_df = pd.merge(
                        self.filtered_df,
                        score_df,
                        on='股票代码',
                        how='inner' # Keep only stocks that passed and have a score
                    )

                    # Move 'left_score' column to the front and sort
                    if 'left_score' in self.filtered_df.columns:
                         cols = ['left_score'] + [col for col in self.filtered_df.columns if col != 'left_score']
                         self.filtered_df = self.filtered_df[cols]
                         self.filtered_df = self.filtered_df.sort_values('left_score', ascending=False)
                else:
                     # No stocks met the criteria
                     Logger.warning("没有股票符合左侧打分条件。")
                     self.filtered_df = pd.DataFrame() # Clear results

            else:
                 # No results from analysis
                 Logger.warning("左侧打分分析未产生任何结果。")
                 self.filtered_df = pd.DataFrame() # Clear results


            Logger.info(f"\n左侧打分分析完成")
            Logger.info(f"- 符合条件的股票数: {len(self.filtered_df)}") # Log count after merge

            return True

        except Exception as e:
            Logger.error(f"执行左侧打分分析时发生错误: {str(e)}")
            Logger.error(traceback.format_exc())
            return False

    def analyze_ichimoku(self, stock_code, stock_name, params):
        """
        一目均衡表分析的统一接口 (Calls TechnicalAnalyzer method)

        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            params: 分析参数

        Returns:
            Dict: 分析结果 or None if analysis fails
        """
        try:
            # Ensure self.analyzer has data set for this stock
            # This check should ideally be inside the TechnicalAnalyzer method itself
            # or ensured by the calling context (apply_technical_analysis)
            if self.analyzer.data is None or self.analyzer.data.empty:
                 Logger.warning(f"分析器没有为 {stock_code} 设置数据，无法执行一目均衡表分析。")
                 return None # Cannot proceed without data

            days = params.get('days', 5)

            # Check if the method exists in the analyzer instance
            if not hasattr(self.analyzer, 'check_ichimoku_signal'):
                 Logger.error("TechnicalAnalyzer 类缺少 'check_ichimoku_signal' 方法。")
                 return None

            # Call the analyzer's method
            meets_criteria, scores = self.analyzer.check_ichimoku_signal(days)

            # Construct the result dictionary
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'meets_criteria': meets_criteria, # bool
                'total_score': scores.get('total_score', 0), # numeric score or 0
                'scores': scores # dictionary of detailed scores
            }
        except AttributeError as ae:
             # This catches if check_ichimoku_signal doesn't exist during runtime check (redundant if hasattr used)
             Logger.error(f"调用分析方法时出错: {ae}")
             return None
        except Exception as e:
            Logger.error(f"为 {stock_code} 执行一目均衡表分析失败: {str(e)}")
            Logger.debug(traceback.format_exc())
            return None

    def _format_hit_dates(self, date_str):
        """
        格式化命中日期字符串
        示例：将 "20250527,20250526,20250520" 转换为 "0527(3)"
        """
        try:
            if not date_str or pd.isna(date_str):
                return ""
                
            # 分割日期字符串
            dates = str(date_str).split(',')
            dates = [d.strip() for d in dates if d.strip()]
            
            if not dates:
                return ""
                
            # 计算日期数量
            date_count = len(dates)
            
            # 获取第一个日期并格式化
            first_date = dates[0]
            # 尝试提取月日部分（假设格式为YYYYMMDD）
            if len(first_date) >= 8 and first_date.isdigit():
                formatted_date = first_date[-4:]  # 取后4位，即MMDD
            else:
                formatted_date = first_date  # 如果格式不符合预期，保持原样
                
            # 构建最终格式
            if date_count > 1:
                return f"{formatted_date}({date_count})"
            else:
                return formatted_date
                
        except Exception as e:
            Logger.warning(f"格式化命中日期失败: {e}")
            return str(date_str)  # 返回原字符串

    def process_csv_for_daily_change(self, csv_path):
        """
        读取指定的CSV文件，提取股票代码和命中日期，
        获取该日期的开盘价和收盘价，计算并打印当日涨幅。
        保留原始CSV的所有列，在后面添加新的计算列。
        """
        Logger.section(f"开始处理CSV文件计算当日涨幅: {csv_path}")
        try:
            # 从CSV路径推断年份
            path_parts = csv_path.replace("\\\\", "/").split('/')
            year_str = None
            # Try to find YYYYMMDD first as it's more specific
            for part in reversed(path_parts):
                if re.fullmatch(r"\d{8}", part): # 例如: 20250529
                    year_str = part[:4]
                    Logger.debug(f"推断年份 '{year_str}' 来自路径部分 '{part}' (YYYYMMDD)")
                    break
            
            if not year_str: # If YYYYMMDD not found, try to find YYYY
                for part in reversed(path_parts):
                    # Match a part that is exactly 4 digits and looks like a plausible year
                    if re.fullmatch(r"\d{4}", part):
                        if 1990 <= int(part) <= datetime.now().year + 5: # Basic year validity check
                            year_str = part
                            Logger.debug(f"推断年份 '{year_str}' 来自路径部分 '{part}' (YYYY)")
                            break
            
            if not year_str: # Fallback to current year if no year part is found
                year_str = str(datetime.now().year)
                Logger.warning(f"未能从路径 '{csv_path}' 推断年份，将使用当前年份: {year_str}")
            else:
                Logger.info(f"推断CSV数据的年份为: {year_str} (基于路径: {csv_path})")

            csv_df = pd.read_csv(csv_path, dtype={'股票代码': str})
            Logger.info(f"成功加载CSV文件，共 {len(csv_df)} 行数据。")
            Logger.info(f"原始CSV列名: {list(csv_df.columns)}")
        except FileNotFoundError:
            Logger.error(f"CSV文件未找到: {csv_path}")
            return
        except Exception as e:
            Logger.error(f"读取CSV文件 '{csv_path}' 失败: {e}")
            Logger.debug(traceback.format_exc())
            return

        if '股票代码' not in csv_df.columns:
            Logger.error("CSV文件必须包含 '股票代码' 列。")
            return

        # 创建结果DataFrame，从原始CSV复制所有数据
        result_df = csv_df.copy()
        
        # 初始化新列
        result_df['处理日期'] = None
        result_df['开盘价'] = None
        result_df['收盘价'] = None
        result_df['当日涨幅(%)'] = None
        result_df['处理状态'] = None
        
        processed_count = 0
        failed_count = 0
        target_date_str = datetime.now().strftime('%Y-%m-%d')

        for index, row in result_df.iterrows():
            stock_code_raw = row['股票代码']
            stock_code_match = re.match(r"(\d{6})", str(stock_code_raw))
            if stock_code_match:
                stock_code = stock_code_match.group(1)
            else:
                stock_code = str(stock_code_raw).split('.')[0]

            try:
                # 强制使用tushare数据源进行CSV处理
                current_data_source = 'tushare'
                
                if not hasattr(self, 'data_provider') or self.data_provider is None or \
                   (hasattr(self.data_provider, 'source_name') and self.data_provider.source_name != current_data_source):
                    self.data_provider = StockDataProviderFactory.get_provider(current_data_source)
                    Logger.debug(f"为CSV处理初始化/切换数据提供者: {current_data_source}")

                Logger.info(f"处理: 代码={stock_code}, 日期={target_date_str}, 源数据代码={stock_code_raw}")
                
                # 添加API调用间隔控制（每分钟150次 = 每0.4秒一次）
                time.sleep(0.5)  # 稍微保守一点，每0.5秒一次
                
                stock_day_data = self.data_provider.get_stock_data_by_date(
                    stock_code,
                    start_date=target_date_str,
                    end_date=target_date_str
                )
                
                if stock_day_data is None:
                    Logger.warning(f"股票 {stock_code}: 未能获取 {target_date_str} 的数据。")
                    result_df.loc[index, '处理状态'] = '获取数据失败'
                    failed_count += 1
                    continue
                
                if stock_day_data.empty:
                    Logger.warning(f"股票 {stock_code}: 获取到空数据集，日期: {target_date_str}")
                    result_df.loc[index, '处理状态'] = '数据为空'
                    failed_count += 1
                    continue

                open_price_col, close_price_col = None, None
                df_cols_lower = {col.lower(): col for col in stock_day_data.columns}
                for col_option in ['open', '开盘', '开盘价']:
                    if col_option in df_cols_lower: open_price_col = df_cols_lower[col_option]; break
                for col_option in ['close', '收盘', '收盘价']:
                    if col_option in df_cols_lower: close_price_col = df_cols_lower[col_option]; break
                
                if not open_price_col or not close_price_col:
                    Logger.warning(f"股票 {stock_code}: {target_date_str} 数据缺少规范的开盘/收盘价列。可用列: {stock_day_data.columns.tolist()}")
                    result_df.loc[index, '处理状态'] = '缺少价格列'
                    failed_count += 1
                    continue
                
                day_data_row = stock_day_data.iloc[0]
                open_price_val = day_data_row[open_price_col]
                close_price_val = day_data_row[close_price_col]

                if pd.isna(open_price_val) or pd.isna(close_price_val) or open_price_val == 0:
                    Logger.warning(f"股票 {stock_code}: {target_date_str} 开盘/收盘价无效或开盘价为0 (开盘={open_price_val}, 收盘={close_price_val})，跳过。")
                    result_df.loc[index, '处理状态'] = '价格数据无效'
                    failed_count += 1
                    continue
                
                open_price = float(open_price_val)
                close_price = float(close_price_val)
                change_percent = ((close_price - open_price) / open_price) * 100
                
                # 更新结果DataFrame中的对应行
                result_df.loc[index, '处理日期'] = target_date_str
                result_df.loc[index, '开盘价'] = round(open_price, 2)
                result_df.loc[index, '收盘价'] = round(close_price, 2)
                result_df.loc[index, '当日涨幅(%)'] = round(change_percent, 2)
                result_df.loc[index, '处理状态'] = '成功'
                
                Logger.info(f"代码: {stock_code_raw} ({stock_code}), 日期: {target_date_str}, 开盘: {open_price:.2f}, 收盘: {close_price:.2f}, 涨幅: {change_percent:.2f}%")
                processed_count += 1

            except DataNotFoundError:
                 Logger.warning(f"股票 {stock_code}: 未找到 {target_date_str} 的数据 (DataNotFoundError)。")
                 result_df.loc[index, '处理状态'] = 'DataNotFoundError'
                 failed_count += 1
            except DataSourceError as dse:
                 Logger.error(f"股票 {stock_code}: 获取 {target_date_str} 数据时发生数据源错误: {dse}")
                 result_df.loc[index, '处理状态'] = f'DataSourceError: {str(dse)}'
                 failed_count += 1
            except Exception as e:
                Logger.error(f"股票 {stock_code}: 处理 {target_date_str} 时发生错误: {e}")
                Logger.debug(traceback.format_exc())
                result_df.loc[index, '处理状态'] = f'处理错误: {str(e)}'
                failed_count += 1
        
        Logger.section("CSV涨幅计算处理总结:")
        Logger.info(f"共成功计算 {processed_count} 条股票的涨幅数据。")
        Logger.info(f"共 {failed_count} 条记录处理失败或被跳过。")
        Logger.info(f"总计尝试处理 {len(csv_df)} 行CSV数据。")
        
        # 保存包含原始列和新增列的完整结果
        try:
            current_date_str = datetime.now().strftime("%Y%m%d")
            output_folder = os.path.join("output", current_date_str)
            os.makedirs(output_folder, exist_ok=True)
            
            # Determine output filename based on the input CSV filename
            input_csv_filename = os.path.splitext(os.path.basename(csv_path))[0]
            output_filename = f"{input_csv_filename}_daily_change.csv"
            
            output_file_path = os.path.join(output_folder, output_filename)
            
            result_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            Logger.info(f"完整结果已保存到: {output_file_path}")
            
            # 可选：显示结果预览
            if processed_count > 0:
                Logger.info("\n结果预览（仅显示成功处理的前5行）:")
                successful_rows = result_df[result_df['处理状态'] == '成功'].head(5)
                # 显示关键列
                key_columns = ['股票代码', '股票名称'] if '股票名称' in result_df.columns else ['股票代码']
                key_columns.extend(['处理日期', '开盘价', '收盘价', '当日涨幅(%)', '处理状态'])
                available_key_columns = [col for col in key_columns if col in result_df.columns]
                print(successful_rows[available_key_columns].to_string(index=False))
            
        except Exception as e:
            Logger.error(f"保存结果到CSV文件失败: {e}")
            Logger.debug(traceback.format_exc())

# Import the new ArgumentParser and CliConfig
from src.cli.argument_parser import ArgumentParser
from src.config.cli_config import CliConfig # Ensure CliConfig is imported
from src.cli.stock_manager import StockManager # For category input

def main():
    # Initialize the custom argument parser
    arg_parser = ArgumentParser()
    config = arg_parser.parse_args() # This returns a CliConfig object

    # Initialize ExcelProcessor with the parsed config
    processor = ExcelProcessor(config=config)
    stock_manager = StockManager() # For managing stock lists by category

    initial_data_loaded = False

    if config.mode == 'cli':
        Logger.info("CLI模式启动...")

        # 1. Handle stock management commands first as they are standalone
        if config.add_stock:
            stock_manager.add_stock(config.add_stock, config.category)
            Logger.info(f"股票 {config.add_stock} 已添加到分类 '{config.category}'.")
            return # Exit after stock management
        if config.remove_stock:
            stock_manager.remove_stock(config.remove_stock, config.category)
            # remove_stock in StockManager should ideally confirm removal
            return # Exit after stock management
        if config.list_stocks:
            stock_manager.list_stocks(config.category)
            return # Exit after stock management

        # 2. Handle CSV input for daily change calculation
        if config.calc_daily_change_from_csv_path:
            Logger.info(f"检测到从CSV计算当日涨幅模式，文件: {config.calc_daily_change_from_csv_path}")
            processor.process_csv_for_daily_change(config.calc_daily_change_from_csv_path)
            # Decide if other operations should follow or if this is exclusive
            # For now, assume it's an exclusive operation for simplicity
            Logger.info("从CSV计算当日涨幅处理完成。")
            return # Exit after CSV processing

        # 3. Handle main data processing (Excel, stock codes, category input)
        input_source_specified = config.input_path or config.category_input
        
        if not input_source_specified:
            Logger.warning("CLI模式下，如果不是股票管理或CSV处理，则需要通过 --input 或 --category-input 指定输入。")
            # sys.exit(1) # Or show help
            return

        # --- Load Initial Data ---
        if config.input_path:
            if config.is_stock: # Input is a list of stock codes
                stock_codes = [s.strip() for s in config.input_path.split(',')]
                Logger.info(f"从命令行参数获取到股票代码: {stock_codes}")
                batch_processor = BatchProcessor(
                    max_concurrent=config.batch_size, # Use config values
                    delay=config.batch_delay,
                    config=config # Pass config to BatchProcessor
                )
                combined_df, _, errors = batch_processor.process_batch(
                    stock_codes,
                    config.period,
                    config.interval,
                    config.data_source
                )
                if combined_df.empty and errors:
                    Logger.error("批量处理股票数据失败。")
                    processor.filtered_df = pd.DataFrame()
                elif combined_df.empty:
                    Logger.warning("批量处理未返回任何股票数据。")
                    processor.filtered_df = pd.DataFrame()
                else:
                    processor.filtered_df = combined_df
                    initial_data_loaded = True
            else: # Input is an Excel file
                if processor.load_file(config.input_path): # load_file uses self.config.limit_rows
                    initial_data_loaded = True
                else:
                    Logger.error(f"无法加载或处理Excel输入文件: {config.input_path}")
                    sys.exit(1)
        elif config.category_input:
            Logger.info(f"从分类 '{config.category_input}' 加载股票...")
            stock_codes = stock_manager.get_stocks_by_category(config.category_input)
            if not stock_codes:
                Logger.warning(f"分类 '{config.category_input}' 为空或不存在。")
                processor.filtered_df = pd.DataFrame() # Ensure it's an empty DF
            else:
                Logger.info(f"从分类 '{config.category_input}' 获取到股票代码: {stock_codes}")
                batch_processor = BatchProcessor(
                    max_concurrent=config.batch_size,
                    delay=config.batch_delay,
                    config=config # Pass config
                )
                combined_df, _, errors = batch_processor.process_batch(
                    stock_codes,
                    config.period,
                    config.interval,
                    config.data_source
                )
                if combined_df.empty and errors:
                    Logger.error(f"批量处理分类 '{config.category_input}' 中的股票数据失败。")
                    processor.filtered_df = pd.DataFrame()
                elif combined_df.empty:
                    Logger.warning(f"批量处理分类 '{config.category_input}' 中的股票未返回任何数据。")
                    processor.filtered_df = pd.DataFrame()
                else:
                    processor.filtered_df = combined_df
                    initial_data_loaded = True
        
        # --- Apply Filters (if data was loaded or filters can operate on empty df like SW) ---
        if config.filters:
            # The new ArgumentParser should provide filters in the correct format
            # (list of tuples, where each tuple is (column, operator, value))
            # The apply_filter method is assumed to handle this format or be adapted.
            if not processor.apply_filter(config.filters): # apply_filter uses self.config.limit_rows if needed
                Logger.error("应用筛选条件失败。")
                sys.exit(1)
        elif not initial_data_loaded:
            # This case might be redundant if input_source_specified check is robust
            Logger.error("没有提供有效的输入数据或筛选条件 (且未从分类加载数据)。")
            sys.exit(1)

        # --- Perform Technical Analysis ---
        if config.analysis_type:
            if processor.filtered_df is None or processor.filtered_df.empty:
                Logger.warning(f"没有数据可供执行技术分析 '{config.analysis_type}'。")
            else:
                # Pass all relevant config parameters to the analysis function
                # The apply_technical_analysis method should ideally take the config object
                # or specific parameters from it.
                analysis_params = {
                    'days': config.days,
                    'ma_period': config.ma_period,
                    'trend': config.trend,
                    'min_score': config.min_score,
                    'verbose': config.verbose,
                    # Pass data loading params needed for analysis data fetching within apply_technical_analysis
                    'period': config.period,
                    'interval': config.interval,
                    'data_source': config.data_source,
                    'realtime': config.realtime,
                    'limit_rows': config.limit_rows # Pass limit_rows for consistency
                }
                if not processor.apply_technical_analysis(config.analysis_type, **analysis_params):
                    Logger.warning(f"技术分析 '{config.analysis_type}' 执行完成，但可能没有符合条件的股票。")

        # --- Perform Left Score Analysis ---
        if config.left_score:
            if processor.filtered_df is None or processor.filtered_df.empty:
                 Logger.warning("没有数据可供执行左侧打分分析。")
            else:
                 processor.apply_left_score_analysis() # This method should use self.config as needed

        # --- Export Data ---
        if config.output_path: # Only export if output_path is given
            if not processor.export_data(config.output_path):
                sys.exit(1)
        else:
            if initial_data_loaded or config.filters or config.analysis_type or config.left_score:
                 Logger.info("处理完成，但未指定输出文件路径 (--output)，结果未保存到文件。")
            # If only stock management or CSV processing was done, this message might not be relevant.

        Logger.info("CLI模式处理完成。")

    elif config.mode == 'gui':
        Logger.info("GUI模式启动 (当前版本中可能未完全集成或已分离)。")
        # Example:
        # from src.ui.stock_kline.app import StockKLineApp
        # app = StockKLineApp(config) # Pass config to GUI app
        # app.run()
        pass # Placeholder

    elif config.mode == 'excel':
        Logger.info("Excel查看器模式启动 (当前版本中可能未完全集成或已分离)。")
        # Example:
        # from src.ui.excel_viewer import ExcelViewerApp
        # viewer = ExcelViewerApp(config) # Pass config
        # viewer.launch()
        pass # Placeholder
    else:
        Logger.error(f"未知的运行模式: {config.mode}")
        sys.exit(1)

if __name__ == "__main__":
    main()