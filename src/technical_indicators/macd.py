"""
MACD技术指标计算模块
"""
import pandas as pd
from typing import Dict, Tuple

class MACD:
    """MACD指标计算类"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """
        初始化MACD计算器
        
        Args:
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        Args:
            data: 包含收盘价的DataFrame
            
        Returns:
            Dict[str, pd.Series]: MACD指标数据，包含DIF、DEA和MACD
        """
        close = data['Close']
        
        # 计算快线和慢线的EMA
        exp1 = close.ewm(span=self.fast_period, adjust=False).mean()
        exp2 = close.ewm(span=self.slow_period, adjust=False).mean()
        
        # 计算DIF
        dif = exp1 - exp2
        
        # 计算DEA
        dea = dif.ewm(span=self.signal_period, adjust=False).mean()
        
        # 计算MACD柱状
        macd = (dif - dea) * 2
        
        return {
            'dif': dif,
            'dea': dea,
            'macd': macd
        }
        
    def get_signals(self, data: pd.DataFrame) -> Dict[str, bool]:
        """
        获取MACD交易信号
        
        Args:
            data: 股票数据
            
        Returns:
            Dict[str, bool]: 交易信号
        """
        result = self.calculate(data)
        dif = result['dif']
        dea = result['dea']
        
        # 获取最新的值
        current_dif = dif.iloc[-1]
        current_dea = dea.iloc[-1]
        prev_dif = dif.iloc[-2]
        prev_dea = dea.iloc[-2]
        
        # 判断金叉和死叉
        golden_cross = prev_dif <= prev_dea and current_dif > current_dea
        death_cross = prev_dif >= prev_dea and current_dif < current_dea
        
        return {
            'golden_cross': golden_cross,  # 金叉信号
            'death_cross': death_cross,    # 死叉信号
            'above_signal': current_dif > current_dea  # DIF在DEA上方
        } 