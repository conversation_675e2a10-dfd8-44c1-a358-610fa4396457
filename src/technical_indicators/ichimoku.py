"""
一目均衡表技术指标计算模块
"""
import pandas as pd
from typing import Dict

class Ichimoku:
    """一目均衡表指标计算类"""
    
    def __init__(self, 
                 tenkan_period: int = 9,
                 kijun_period: int = 26,
                 senkou_b_period: int = 52,
                 displacement: int = 26):
        """
        初始化一目均衡表计算器
        
        Args:
            tenkan_period: 转换线周期
            kijun_period: 基准线周期
            senkou_b_period: 先行带B周期
            displacement: 延迟周期
        """
        self.tenkan_period = tenkan_period
        self.kijun_period = kijun_period
        self.senkou_b_period = senkou_b_period
        self.displacement = displacement
        
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        计算一目均衡表指标
        
        Args:
            data: 包含OHLC数据的DataFrame
            
        Returns:
            Dict[str, pd.Series]: 一目均衡表指标数据
        """
        # 计算转换线（Tenkan-sen）
        tenkan_sen = self._calculate_midpoint(data, self.tenkan_period)
        
        # 计算基准线（Kijun-sen）
        kijun_sen = self._calculate_midpoint(data, self.kijun_period)
        
        # 计算先行带A（Senkou Span A）
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(self.displacement)
        
        # 计算先行带B（Senkou Span B）
        senkou_span_b = self._calculate_midpoint(data, self.senkou_b_period).shift(self.displacement)
        
        # 计算延迟线（Chikou Span）
        chikou_span = data['Close'].shift(-self.displacement)
        
        return {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_span_a': senkou_span_a,
            'senkou_span_b': senkou_span_b,
            'chikou_span': chikou_span
        }
        
    def _calculate_midpoint(self, data: pd.DataFrame, period: int) -> pd.Series:
        """计算区间中点"""
        high = data['High'].rolling(window=period).max()
        low = data['Low'].rolling(window=period).min()
        return (high + low) / 2
        
    def get_signals(self, data: pd.DataFrame) -> Dict[str, bool]:
        """
        获取一目均衡表交易信号
        
        Args:
            data: 股票数据
            
        Returns:
            Dict[str, bool]: 交易信号
        """
        ichimoku = self.calculate(data)
        
        # 获取最新的值
        current_price = data['Close'].iloc[-1]
        current_tenkan = ichimoku['tenkan_sen'].iloc[-1]
        current_kijun = ichimoku['kijun_sen'].iloc[-1]
        current_span_a = ichimoku['senkou_span_a'].iloc[-1]
        current_span_b = ichimoku['senkou_span_b'].iloc[-1]
        
        # 判断云层位置
        above_cloud = (current_price > current_span_a and 
                      current_price > current_span_b)
        below_cloud = (current_price < current_span_a and 
                      current_price < current_span_b)
        in_cloud = not (above_cloud or below_cloud)
        
        # 判断转换线和基准线的关系
        tenkan_above_kijun = current_tenkan > current_kijun
        
        return {
            'above_cloud': above_cloud,
            'below_cloud': below_cloud,
            'in_cloud': in_cloud,
            'tenkan_above_kijun': tenkan_above_kijun
        }

