"""
阻力位和斐波那契回撤分析模块
"""
import numpy as np
import pandas as pd
from scipy.signal import find_peaks
from scipy.stats import gaussian_kde

def calculate_fibonacci_levels(data):
    """
    计算斐波那契回撤位
    
    参数:
    data: 价格数据DataFrame
    
    返回:
    包含各回撤位的字典
    """
    max_price = data['High'].max()
    min_price = data['Low'].min()

    diff = max_price - min_price
    levels = [23.6, 38.2, 50.0, 61.8, 78.6]

    fib_levels = {level: max_price - diff * (level / 100) for level in levels}
    return fib_levels

def _is_psychological_level(price):
    """
    识别心理价位
    
    参数:
    price: 价格
    
    返回:
    (bool, str)，是否为心理价位及其描述
    """
    # 转换为字符串以便分析
    price_str = str(price)
    
    # 检查是否为整数
    if price_str.endswith('.0') or price_str.endswith('.00'):
        return True, "整数心理价位"
    
    # 检查是否为半整数
    if price_str.endswith('.5') or price_str.endswith('.50'):
        return True, "半整数心理价位"
    
    # 检查是否为重要整数的倍数
    if price > 0:
        # 对于高价股票的100的倍数
        if price >= 100 and abs(price % 100) < 0.5:
            return True, "百位整数价位"
        
        # 对于中等价格的10的倍数
        if price >= 10 and abs(price % 10) < 0.1:
            return True, "十位整数价位"
        
        # 对于低价股票的1的倍数
        if price < 10 and abs(price % 1) < 0.01:
            return True, "个位整数价位"
    
    return False, ""

def _analyze_volume_at_price(data, price, tolerance=0.005):
    """
    分析特定价格附近的成交量情况
    
    参数:
    data: 股票历史数据
    price: 要分析的价格点
    tolerance: 价格容忍度百分比
    
    返回:
    dict: 包含成交量分析结果的字典
    """
    # 如果数据中没有成交量列，返回默认值
    if 'Volume' not in data.columns:
        return {
            'volume_ratio': 1.0,
            'is_high_volume': False,
            'is_low_volume': False,
            'description': '无成交量数据'
        }
    
    # 定义价格区间
    price_range = (data['High'] >= price * (1-tolerance)) & (data['High'] <= price * (1+tolerance))
    if not price_range.any():
        return {
            'volume_ratio': 1.0,
            'is_high_volume': False,
            'is_low_volume': False,
            'description': '该价格区间无历史数据'
        }
    
    # 计算该价格区间的平均成交量
    zone_volume = data.loc[price_range, 'Volume'].mean()
    
    # 计算整体平均成交量
    overall_volume = data['Volume'].mean()
    
    # 计算成交量比率
    volume_ratio = zone_volume / overall_volume if overall_volume > 0 else 1.0
    
    # 判断是否为高成交量或低成交量
    is_high_volume = volume_ratio > 1.5
    is_low_volume = volume_ratio < 0.5
    
    # 生成描述文本
    if is_high_volume:
        description = f"高成交量区域(成交量是平均的{volume_ratio:.1f}倍)"
    elif is_low_volume:
        description = f"低成交量区域(成交量是平均的{volume_ratio:.1f}倍)"
    else:
        description = f"普通成交量区域(成交量是平均的{volume_ratio:.1f}倍)"
    
    # 增强触及次数分析
    touch_count = price_range.sum()
    
    # 计算最近一次触及的时间
    if touch_count > 0:
        recent_touches = data.index[price_range]
        last_touch_date = recent_touches[-1]
        try:
            days_since_touch = (data.index[-1] - last_touch_date).days
        except:
            days_since_touch = 0  # 如果日期计算失败，使用默认值
        
        # 根据最近触及时间调整描述
        recency_desc = ""
        if days_since_touch <= 7:
            recency_desc = "，近期触及"
        elif days_since_touch <= 30:
            recency_desc = "，最近一个月内触及"
        
        # 分析触及后的价格行为
        bounce_count = 0
        for touch_date in recent_touches:
            touch_idx = data.index.get_loc(touch_date)
            if touch_idx + 5 < len(data):  # 确保有足够的后续数据
                # 检查触及后是否出现明显下跌
                post_touch = data.iloc[touch_idx:touch_idx+5]
                if (post_touch['Low'].min() < price * 0.99):
                    bounce_count += 1
        
        bounce_ratio = bounce_count / touch_count if touch_count > 0 else 0
        if bounce_ratio > 0.5:
            recency_desc += "，多次形成反转"
    
    # 添加到描述中
    if touch_count > 5:
        description += f"，多次触及({touch_count}次){recency_desc}"
    elif touch_count > 2:
        description += f"，触及{touch_count}次{recency_desc}"
    
    return {
        'volume_ratio': volume_ratio,
        'is_high_volume': is_high_volume,
        'is_low_volume': is_low_volume,
        'touch_count': touch_count,
        'description': description
    }

def calculate_resistance_zones(data, current_price):
    """
    计算股票的阻力区 (整合心理价位和触及次数分析)
    
    参数:
    data: 股票历史数据
    current_price: 当前价格
    
    返回:
    阻力区列表
    """
    resistance_zones = []

    # 只考虑当前价格以上的高点
    highs = data[data['High'] > current_price]['High']

    if highs.empty:
        # 没有高于当前价格的数据，返回历史最高点
        if not data.empty:
            highest = data['High'].max()
            # 检查是否为心理价位
            is_psych, psych_desc = _is_psychological_level(highest)
            description = '历史最高点'
            if is_psych:
                description += f"，{psych_desc}"
                
            resistance_zones.append({
                'price': highest,
                'strength': '历史最高',
                'volume_ratio': 1.0,
                'is_psychological': is_psych,
                'description': description
            })
        return resistance_zones

    # 使用KDE (核密度估计) 找出价格密集区域
    try:
        # 检查高点数据是否足够进行KDE分析
        if len(highs) < 2:
            # 如果只有一个高点，直接使用它作为阻力位
            price = highs.iloc[0]
            volume_info = _analyze_volume_at_price(data, price)
            
            # 检查是否为心理价位
            is_psych, psych_desc = _is_psychological_level(price)
            if is_psych:
                volume_info['description'] += f"，{psych_desc}"
                strength = '极强' if volume_info['is_high_volume'] else '强'
            else:
                strength = '强' if volume_info['is_high_volume'] else '中'
                
            resistance_zones.append({
                'price': price,
                'strength': strength,
                'volume_ratio': volume_info['volume_ratio'],
                'is_psychological': is_psych,
                'description': volume_info['description']
            })
            return resistance_zones
            
        # 检查数据是否有足够的变异性
        if len(highs.unique()) < 2 or highs.std() < 1e-6:
            # 数据变异性不足，使用备用方法
            raise ValueError("数据变异性不足，无法使用KDE")
            
        # 创建价格范围
        price_range = np.linspace(
            current_price, data['High'].max() * 1.05, 1000)

        try:
            # 计算KDE
            kde = gaussian_kde(highs)
            density = kde(price_range)
            
            # 找出密度峰值
            peaks, _ = find_peaks(density, height=0, distance=len(price_range)//20)

            # 按密度值排序
            peak_densities = [(price_range[peak], density[peak]) for peak in peaks]
            peak_densities.sort(key=lambda x: x[1], reverse=True)

            # 距离当前价格20%以内的阻力位
            valid_peaks = [p for p in peak_densities if 0 <
                        (p[0] - current_price)/current_price <= 0.2]

            if not valid_peaks and peak_densities:
                # 没有符合条件的，选择最接近的一个
                valid_peaks = [
                    min(peak_densities, key=lambda x: abs(x[0]-current_price))]

            # 取前3个最强的阻力区
            for i, (price, density_value) in enumerate(valid_peaks[:3]):
                # 分析该价格附近的成交量和触及次数
                volume_info = _analyze_volume_at_price(data, price)
                
                # 检查是否为心理价位
                is_psych, psych_desc = _is_psychological_level(price)
                if is_psych:
                    volume_info['description'] += f"，{psych_desc}"
                
                # 基础强度
                base_strength = '强' if i == 0 else '中' if i == 1 else '弱'
                
                # 根据成交量、密度和心理价位调整强度
                if volume_info['is_high_volume'] and is_psych:
                    # 高成交量 + 心理价位 = 极强阻力
                    strength = '极强'
                    description = f"{volume_info['description']}，价格密集区"
                elif volume_info['is_high_volume']:
                    # 高成交量 = 提升一级强度
                    strength = '极强' if base_strength == '强' else '强'
                    description = f"{volume_info['description']}，价格密集区"
                elif is_psych and base_strength != '弱':
                    # 心理价位 = 提升一级强度（除非基础强度为弱）
                    strength = '强' if base_strength == '中' else base_strength
                    description = f"{volume_info['description']}，价格密集区"
                else:
                    strength = base_strength
                    description = f"{volume_info['description']}，价格密集区"
                
                # 如果触及次数大于5且有反转，进一步增强强度
                if volume_info.get('touch_count', 0) > 5 and '多次形成反转' in volume_info['description']:
                    if strength != '极强':
                        strength = '强'  # 至少为强
                
                resistance_zones.append({
                    'price': price,
                    'strength': strength,
                    'volume_ratio': volume_info['volume_ratio'],
                    'density': density_value,
                    'is_psychological': is_psych,
                    'description': description
                })
        except Exception as e:
            # KDE计算失败，使用备用方法
            print(f"KDE计算失败，使用备用方法: {str(e)}")
            raise ValueError(f"KDE计算失败: {str(e)}")

    except (ImportError, ValueError) as e:
        print(f"使用备用阻力计算方案: {e}")

        # 使用备用方案：局部高点法
        window = min(10, max(3, len(highs) // 5))
        try:
            peaks_idx, _ = find_peaks(highs, distance=window)
            local_highs = highs.iloc[peaks_idx]
        except (ImportError, ValueError):
            # 若无scipy，回退到简单的滑动窗口法
            rolling_max = highs.rolling(window=window, center=True).max()
            local_highs = highs[highs == rolling_max].dropna()

        # 如果没有找到局部高点，使用简单的分位数方法
        if len(local_highs) == 0:
            # 使用分位数方法
            quantiles = [0.9, 0.75, 0.5]
            for i, q in enumerate(quantiles):
                if len(highs) > 0:
                    price = highs.quantile(q)
                    # 分析该价格附近的成交量
                    volume_info = _analyze_volume_at_price(data, price)
                    
                    # 检查是否为心理价位
                    is_psych, psych_desc = _is_psychological_level(price)
                    if is_psych:
                        volume_info['description'] += f"，{psych_desc}"
                    
                    base_strength = '强' if i == 0 else '中' if i == 1 else '弱'
                    if volume_info['is_high_volume'] and is_psych:
                        strength = '极强'
                    elif volume_info['is_high_volume'] or is_psych:
                        strength = '强'
                    else:
                        strength = base_strength
                        
                    resistance_zones.append({
                        'price': price,
                        'strength': strength,
                        'volume_ratio': volume_info['volume_ratio'],
                        'is_psychological': is_psych,
                        'description': volume_info['description']
                    })
            return resistance_zones

        # 按距离当前价格近的顺序排序
        sorted_highs = sorted(local_highs.tolist(),
                            key=lambda p: abs(p - current_price))

        valid_highs = [p for p in sorted_highs if 0 <
                    (p - current_price)/current_price <= 0.2]

        if not valid_highs and sorted_highs:
            valid_highs = [sorted_highs[0]]

        for i, price in enumerate(valid_highs[:3]):
            # 分析该价格附近的成交量
            volume_info = _analyze_volume_at_price(data, price)
            
            # 检查是否为心理价位
            is_psych, psych_desc = _is_psychological_level(price)
            if is_psych:
                volume_info['description'] += f"，{psych_desc}"
            
            base_strength = '强' if i == 0 else '中' if i == 1 else '弱'
            if volume_info['is_high_volume'] and is_psych:
                strength = '极强'
            elif volume_info['is_high_volume'] or (is_psych and base_strength != '弱'):
                strength = '强'
            else:
                strength = base_strength
                
            resistance_zones.append({
                'price': price,
                'strength': strength,
                'volume_ratio': volume_info['volume_ratio'],
                'is_psychological': is_psych,
                'description': volume_info['description']
            })

    # 阻力位去重合并函数
    def merge_similar_zones(zones, price_tolerance=0.005):
        merged = []
        zones.sort(key=lambda x: x['price'])
        for zone in zones:
            if not merged:
                merged.append(zone)
            else:
                last_zone = merged[-1]
                # 如果价格差距小于0.5%，则合并
                if abs(zone['price'] - last_zone['price']) / last_zone['price'] < price_tolerance:
                    # 保留强度更高的阻力
                    strength_order = {'极强': 4, '强': 3, '中': 2, '弱': 1, '历史最高': 5}
                    if strength_order.get(zone['strength'], 0) > strength_order.get(last_zone['strength'], 0):
                        merged[-1] = zone
                    # 如果强度相同但成交量比率更高，也替换
                    elif (strength_order.get(zone['strength'], 0) == strength_order.get(last_zone['strength'], 0) and
                          zone.get('volume_ratio', 1.0) > last_zone.get('volume_ratio', 1.0)):
                        merged[-1] = zone
                    # 如果强度和成交量相似，但一个有更多的触及次数，保留触及次数更多的
                    elif (strength_order.get(zone['strength'], 0) == strength_order.get(last_zone['strength'], 0) and
                          abs(zone.get('volume_ratio', 1.0) - last_zone.get('volume_ratio', 1.0)) < 0.1 and
                          zone.get('touch_count', 0) > last_zone.get('touch_count', 0) * 1.5):
                        merged[-1] = zone
                else:
                    merged.append(zone)
        return merged

    # 最终合并去重后的阻力区
    resistance_zones = merge_similar_zones(resistance_zones)

    return resistance_zones

def plot_resistance_zones_with_fibonacci(ax, data, resistance_zones, fib_levels, divergences=None, lookback=120):
    """
    绘制阻力区和斐波那契水平
    
    参数:
    ax: 图表坐标轴
    data: 股票数据
    resistance_zones: 阻力区列表
    fib_levels: 斐波那契水平字典
    divergences: MACD背离点列表，可选
    lookback: 回溯的数据点数量，用于MACD背离
    """
    current_price = data['Close'].iloc[-1]
    
    # 绘制斐波那契回撤位 - 使用虚线和特殊样式
    for level, price in fib_levels.items():
        ax.axhline(y=price, color='#FF9800', linestyle=':', alpha=0.6, linewidth=1.0)
        ax.text(len(data)*0.01, price, f"{level}% ({price:.2f})",
                color='#FF9800', fontsize=7, verticalalignment='bottom',
                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

    # 绘制KDE阻力区 - 根据成交量和强度调整样式
    for zone in resistance_zones:
        # 根据强度选择颜色 - 使用更加突出的颜色
        if zone['strength'] == '极强':
            color = '#E91E63'  # 鲜艳的粉红色
            linewidth = 2.0
            alpha = 0.85
            linestyle = 'solid'
        elif zone['strength'] == '强':
            color = '#FF5722'  # 明亮的橙红色
            linewidth = 1.8
            alpha = 0.8
            linestyle = 'solid'
        elif zone['strength'] == '历史最高':
            color = '#9C27B0'  # 紫色
            linewidth = 2.0
            alpha = 0.85
            linestyle = 'solid'
        else:
            color = '#FF9800'  # 橙色
            linewidth = 1.5
            alpha = 0.75
            linestyle = 'solid'
        
        # 如果是心理价位，使用特殊线型
        if zone.get('is_psychological', False):
            linestyle = (0, (5, 2, 1, 2))  # 点划线变体
            
        # 绘制阻力线 - 使用简单的实线，移除可能不兼容的阴影效果
        ax.axhline(y=zone['price'], color=color, linestyle=linestyle, alpha=alpha, linewidth=linewidth)
        
        # 模拟阴影效果 - 通过绘制稍微偏移和更淡的二次线条实现
        ax.axhline(y=zone['price']-0.5, color='gray', linestyle=linestyle, alpha=0.2, linewidth=linewidth)
        
        # 准备显示文本
        strength_text = zone['strength']
        if 'volume_ratio' in zone and zone['volume_ratio'] > 1.5:
            strength_text += "(高成交量)"
        if zone.get('is_psychological', False):
            strength_text += "(心理价位)"
        
        # 显示阻力位文本 - 添加更明显的背景框
        ax.text(len(data)*0.95, zone['price']*1.002,
                f"阻力: {zone['price']:.2f} ({strength_text})",
                color=color, fontsize=8, verticalalignment='bottom',
                horizontalalignment='right', fontweight='bold',
                bbox=dict(facecolor='white', alpha=0.8, edgecolor=color, boxstyle='round,pad=0.3'))
        
        # 如果有描述信息，添加到图表上
        if 'description' in zone:
            # 缩短描述文本，避免过长
            desc = zone['description']
            if len(desc) > 60:
                # 保留最重要的信息
                parts = desc.split('，')
                if len(parts) > 2:
                    desc = '，'.join(parts[:2]) + '...'
                
            ax.text(len(data)*0.5, zone['price']*0.998,
                    desc,
                    color=color, fontsize=7, alpha=0.9,
                    horizontalalignment='center', verticalalignment='top',
                    bbox=dict(facecolor='white', alpha=0.7, edgecolor=color, pad=1))

    # 检查并标记汇合区 (±0.5%内) - 使用更加突出的样式
    for fib_price in fib_levels.values():
        for zone in resistance_zones:
            if abs(zone['price'] - fib_price) / fib_price <= 0.005:
                # 汇合点突出标记 - 使用渐变效果
                gradient = np.linspace(0, 0.3, 30)
                for i, alpha in enumerate(gradient):
                    height = 0.0025 * (i/10 + 1)
                    ax.axhspan(zone['price']*(1-height), zone['price']*(1+height),
                            color='#E91E63', alpha=alpha)
                
                # 准备汇合区文本
                confluence_text = f"汇合区: {zone['price']:.2f}"
                if zone.get('is_psychological', False):
                    confluence_text += " (心理价位)"
                
                ax.text(len(data)*0.5, zone['price'],
                        confluence_text,
                        color='#E91E63', fontsize=10, fontweight='bold',
                        horizontalalignment='center', verticalalignment='center',
                        bbox=dict(facecolor='#FFF176', alpha=0.7, edgecolor='#E91E63', 
                                 boxstyle='round,pad=0.4'))
            
    # 在图上绘制背离标记
    if divergences:
        for div in divergences:
            idx = div['index']
            # 由于我们现在使用的是整数索引位置，需要确保它在有效范围内
            if idx < 0 or idx >= lookback:
                continue
                
            # 获取实际数据点的位置（在绘图中的x坐标）
            x_pos = len(data) - lookback + idx
            if x_pos < 0 or x_pos >= len(data):
                continue
                
            price = data['High'].iloc[x_pos] if div['type'] == '顶背离' else data['Low'].iloc[x_pos]
            offset = (ax.get_ylim()[1] - ax.get_ylim()[0]) * 0.02
            y_pos = price + offset if div['type']=='顶背离' else price - offset
            
            div_color = '#F44336' if div['type']=='顶背离' else '#4CAF50'
            
            ax.annotate(div['type'], xy=(x_pos, price), 
                        xytext=(x_pos, y_pos),
                        arrowprops=dict(facecolor=div_color, shrink=0.05, width=2),
                        fontsize=9, color=div_color, fontweight='bold',
                        horizontalalignment='center',
                        verticalalignment='bottom' if div['type']=='顶背离' else 'top',
                        bbox=dict(facecolor='white', alpha=0.8, edgecolor=div_color, boxstyle='round,pad=0.2'))
        
    # 当前价格标记 - 使用更明显的蓝色线，移除不兼容的阴影效果
    ax.axhline(y=current_price, color='#2196F3', linestyle='-', alpha=0.8, linewidth=1.5)
    # 添加淡色辅助线模拟阴影
    ax.axhline(y=current_price-0.5, color='gray', linestyle='-', alpha=0.2, linewidth=1.5)
    
    ax.text(len(data)*0.95, current_price*0.998, f"当前: {current_price:.2f}",
            color='#2196F3', fontsize=9, fontweight='bold', verticalalignment='top',
            horizontalalignment='right', 
            bbox=dict(facecolor='white', alpha=0.8, edgecolor='#2196F3', boxstyle='round,pad=0.2')) 