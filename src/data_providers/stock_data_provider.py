"""
股票数据提供者模块
提供不同数据源的股票数据获取接口
"""
import abc
import pandas as pd
import yfinance as yf
import re
import time
import requests
import json
from datetime import datetime, timedelta
import threading
from ..utils.logger import Logger, DataSourceError, DataNotFoundError, InvalidStockCodeError, APIRequestError


class RateLimiter:
    """请求频率限制器"""
    def __init__(self, max_requests=3, time_window=1):
        self.max_requests = max_requests  # 时间窗口内最大请求数
        self.time_window = time_window    # 时间窗口（秒）
        self.requests = []
        self.lock = threading.Lock()

    def acquire(self):
        """获取请求许可"""
        with self.lock:
            now = time.time()
            
            # 清理过期的请求记录
            self.requests = [req for req in self.requests if now - req < self.time_window]
            
            if len(self.requests) >= self.max_requests:
                # 计算需要等待的时间
                wait_time = self.requests[0] + self.time_window - now
                if wait_time > 0:
                    time.sleep(wait_time)
                # 重新清理过期请求记录
                now = time.time()
                self.requests = [req for req in self.requests if now - req < self.time_window]
            
            self.requests.append(now)

def rate_limit(max_requests=3, time_window=1):
    """请求频率限制装饰器"""
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            # 优先使用实例的速率限制器（如果存在）
            limiter = getattr(self, 'rate_limiter', None)
            if limiter is None:
                # 如果实例没有速率限制器，创建一个新的
                limiter = RateLimiter(max_requests, time_window)
            
            limiter.acquire()
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

class StockDataProvider(abc.ABC):
    """股票数据提供者基类"""
    
    @abc.abstractmethod
    def get_stock_data(self, stock_code, period="3mo", interval="1d"):
        """
        获取股票数据
        
        参数:
        stock_code: 股票代码
        period: 时间周期
        interval: 时间间隔
        
        返回:
        包含股票数据的DataFrame
        """
        pass
    
    @abc.abstractmethod
    def get_stock_info(self, stock_code):
        """
        获取股票基本信息
        
        参数:
        stock_code: 股票代码
        
        返回:
        包含股票信息的字典
        """
        pass

    @abc.abstractmethod
    def get_realtime_data(self, stock_code):
        """
        获取股票实时数据

        参数:
        stock_code: 股票代码

        返回:
        包含股票实时信息的字典
        """
        pass
    
    @abc.abstractmethod
    def format_stock_code(self, stock_code):
        """
        格式化股票代码
        
        参数:
        stock_code: 原始股票代码
        
        返回:
        格式化后的股票代码
        """
        pass

    @abc.abstractmethod
    def get_stock_data_by_date(self, stock_code, start_date, end_date):
        """
        根据指定的开始和结束日期获取股票历史数据。

        参数:
        stock_code: 股票代码
        start_date: 开始日期 (格式: 'YYYY-MM-DD' 或 'YYYYMMDD')
        end_date: 结束日期 (格式: 'YYYY-MM-DD' 或 'YYYYMMDD')

        返回:
        包含股票数据的DataFrame
        """
        pass

    def _convert_period_to_days(self, period):
        """将周期字符串转换为天数"""
        period = period.lower()
        # 解析数字部分
        number = int(''.join(filter(str.isdigit, period)))
        # 解析单位部分
        unit = ''.join(filter(str.isalpha, period))
        
        # 转换为天数
        if unit == 'd':  # 天
            return number
        elif unit == 'mo':  # 月
            return number * 30
        elif unit == 'y':  # 年
            return number * 365
        elif unit == 'w':  # 周
            return number * 7
        elif unit == 'h':  # 小时
            return 1  # 不足一天返回1天
        elif unit == 'm':  # 分钟
            return 1  # 不足一天返回1天
        else:
            raise ValueError(f"不支持的周期单位: {unit}")

class YahooFinanceProvider(StockDataProvider):
    """Yahoo Finance数据提供者"""
    
    def __init__(self):
        """初始化Yahoo Finance数据提供者"""
        self.rate_limiter = RateLimiter(max_requests=3, time_window=1)

    def format_stock_code(self, stock_code):
        """格式化股票代码为Yahoo Finance支持的格式"""
        try:
            code = stock_code.strip().upper()
            
            # 处理中国股票代码
            if re.match(r'^\d{6}$', code):
                # 判断股票交易所
                if code.startswith(('0', '3')):  # 深交所
                    return f"{code}.SZ"
                elif code.startswith(('6', '9')):  # 上交所
                    return f"{code}.SS"
                else:
                    return code
            
            # 处理港股代码
            if re.match(r'^\d{4}$', code) and not code.startswith(('0', '3', '6', '9')):
                return f"{code}.HK"
            
            # 其他情况直接返回代码
            return code
        except Exception as e:
            Logger.error(f"格式化股票代码失败: {stock_code}", exc_info=True)
            raise InvalidStockCodeError(f"无效的股票代码格式: {stock_code}") from e
    
    @rate_limit(max_requests=3, time_window=1)
    def get_stock_info(self, stock_code):
        """获取股票基本信息"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从Yahoo Finance获取股票信息: {formatted_code}")
            
            stock = yf.Ticker(formatted_code)
            info = stock.info
            
            if not info:
                Logger.warning(f"未能获取到{formatted_code}的信息")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的基本信息")
                
            return info
        except Exception as e:
            Logger.error(f"获取股票信息时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Yahoo Finance获取股票信息失败: {str(e)}") from e

    def get_realtime_data(self, stock_code):
        """Yahoo Finance 不直接提供标准的实时数据接口"""
        Logger.warning("Yahoo Finance不支持实时数据获取")
        raise NotImplementedError("Yahoo Finance provider does not support real-time data fetching in this format.")

    @rate_limit(max_requests=3, time_window=1)
    def get_stock_data(self, stock_code, period="3mo", interval="1d"):
        """从Yahoo Finance获取股票数据"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从Yahoo Finance获取股票数据: {formatted_code}, period={period}, interval={interval}")
            
            stock = yf.Ticker(formatted_code)
            df = stock.history(period=period, interval=interval)
            
            if df.empty:
                Logger.warning(f"未能获取到{formatted_code}的数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的数据")
            
            # 确保列名符合标准格式
            df = df.rename(columns={
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume',
                'Dividends': 'Dividends',
                'Stock Splits': 'Stock_Splits'
            })
            
            return df
            
        except Exception as e:
            Logger.error(f"从Yahoo Finance获取数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Yahoo Finance获取数据失败: {str(e)}") from e

    @rate_limit(max_requests=3, time_window=1)
    def get_stock_data_by_date(self, stock_code, start_date, end_date):
        """根据指定的开始和结束日期获取股票历史数据"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从Yahoo Finance获取股票历史数据: {formatted_code}, 从 {start_date} 到 {end_date}")
            
            # 转换日期格式
            start = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            stock = yf.Ticker(formatted_code)
            df = stock.history(start=start, end=end)
            
            if df.empty:
                Logger.warning(f"未找到股票 {formatted_code} 在指定日期范围的数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 在指定日期范围的数据")
            
            # 确保列名符合标准格式
            df = df.rename(columns={
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume',
                'Dividends': 'Dividends',
                'Stock Splits': 'Stock_Splits'
            })
            
            # 确保数据类型正确
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            
            # 按日期排序
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            Logger.error(f"从Yahoo Finance获取历史数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Yahoo Finance获取历史数据失败: {str(e)}") from e

class SinaFinanceProvider(StockDataProvider):
    """新浪财经数据提供者"""
    
    def __init__(self):
        """初始化新浪财经数据提供者"""
        self.rate_limiter = RateLimiter(max_requests=3, time_window=1)

    def format_stock_code(self, stock_code):
        """格式化股票代码为新浪财经支持的格式"""
        try:
            code = stock_code.strip().upper()
            
            # 处理中国股票代码
            if re.match(r'^\d{6}$', code):
                # 判断股票交易所
                if code.startswith(('0', '3')):  # 深交所
                    return f"sz{code}"
                elif code.startswith(('6', '9')):  # 上交所
                    return f"sh{code}"
                else:
                    return code
            
            # 如果已经包含交易所信息
            if code.endswith(('.SH', '.SZ')):
                exchange = 'sh' if code.endswith('.SH') else 'sz'
                code = code[:-3]
                return f"{exchange}{code}"
                
            raise InvalidStockCodeError(f"不支持的股票代码格式: {stock_code}")
        except Exception as e:
            Logger.error(f"格式化股票代码失败: {stock_code}", exc_info=True)
            raise InvalidStockCodeError(f"无效的股票代码格式: {stock_code}") from e
    
    @rate_limit(max_requests=3, time_window=1)
    def get_stock_data_by_date(self, stock_code, start_date, end_date):
        """根据日期范围获取股票数据"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从新浪财经获取股票历史数据: {formatted_code}, 从 {start_date} 到 {end_date}")
            
            # 确保日期格式正确
            start_date = pd.to_datetime(start_date)
            end_date = pd.to_datetime(end_date)
            
            # 计算需要的天数（包括非交易日）
            days = (end_date - start_date).days + 1
            # 考虑到节假日等因素，多获取一些数据
            days = int(days * 1.5)
            
            # 构建API URL
            url = f"https://quotes.sina.cn/cn/api/jsonp_v2.php/var%20_{formatted_code}_day=/CN_MarketData.getKLineData"
            params = {
                'symbol': formatted_code,
                'scale': 240,
                'ma': 'no',
                'datalen': days
            }
            
            # 发送请求
            response = requests.get(url, params=params)
            if response.status_code != 200:
                Logger.error(f"新浪财经API请求失败: {response.status_code}")
                raise APIRequestError(f"API请求失败，状态码: {response.status_code}")
            
            # 解析JSONP响应
            text = response.text
            json_str = text[text.find("(")+1:text.rfind(")")]
            data_list = json.loads(json_str)
            
            if not data_list:
                Logger.warning(f"未能获取到{formatted_code}的数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的数据")
            
            # 创建DataFrame
            df = pd.DataFrame(data_list)
            df.rename(columns={
                'day': 'Date',
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }, inplace=True)
            
            # 转换日期和数值列
            df['Date'] = pd.to_datetime(df['Date'])
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            
            # 设置索引并按日期升序排序
            df.set_index('Date', inplace=True)
            df.sort_index(inplace=True)
            
            # 过滤指定日期范围的数据
            df = df.loc[start_date:end_date]
            
            if df.empty:
                Logger.warning(f"过滤后未找到{formatted_code}在指定日期范围的数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 在指定日期范围的数据")
            
            return df
            
        except Exception as e:
            Logger.error(f"从新浪财经获取历史数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从新浪财经获取历史数据失败: {str(e)}") from e

    @rate_limit(max_requests=10, time_window=1)
    def get_realtime_data(self, stock_code):
        """从新浪财经获取实时股票数据"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从新浪财经获取实时数据: {formatted_code}")
            
            # 构建API URL
            url = f"https://hq.sinajs.cn/list={formatted_code}"
            
            # 设置请求头，防止被识别为爬虫
            headers = {
                'Referer': 'https://finance.sina.com.cn',
                'User-Agent': 'Mozilla/5.0'
            }
            
            # 发送请求
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                Logger.error(f"新浪财经实时API请求失败: {response.status_code}")
                raise APIRequestError(f"实时API请求失败，状态码: {response.status_code}")
            
            response.encoding = response.apparent_encoding  # 自动检测编码
            text = response.text
            
            if "FAILED" in text or "=" not in text or '""' in text:
                Logger.warning(f"未能获取到{formatted_code}的实时数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的实时数据")
            
            # 提取数据部分
            data_str = text.split('="')[1].split('";')[0]
            data = data_str.split(',')
            
            if len(data) < 32:  # 基本数据至少需要这么多字段
                Logger.error(f"实时数据格式不正确: {formatted_code}")
                raise DataSourceError(f"实时数据格式不正确: {formatted_code}")
            
            # 组装实时数据字典
            realtime_info = {
                'name': data[0],             # 股票名称
                'open': float(data[1]),      # 今日开盘价
                'prev_close': float(data[2]),# 昨日收盘价
                'current_price': float(data[3]), # 当前价格
                'high': float(data[4]),      # 今日最高价
                'low': float(data[5]),       # 今日最低价
                'bid_price': float(data[6]), # 竞买价（买一）
                'ask_price': float(data[7]), # 竞卖价（卖一）
                'volume': int(data[8]),      # 成交量（股）
                'amount': float(data[9]),    # 成交额（元）
                'bid_vol_1': int(data[10]),  # 买一量
                'bid_price_1': float(data[11]),# 买一价
                'bid_vol_2': int(data[12]),
                'bid_price_2': float(data[13]),
                'bid_vol_3': int(data[14]),
                'bid_price_3': float(data[15]),
                'bid_vol_4': int(data[16]),
                'bid_price_4': float(data[17]),
                'bid_vol_5': int(data[18]),
                'bid_price_5': float(data[19]),
                'ask_vol_1': int(data[20]),  # 卖一量
                'ask_price_1': float(data[21]),# 卖一价
                'ask_vol_2': int(data[22]),
                'ask_price_2': float(data[23]),
                'ask_vol_3': int(data[24]),
                'ask_price_3': float(data[25]),
                'ask_vol_4': int(data[26]),
                'ask_price_4': float(data[27]),
                'ask_vol_5': int(data[28]),
                'ask_price_5': float(data[29]),
                'date': data[30],            # 日期
                'time': data[31],            # 时间
                'status': data[32]           # 状态信息 (例如: "03" 表示停牌)
            }
            return realtime_info
            
        except requests.exceptions.RequestException as e:
            Logger.error(f"请求新浪实时接口时出错: {str(e)}", exc_info=True)
            raise APIRequestError(f"请求新浪实时接口失败: {str(e)}") from e
        except (ValueError, IndexError) as e:
            Logger.error(f"解析新浪实时数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"解析新浪实时数据失败: {str(e)}") from e
        except Exception as e:
            Logger.error(f"获取新浪实时数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"获取新浪实时数据失败: {str(e)}") from e
            
    def get_stock_data(self, stock_code, period="3mo", interval="1d"):
        """从新浪财经获取股票数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            days = self._convert_period_to_days(period)
            start_date = end_date - timedelta(days=days)
            
            # 调用按日期获取数据的方法
            return self.get_stock_data_by_date(
                stock_code, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
        except Exception as e:
            Logger.error(f"从新浪财经获取数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从新浪财经获取数据失败: {str(e)}") from e
    
    @rate_limit(max_requests=3, time_window=1)
    def get_stock_info(self, stock_code):
        """获取股票基本信息"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从新浪财经获取股票信息: {formatted_code}")
            
            # 构建API URL
            url = f"https://hq.sinajs.cn/list={formatted_code}"
            headers = {
                'Referer': 'https://finance.sina.com.cn',
                'User-Agent': 'Mozilla/5.0'
            }
            
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                Logger.error(f"新浪财经API请求失败: {response.status_code}")
                raise APIRequestError(f"API请求失败，状态码: {response.status_code}")
            
            text = response.text
            if "FAILED" in text or "," not in text:
                Logger.warning(f"未能获取到{formatted_code}的信息")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的信息")
            
            data = text.split('="')[1].split('";')[0].split(',')
            
            info = {
                'name': data[0],
                'open': float(data[1]),
                'prev_close': float(data[2]),
                'last_price': float(data[3]),
                'high': float(data[4]),
                'low': float(data[5]),
                'date': data[30],
                'time': data[31],
                'status': data[32] if len(data) > 32 else None
            }
            
            return info
            
        except Exception as e:
            Logger.error(f"获取股票信息时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从新浪财经获取股票信息失败: {str(e)}") from e


class TushareProvider(StockDataProvider):
    """Tushare数据提供者"""
    def __init__(self):
        # 写死 token，使用时直接调用，不需要额外配置
        token = "37981f944a122a7ac2266475734fe31128fb201ffbb911f94f84571f"  # 替换成你的 token
        import tushare as ts
        ts.set_token(token)
        self.pro = ts.pro_api()
        self.ts = ts  # 保存 ts 模块引用
        # ExcelProcessor._get_market_data 会处理 Tushare 的特定频率限制 (100次/分钟)
        # 此处不再需要独立的、固定的 RateLimiter，或者应配置为更宽松的值以避免冲突
        # 为简单起见，暂时移除或将其配置为非常宽松，因为外部已有控制
        # self.rate_limiter = RateLimiter(max_requests=200, time_window=60) # 例如，非常宽松的配置
        self.rate_limiter = None # 或者直接设为 None，因为外部已控制 get_stock_data

    def format_stock_code(self, stock_code):
        """格式化股票代码为Tushare格式"""
        # 移除所有非数字字符
        code = re.sub(r'\D', '', stock_code)
        # 确保代码长度为6位
        if len(code) != 6:
            raise ValueError(f"Invalid stock code: {stock_code}")
        # 添加市场后缀
        if code.startswith('6'):
            return f"{code}.SH"  # 上海市场
        else:
            return f"{code}.SZ"  # 深圳市场

    # 移除此处的 @rate_limit，因为 ExcelProcessor._get_market_data 已经处理了
    def get_stock_data(self, stock_code, period="3mo", interval="1d"):
        """获取股票数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            days = self._convert_period_to_days(period)
            start_date = end_date - timedelta(days=days)
            
            # 调用按日期获取数据的方法
            return self.get_stock_data_by_date(
                stock_code, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
        except Exception as e:
            Logger.error(f"从Tushare获取数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Tushare获取数据失败: {str(e)}") from e

    # 移除此处的 @rate_limit，如果 ExcelProcessor 也通过特定方法调用它并做了频率控制
    # 如果 ExcelProcessor 不直接控制此方法的调用频率，则需要保留或调整此处的 rate_limit
    # 暂时保留，但理想情况下应由调用方（如ExcelProcessor）统一管理
    @rate_limit(max_requests=100, time_window=60) # 调整为Tushare的限制
    def get_stock_info(self, stock_code):
        """获取股票基本信息"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从Tushare获取股票信息: {formatted_code}")
            
            # 获取股票基本信息
            basic_info = self.pro.stock_basic(
                ts_code=formatted_code,
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            if basic_info.empty:
                Logger.warning(f"未找到股票 {formatted_code} 的基本信息")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 的基本信息")
            
            # 获取最新的每日指标
            daily_basic = self.pro.daily_basic(
                ts_code=formatted_code,
                fields='ts_code,trade_date,pe,pb,total_share,float_share',
                limit=1
            )
            
            # 合并信息
            info = {
                'code': formatted_code,
                'name': basic_info['name'].iloc[0],
                'industry': basic_info['industry'].iloc[0],
                'area': basic_info['area'].iloc[0],
                'market': basic_info['market'].iloc[0],
                'list_date': basic_info['list_date'].iloc[0]
            }
            
            # 添加每日指标（如果有）
            if not daily_basic.empty:
                info.update({
                    'pe': daily_basic['pe'].iloc[0],
                    'pb': daily_basic['pb'].iloc[0],
                    'total_share': daily_basic['total_share'].iloc[0],
                    'float_share': daily_basic['float_share'].iloc[0]
                })
            
            return info
            
        except Exception as e:
            Logger.error(f"从Tushare获取股票信息时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Tushare获取股票信息失败: {str(e)}") from e

    # 移除此处的 @rate_limit，因为 get_stock_data 会调用它，而 get_stock_data 的频率已被外部控制
    def get_stock_data_by_date(self, stock_code, start_date, end_date):
        """根据指定的开始和结束日期获取股票历史数据"""
        try:
            formatted_code = self.format_stock_code(stock_code)
            Logger.info(f"开始从Tushare获取股票历史数据: {formatted_code}, 从 {start_date} 到 {end_date}")
            
            # 格式化日期（确保格式为 YYYYMMDD）
            start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
            end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
            
            # 使用 ts.pro_bar 获取前复权数据
            df = self.ts.pro_bar(
                ts_code=formatted_code,
                start_date=start_date,
                end_date=end_date,
                adj='qfq',  # 前复权
                freq='D'    # 日线数据
            )
            
            if df is None or df.empty:
                Logger.warning(f"未找到股票 {formatted_code} 在指定日期范围的数据")
                raise DataNotFoundError(f"未找到股票 {formatted_code} 在指定日期范围的数据")
            
            # 重命名列以匹配统一接口
            df = df.rename(columns={
                'trade_date': 'Date',
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'vol': 'Volume',
                'amount': 'Amount',
                'change': 'Change',
                'pct_chg': 'Pct_chg'
            })
            
            # 转换日期格式
            df['Date'] = pd.to_datetime(df['Date'])
            
            # 转换数值类型
            numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Amount', 'Change', 'Pct_chg']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 设置日期索引并按日期升序排序
            df.set_index('Date', inplace=True)
            df.sort_index(inplace=True)
            
            # 验证数据完整性
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                Logger.error(f"数据缺少必要的列: {missing_columns}")
                raise DataSourceError(f"数据缺少必要的列: {missing_columns}")
            
            # 检查数据质量
            if df.isnull().any().any():
                Logger.warning(f"数据中存在缺失值，将进行填充")
                # 对缺失值进行前向填充
                df.fillna(method='ffill', inplace=True)
                # 如果还有缺失值（比如第一行），进行后向填充
                df.fillna(method='bfill', inplace=True)
            
            return df
            
        except Exception as e:
            Logger.error(f"从Tushare获取历史数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从Tushare获取历史数据失败: {str(e)}") from e

    def get_realtime_data(self, stock_code):
        """Tushare 免费版不提供实时数据"""
        raise NotImplementedError("Tushare provider (free version) does not support real-time data fetching.")

    # 这些内部辅助方法如果被外部控制频率的方法调用，则不需要独立的rate_limit
    # 如果它们可能被独立调用，则需要考虑。暂时假设它们主要被 get_sw_index_members 调用
    def _get_sw_index_code_by_name(self, industry_name: str) -> tuple[str, int]:
        """
        根据申万行业名称获取行业代码 (index_code) 和级别 (level)。
        优先使用 SW2021 标准，失败则尝试 SW 标准。

        参数:
        industry_name: 申万行业名称 (例如: "银行", "白酒")

        返回:
        元组 (index_code, level)，例如 ("801780.SI", 1)

        抛出:
        DataNotFoundError: 如果未找到指定的行业名称
        DataSourceError: 如果无法从Tushare获取行业分类数据或级别无效
        """
        Logger.info(f"开始从Tushare获取行业 '{industry_name}' 对应的申万行业代码和级别")
        try:
            # 特殊处理银行行业名称到代码和级别的映射
            if industry_name == "银行":
                index_code_found = "801780.SI"  # 申万一级 - 银行
                level_found = 1
                Logger.info(f"使用预定义的银行行业代码: {index_code_found} (级别: {level_found}) for industry name '{industry_name}'")
                return index_code_found, level_found

            df_classify = pd.DataFrame()
            # 优先尝试 SW2021 标准
            try:
                df_classify_sw2021 = self.pro.index_classify(src='SW2021', fields='index_code,industry_name,level')
                if df_classify_sw2021 is not None and not df_classify_sw2021.empty:
                    df_classify = df_classify_sw2021
                else:
                    Logger.info("申万行业分类 (SW2021) 未找到数据或返回为空，尝试 SW (旧版).")
            except Exception as e_sw2021:
                Logger.warning(f"获取申万行业分类 (SW2021) 失败: {e_sw2021}, 尝试 SW (旧版).")

            # 如果 SW2021 失败或为空，尝试 SW 旧版标准
            if df_classify.empty:
                try:
                    df_classify_sw = self.pro.index_classify(src='SW', fields='index_code,industry_name,level')
                    if df_classify_sw is not None and not df_classify_sw.empty:
                        df_classify = df_classify_sw
                    else:
                        Logger.warning("申万行业分类 (SW 旧版) 也未找到数据或返回为空.")
                except Exception as e_sw:
                    Logger.warning(f"获取申万行业分类 (SW 旧版) 失败: {e_sw}.")

            if df_classify.empty:
                Logger.error("无法从Tushare获取任何申万行业分类数据.")
                raise DataSourceError("无法从Tushare获取申万行业分类数据.")

            # 精确匹配行业名称，优先匹配一级行业
            # Tushare 'level' field can be 'L1', 'L2', 'L3' or '1', '2', '3'.
            # We need to handle both when comparing.
            df_classify['level_int'] = df_classify['level'].astype(str).str.replace('L', '').astype(int)
            
            matched_industry = df_classify[
                (df_classify['industry_name'] == industry_name) &
                (df_classify['level_int'] == 1)  # 优先匹配一级行业
            ]

            if matched_industry.empty: # 如果没有找到一级行业，尝试匹配任意级别
                matched_industry = df_classify[df_classify['industry_name'] == industry_name]

            if matched_industry.empty:
                Logger.warning(f"未找到名称为 '{industry_name}' 的申万行业.")
                raise DataNotFoundError(f"未找到申万行业名称: '{industry_name}'")

            index_code_found = matched_industry['index_code'].iloc[0]
            level_found_str = str(matched_industry['level'].iloc[0]) # e.g. '1', 'L1'
            # Normalize level to integer 1, 2, or 3
            level_found = int(re.sub(r'\D', '', level_found_str))
            if level_found not in [1, 2, 3]:
                 raise DataSourceError(f"从Tushare获取的行业级别 '{level_found_str}' 无效，行业名称: {industry_name}")

            Logger.warning(f"为行业名称 '{industry_name}' 找到匹配的代码: {index_code_found}, 级别: {level_found}") # 使用WARNING级别记录找到的代码
            Logger.info(f"找到行业 '{industry_name}' 对应的申万行业代码: {index_code_found} (级别: {level_found})")
            return index_code_found, level_found

        except DataNotFoundError:
            raise
        except Exception as e:
            Logger.error(f"根据行业名称 '{industry_name}' 获取申万行业代码和级别时发生错误: {str(e)}", exc_info=True)
            raise DataSourceError(f"获取行业 '{industry_name}' 的申万行业代码和级别失败: {str(e)}") from e

    def _get_sw_index_level_by_code(self, index_code_to_find: str) -> int:
        """
        根据申万行业代码获取其级别 (1, 2, or 3)。
        优先使用 SW2021 标准，失败则尝试 SW 标准。

        参数:
        index_code_to_find: 申万行业代码 (例如: "801780.SI", "801781.SI")

        返回:
        对应的级别整数 (1, 2, or 3)

        抛出:
        DataNotFoundError: 如果未找到指定的行业代码
        DataSourceError: 如果无法从Tushare获取行业分类数据或级别无效
        """
        Logger.info(f"开始从Tushare获取行业代码 '{index_code_to_find}' 对应的级别")
        try:
            df_classify = pd.DataFrame()
            # 优先尝试 SW2021 标准
            try:
                df_classify_sw2021 = self.pro.index_classify(src='SW2021', fields='index_code,level')
                if df_classify_sw2021 is not None and not df_classify_sw2021.empty:
                    df_classify = df_classify_sw2021
                else:
                    Logger.info("申万行业分类 (SW2021) 未找到数据或返回为空，尝试 SW (旧版) (for level lookup).")
            except Exception as e_sw2021:
                Logger.warning(f"获取申万行业分类 (SW2021) 失败: {e_sw2021}, 尝试 SW (旧版) (for level lookup).")

            # 如果 SW2021 失败或为空，尝试 SW 旧版标准
            if df_classify.empty:
                try:
                    df_classify_sw = self.pro.index_classify(src='SW', fields='index_code,level')
                    if df_classify_sw is not None and not df_classify_sw.empty:
                        df_classify = df_classify_sw
                    else:
                        Logger.warning("申万行业分类 (SW 旧版) 也未找到数据或返回为空 (for level lookup).")
                except Exception as e_sw:
                    Logger.warning(f"获取申万行业分类 (SW 旧版) 失败: {e_sw} (for level lookup).")
            
            if df_classify.empty:
                Logger.error("无法从Tushare获取任何申万行业分类数据以确定代码级别.")
                raise DataSourceError("无法从Tushare获取申万行业分类数据以确定代码级别.")

            matched_code_info = df_classify[df_classify['index_code'] == index_code_to_find]
            
            if matched_code_info.empty:
                Logger.warning(f"未在申万行业分类中找到代码: '{index_code_to_find}'")
                raise DataNotFoundError(f"未在申万行业分类中找到代码: '{index_code_to_find}'")
            
            level_str = str(matched_code_info['level'].iloc[0]) # e.g. '1', 'L1'
            # Normalize level to integer 1, 2, or 3
            level = int(re.sub(r'\D', '', level_str))
            if level not in [1, 2, 3]:
                raise DataSourceError(f"从Tushare获取的行业级别 '{level_str}' 无效，代码: {index_code_to_find}")

            Logger.info(f"找到行业代码 '{index_code_to_find}' 对应的级别: {level}")
            return level
        except DataNotFoundError:
            raise
        except Exception as e:
            Logger.error(f"根据代码 '{index_code_to_find}' 获取申万行业级别时发生错误: {str(e)}", exc_info=True)
            raise DataSourceError(f"获取代码 '{index_code_to_find}' 的申万行业级别失败: {str(e)}") from e
            
    # 此方法如果由 ExcelProcessor 控制频率，则移除装饰器
    # 否则，需要根据Tushare对此类API的限制来设置
    @rate_limit(max_requests=100, time_window=60) # 调整为Tushare的限制
    def get_sw_index_members(self, industry_name: str = None, index_code: str = None, l1_code: str = None, l2_code: str = None, l3_code: str = None, ts_code: str = None, is_new: str = 'Y'):
        """
        获取申万行业成分构成
        可以通过行业名称、行业代码(index_code)、分级代码(l1_code, l2_code, l3_code)或股票代码(ts_code)查询。
        优先级: industry_name > index_code > (l1_code | l2_code | l3_code | ts_code)

        参数:
        industry_name: 申万行业名称 (例如: "银行", "白酒")
        index_code: 申万行业代码 (例如: "801780.SI")
        l1_code: 一级行业代码
        l2_code: 二级行业代码
        l3_code: 三级行业代码
        ts_code: 股票代码
        is_new: 是否最新 (默认为"Y是")

        返回:
        包含行业成分股的DataFrame

        抛出:
        DataNotFoundError: 如果未找到数据
        DataSourceError: 如果API调用失败或参数错误
        """
        params_for_api = {}
        log_params = {
            "industry_name": industry_name, "index_code": index_code,
            "l1_code": l1_code, "l2_code": l2_code, "l3_code": l3_code,
            "ts_code": ts_code, "is_new": is_new
        }
        Logger.info(f"开始从Tushare获取申万行业成分，请求参数: {log_params}")

        try:
            actual_index_code_to_use = None
            query_description = ""

            if industry_name:
                index_code_and_level = self._get_sw_index_code_by_name(industry_name)
                # 只取元组中的第一个元素(行业代码)，而不是整个元组
                actual_index_code_to_use = index_code_and_level[0]
                params_for_api['index_code'] = actual_index_code_to_use
                query_description = f"行业名称 '{industry_name}' (解析为代码: {actual_index_code_to_use})"
            elif index_code:
                actual_index_code_to_use = index_code
                params_for_api['index_code'] = index_code
                query_description = f"行业代码 '{index_code}'"
            elif l1_code:
                # l1_code IS an index_code for a level 1 industry
                params_for_api['index_code'] = l1_code
                query_description = f"一级行业代码 '{l1_code}' (用作 index_code)"
                actual_index_code_to_use = l1_code
            elif l2_code:
                # l2_code IS an index_code for a level 2 industry
                params_for_api['index_code'] = l2_code
                query_description = f"二级行业代码 '{l2_code}' (用作 index_code)"
                actual_index_code_to_use = l2_code
            elif l3_code:
                # l3_code IS an index_code for a level 3 industry
                params_for_api['index_code'] = l3_code
                query_description = f"三级行业代码 '{l3_code}' (用作 index_code)"
                actual_index_code_to_use = l3_code
            elif ts_code:
                params_for_api['ts_code'] = ts_code # Tushare API supports querying by ts_code
                query_description = f"股票代码 '{ts_code}'"
                # When querying by ts_code, actual_index_code_to_use remains None or its previous value
                # This is fine as the bank special handling below checks actual_index_code_to_use
            else:
                Logger.error("调用 get_sw_index_members 时未提供任何有效的查询参数。")
                raise DataSourceError("从Tushare获取申万行业成分失败: 未提供任何有效的查询参数")

            params_for_api['is_new'] = is_new
            Logger.info(f"实际调用 Tushare index_member_all 使用参数: {params_for_api} (基于查询: {query_description})")

            # 特殊处理银行行业
            # Define bank index code for clarity and reusability
            BANK_INDEX_CODE = "801780.SI"

            # Special handling for the bank industry, if queried by its industry code/name
            # and not by a specific stock code (ts_code).
            # 确保actual_index_code_to_use是字符串而不是元组
            if isinstance(actual_index_code_to_use, tuple):
                # 如果是元组，只取第一个元素(代码)
                actual_index_code_to_use = actual_index_code_to_use[0]
                
            if actual_index_code_to_use == BANK_INDEX_CODE and 'ts_code' not in params_for_api:
                try:
                    # params_for_api should correctly contain {'index_code': BANK_INDEX_CODE, 'is_new': is_new}
                    df = self.pro.index_member_all(**params_for_api)
                    if df is None or df.empty:
                        Logger.warning(f"未能获取到银行行业 '{BANK_INDEX_CODE}' 成分数据 (查询: {query_description}, API参数: {params_for_api})")
                        raise DataNotFoundError(f"未能获取到申万行业 '{BANK_INDEX_CODE}' 成分数据，参数: {params_for_api}")
                    
                    # This filter assumes 'l1_code' is the relevant column and BANK_INDEX_CODE is an L1 code.
                    # If index_member_all with index_code=BANK_INDEX_CODE guarantees all results are for this L1,
                    # this filter might be redundant. Consider verifying Tushare API behavior.
                    df_filtered = df[df['l1_code'] == BANK_INDEX_CODE]
                    if df_filtered.empty and not df.empty:
                         Logger.warning(f"银行行业 '{BANK_INDEX_CODE}' 成分数据过滤后为空 (原始 {len(df)} 条)，检查 l1_code 字段。查询: {query_description}")
                    df = df_filtered
                    
                    if df.empty:
                        Logger.warning(f"过滤后未找到银行行业 '{BANK_INDEX_CODE}' 的股票 (查询: {query_description})")
                        raise DataNotFoundError(f"未能获取到申万行业 '{BANK_INDEX_CODE}' 成分数据，参数: {params_for_api}")
                    
                    Logger.info(f"成功获取到 {len(df)} 条关于 '{query_description}' (银行行业) 的成分数据。")
                    return df
                except Exception as e:
                    Logger.error(f"获取银行行业成分数据时发生错误: {str(e)}", exc_info=True)
                    raise DataSourceError(f"从Tushare获取申万行业成分失败: {str(e)}") from e
            else:
                Logger.info(f"调用 self.pro.index_member_all with params: {params_for_api}") # 确认API调用参数
                # 添加类型检查和日志
                if 'index_code' in params_for_api and not isinstance(params_for_api['index_code'], str):
                    Logger.warning(f"index_code类型异常: {type(params_for_api['index_code'])}，值: {params_for_api['index_code']}，尝试转换为字符串")
                    # 尝试修复：如果是元组，取第一个元素
                    if isinstance(params_for_api['index_code'], tuple):
                        params_for_api['index_code'] = params_for_api['index_code'][0]
                        Logger.info(f"index_code已转换为: {params_for_api['index_code']}")
                
                # 处理其他行业
                df = self.pro.index_member_all(**params_for_api)

                if df is None or df.empty:
                    Logger.warning(f"未能获取到申万行业成分数据 (查询: {query_description}, API参数: {params_for_api})")
                    raise DataNotFoundError(f"未能获取到申万行业成分数据，参数: {params_for_api}")

                # 获取查询代码的级别 (如果之前没有获取)
                query_level = None
                if actual_index_code_to_use:
                    try:
                        # 调用 _get_sw_index_level_by_code 获取级别
                        level_of_query_code = self._get_sw_index_level_by_code(actual_index_code_to_use)
                    except DataNotFoundError:
                         Logger.warning(f"无法确定查询代码 {actual_index_code_to_use} 的级别，无法进行精确过滤。")
                         level_of_query_code = None # 或者抛出错误
                    except Exception as lvl_e:
                         Logger.error(f"获取代码 {actual_index_code_to_use} 级别时出错: {lvl_e}", exc_info=True)
                         level_of_query_code = None # 无法获取级别，不进行过滤

                # 根据级别进行过滤
                if level_of_query_code and actual_index_code_to_use:
                    level_column_map = {1: 'l1_code', 2: 'l2_code', 3: 'l3_code'}
                    filter_column = level_column_map.get(level_of_query_code)

                    if filter_column and filter_column in df.columns:
                        original_count = len(df)
                        # 使用 .copy() 避免 SettingWithCopyWarning
                        df_filtered = df[df[filter_column] == actual_index_code_to_use].copy()
                        filtered_count = len(df_filtered)
                        Logger.info(f"根据级别 {level_of_query_code} 和代码 {actual_index_code_to_use} (列: {filter_column}) 过滤成分股，从 {original_count} 条过滤到 {filtered_count} 条。")
                        if df_filtered.empty and original_count > 0:
                             Logger.warning(f"根据 {filter_column} == {actual_index_code_to_use} 过滤后结果为空，请检查 Tushare 返回的数据列和代码是否匹配。")
                        df = df_filtered # 更新 df 为过滤后的结果
                    elif filter_column:
                         Logger.warning(f"Tushare 返回的数据中缺少用于过滤的列: {filter_column}，无法按级别 {level_of_query_code} 进行精确过滤。")
                    else:
                         Logger.warning(f"无法识别的查询级别 {level_of_query_code}，无法进行精确过滤。")
                elif actual_index_code_to_use:
                    # 仅在 actual_index_code_to_use 存在但无法获取级别时记录警告
                    if level_of_query_code is None:
                        Logger.warning(f"未能获取查询代码 {actual_index_code_to_use} 的级别，跳过按级别过滤。")


                Logger.info(f"成功获取到 {len(df)} 条关于 '{query_description}' 的申万行业成分数据。") # 日志现在会显示过滤后的数量
                return df

        except DataNotFoundError:
            raise
        except DataSourceError as dse:
            Logger.error(f"获取申万行业成分数据源错误 (查询: {query_description}, 初始参数: {log_params}): {dse}", exc_info=True)
            raise
        except Exception as e:
            Logger.error(f"从Tushare获取申万行业成分时发生未知错误 (查询: {query_description}, 初始参数: {log_params}): {str(e)}", exc_info=True)
            if "token" in str(e).lower() or "权限" in str(e):
                 raise DataSourceError(f"从Tushare获取申万行业成分失败: API认证或权限问题") from e
            raise DataSourceError(f"从Tushare获取申万行业成分失败: {str(e)}") from e

class LocalDataProvider(StockDataProvider):
    """本地数据提供者"""
    def __init__(self, base_url="http://localhost:5001"):
        """
        初始化本地数据提供者
        
        参数:
        base_url: API基础URL
        """
        self.base_url = base_url
    
    def format_stock_code(self, stock_code):
        """格式化股票代码"""
        code = stock_code.strip()
        
        # 处理股票代码
        if re.match(r'^\d{6}$', code):
            if code.startswith('6'):
                return {'code': code, 'exchange': 'sh'}
            elif code.startswith(('0', '3')):
                return {'code': code, 'exchange': 'sz'}
        
        # 如果已经包含交易所信息
        if code.endswith(('.SH', '.SZ')):
            exchange = 'sh' if code.endswith('.SH') else 'sz'
            code = code[:-3]
            return {'code': code, 'exchange': exchange}
            
        raise ValueError(f"不支持的股票代码格式: {stock_code}")
    
    def get_stock_data(self, stock_code, period="3mo", interval="1d"):
        """获取股票数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            days = self._convert_period_to_days(period)
            start_date = end_date - timedelta(days=days)
            
            # 调用按日期获取数据的方法
            return self.get_stock_data_by_date(
                stock_code, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
        except Exception as e:
            logger.error(f"从本地数据源获取数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从本地数据源获取数据失败: {str(e)}") from e
    
    def get_stock_info(self, stock_code):
        """获取股票基本信息"""
        try:
            formatted = self.format_stock_code(stock_code)
            Logger.info(f"开始从本地数据源获取股票信息: {formatted}")
            
            url = f"{self.base_url}/api/stock/daily"
            params = {
                'code': formatted['code'],
                'exchange': formatted['exchange']
            }
            
            response = requests.get(url, params=params)
            if response.status_code != 200:
                Logger.error(f"本地API请求失败: {response.status_code}")
                raise APIRequestError(f"API请求失败，状态码: {response.status_code}")
            
            data = response.json()
            if data['code'] != 200 or 'data' not in data:
                Logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                raise DataSourceError(f"API返回错误: {data.get('message', '未知错误')}")
            
            stock_info = data['data']['stock_info']
            if not stock_info:
                Logger.warning(f"未找到股票 {formatted} 的信息")
                raise DataNotFoundError(f"未找到股票的基本信息")
            
            # 转换为统一格式
            info = {
                'code': stock_info['stock_code'],
                'name': stock_info['stock_name'],
                'exchange': stock_info['exchange'],
                'created_at': stock_info['created_at'],
                'updated_at': stock_info['updated_at']
            }
            
            return info
            
        except Exception as e:
            Logger.error(f"从本地数据源获取股票信息时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从本地数据源获取股票信息失败: {str(e)}") from e

    def get_realtime_data(self, stock_code):
        """本地数据源通常不包含实时数据"""
        raise NotImplementedError("Local data provider does not support real-time data fetching.")

    def get_stock_data_by_date(self, stock_code, start_date, end_date):
        """根据日期范围获取股票数据"""
        try:
            formatted = self.format_stock_code(stock_code)
            Logger.info(f"开始从本地数据源获取股票历史数据: {formatted}, 从 {start_date} 到 {end_date}")
            
            # 确保日期格式正确
            start_date = pd.to_datetime(start_date).strftime('%Y-%m-%d')
            end_date = pd.to_datetime(end_date).strftime('%Y-%m-%d')
            
            url = f"{self.base_url}/api/stock/daily"
            params = {
                'code': formatted['code'],
                'exchange': formatted['exchange'],
                'start_date': start_date,
                'end_date': end_date
            }
            
            response = requests.get(url, params=params)
            if response.status_code != 200:
                Logger.error(f"本地API请求失败: {response.status_code}")
                raise APIRequestError(f"API请求失败，状态码: {response.status_code}")
            
            data = response.json()
            if data['code'] != 200 or 'data' not in data:
                Logger.error(f"API返回错误: {data.get('message', '未知错误')}")
                raise DataSourceError(f"API返回错误: {data.get('message', '未知错误')}")
            
            daily_data = data['data']['daily_data']
            if not daily_data:
                Logger.warning(f"未找到{formatted}在指定日期范围的数据")
                raise DataNotFoundError(f"未找到股票在指定日期范围的数据")
            
            # 转换为DataFrame
            df = pd.DataFrame(daily_data)
            df = df.rename(columns={
                'trade_date': 'Date',
                'open_price': 'Open',
                'high_price': 'High',
                'low_price': 'Low',
                'close_price': 'Close',
                'volume': 'Volume',
                'turnover': 'Amount'
            })
            
            # 转换数据类型
            df['Date'] = pd.to_datetime(df['Date'])
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            
            # 设置索引
            df.set_index('Date', inplace=True)
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            Logger.error(f"从本地数据源获取历史数据时出错: {str(e)}", exc_info=True)
            raise DataSourceError(f"从本地数据源获取历史数据失败: {str(e)}") from e


class StockDataProviderFactory:
    """股票数据提供者工厂类"""
    @staticmethod
    def get_provider(provider_name):
        """
        获取数据提供者实例
        :param provider_name: 提供者名称 ('yahoo', 'sina', 'tushare', 'local')
        :return: 数据提供者实例
        """
        providers = {
            'yahoo': YahooFinanceProvider(),
            'sina': SinaFinanceProvider(),
            'tushare': TushareProvider(),
            'local': LocalDataProvider()
        }
        
        if provider_name not in providers:
            raise ValueError(f"不支持的数据提供者: {provider_name}")
            
        return providers[provider_name]