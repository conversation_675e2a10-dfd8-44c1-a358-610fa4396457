from tests.test_stock_data import StockDataTest
import os

def save_to_file(df, filename, stock_code, start_date, end_date):
    """
    将数据保存到文件
    
    参数:
        df: DataFrame对象
        filename: 文件名
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    """
    if df.empty:
        return
        
    # 确保输出目录存在
    os.makedirs('output', exist_ok=True)
    
    # 构建完整的文件路径
    filepath = os.path.join('output', filename)
    
    # 写入文件头部信息
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(f"股票代码: {stock_code}\n")
        f.write(f"日期范围: {start_date} 至 {end_date}\n")
        f.write("=" * 50 + "\n\n")
        
        # 写入基本数据
        f.write("1. 基本数据:\n")
        df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].to_string(f)
        f.write("\n\n")
        
        # 写入移动平均线
        f.write("2. 移动平均线:\n")
        df[['Date', 'Close', 'ma_5', 'ma_20']].to_string(f)
        f.write("\n\n")
        
        # 写入RSI指标
        f.write("3. RSI指标:\n")
        df[['Date', 'Close', 'rsi_14']].to_string(f)
        f.write("\n\n")
        
        # 写入MACD指标
        f.write("4. MACD指标:\n")
        df[['Date', 'macd', 'macd_signal', 'macd_diff']].to_string(f)
        f.write("\n\n")
        
        # 写入布林带
        f.write("5. 布林带:\n")
        df[['Date', 'boll_upper', 'boll_mid', 'boll_lower']].to_string(f)
        f.write("\n\n")
        
        # 写入成交量均线
        f.write("6. 成交量均线:\n")
        df[['Date', 'Volume', 'vol_ma_5', 'vol_ma_20']].to_string(f)
        f.write("\n")
    
    print(f"\n数据已保存到文件: {filepath}")

def main():
    # 创建测试实例
    stock_test = StockDataTest()
    
    # 测试贵州茅台的数据
    print("获取贵州茅台(600519)的数据...")
    df1 = stock_test.get_stock_data(
        stock_code="600519",
        start_date="2024-01-01",
        end_date="2024-03-20"
    )
    
    if not df1.empty:
        print("\n1. 基本数据:")
        print(df1[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].head())
        print("\n2. 移动平均线:")
        print(df1[['Date', 'Close', 'ma_5', 'ma_20']].head())
        print("\n3. RSI指标:")
        print(df1[['Date', 'Close', 'rsi_14']].head())
        print("\n4. MACD指标:")
        print(df1[['Date', 'macd', 'macd_signal', 'macd_diff']].head())
        print("\n5. 布林带:")
        print(df1[['Date', 'boll_upper', 'boll_mid', 'boll_lower']].head())
        print("\n6. 成交量均线:")
        print(df1[['Date', 'Volume', 'vol_ma_5', 'vol_ma_20']].head())
        
        # 保存贵州茅台的数据到文件
        save_to_file(df1, 'maotai_test.txt', '600519', '2024-01-01', '2024-03-20')
    
    # 测试自定义股票
    stock_code = input("\n请输入要查询的股票代码（例如：000001）：")
    start_date = input("请输入开始日期（例如：2024-01-01）：")
    end_date = input("请输入结束日期（例如：2024-03-20）：")
    
    # 使用默认值
    if not start_date:
        start_date = "2024-01-01"
    if not end_date:
        end_date = "2024-03-20"
    
    print(f"\n获取 {stock_code} 的数据...")
    df2 = stock_test.get_stock_data(
        stock_code=stock_code,
        start_date=start_date,
        end_date=end_date
    )
    
    if not df2.empty:
        print("\n1. 基本数据:")
        print(df2[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].head())
        print("\n2. 技术指标:")
        print(df2[['Date', 'ma_5', 'ma_20', 'rsi_14', 'macd', 'boll_upper', 'boll_lower']].head())
        
        # 保存自定义股票的数据到文件
        save_to_file(df2, f'stock_{stock_code}_test.txt', stock_code, start_date, end_date)

if __name__ == "__main__":
    main() 