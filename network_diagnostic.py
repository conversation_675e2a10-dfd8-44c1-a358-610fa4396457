#!/usr/bin/env python3
"""
网络连接诊断工具 - 专门诊断 Gemini API 连接问题
"""

import os
import socket
import ssl
import requests
import time
import subprocess
import platform
from urllib.parse import urlparse

class NetworkDiagnostic:
    def __init__(self):
        self.target_host = "generativelanguage.googleapis.com"
        self.target_port = 443
        self.test_urls = [
            "https://www.google.com",
            "https://ai.google.dev", 
            "https://generativelanguage.googleapis.com"
        ]
    
    def check_system_info(self):
        """检查系统信息"""
        print("🖥️ 系统信息检查")
        print("=" * 40)
        
        print(f"操作系统: {platform.system()} {platform.release()}")
        print(f"Python版本: {platform.python_version()}")
        
        # 检查SSL版本
        print(f"SSL版本: {ssl.OPENSSL_VERSION}")
        
        # 检查关键库版本
        try:
            import requests
            print(f"Requests版本: {requests.__version__}")
        except:
            print("❌ Requests库未安装")
        
        try:
            import urllib3
            print(f"urllib3版本: {urllib3.__version__}")
        except:
            print("❌ urllib3库未安装")
        
        # 检查证书
        try:
            cert_path = requests.certs.where()
            print(f"证书路径: {cert_path}")
            
            # 检查证书文件是否存在
            if os.path.exists(cert_path):
                stat = os.stat(cert_path)
                print(f"证书文件大小: {stat.st_size} bytes")
                print(f"证书修改时间: {time.ctime(stat.st_mtime)}")
            else:
                print("❌ 证书文件不存在")
        except Exception as e:
            print(f"❌ 证书检查失败: {e}")
    
    def check_dns_resolution(self):
        """检查DNS解析"""
        print("\n🌐 DNS解析检查")
        print("=" * 40)
        
        try:
            # 解析目标域名
            ip_list = socket.gethostbyname_ex(self.target_host)
            print(f"✅ {self.target_host} 解析成功")
            print(f"   主IP: {ip_list[2][0]}")
            print(f"   所有IP: {', '.join(ip_list[2])}")
            
            # 测试反向解析
            try:
                hostname = socket.gethostbyaddr(ip_list[2][0])
                print(f"   反向解析: {hostname[0]}")
            except:
                print("   反向解析: 失败")
                
            return True
            
        except socket.gaierror as e:
            print(f"❌ DNS解析失败: {e}")
            print("💡 建议:")
            print("   1. 检查DNS设置")
            print("   2. 尝试使用公共DNS (*******, *******)")
            print("   3. 检查网络连接")
            return False
    
    def check_port_connectivity(self):
        """检查端口连通性"""
        print("\n🔌 端口连通性检查")
        print("=" * 40)
        
        try:
            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            result = sock.connect_ex((self.target_host, self.target_port))
            sock.close()
            
            if result == 0:
                print(f"✅ {self.target_host}:{self.target_port} 端口连通")
                return True
            else:
                print(f"❌ {self.target_host}:{self.target_port} 端口不通")
                print("💡 可能原因:")
                print("   1. 防火墙阻断")
                print("   2. 代理设置问题")
                print("   3. 网络运营商限制")
                return False
                
        except Exception as e:
            print(f"❌ 端口检查失败: {e}")
            return False
    
    def check_ssl_handshake(self):
        """检查SSL握手"""
        print("\n🔒 SSL握手检查")
        print("=" * 40)
        
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 尝试SSL连接
            with socket.create_connection((self.target_host, self.target_port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=self.target_host) as ssock:
                    print(f"✅ SSL握手成功")
                    print(f"   协议版本: {ssock.version()}")
                    print(f"   加密套件: {ssock.cipher()}")
                    
                    # 获取证书信息
                    cert = ssock.getpeercert()
                    print(f"   证书主题: {cert.get('subject', 'Unknown')}")
                    print(f"   证书颁发者: {cert.get('issuer', 'Unknown')}")
                    print(f"   证书有效期: {cert.get('notAfter', 'Unknown')}")
                    
            return True
            
        except ssl.SSLError as e:
            print(f"❌ SSL错误: {e}")
            print("💡 可能解决方案:")
            print("   1. 更新系统证书")
            print("   2. 检查系统时间")
            print("   3. 尝试禁用SSL验证（仅测试）")
            return False
        except Exception as e:
            print(f"❌ SSL检查失败: {e}")
            return False
    
    def check_http_requests(self):
        """检查HTTP请求"""
        print("\n🌍 HTTP请求检查")
        print("=" * 40)
        
        # 不同的请求配置
        configs = [
            {"verify": True, "timeout": 10, "name": "标准HTTPS"},
            {"verify": False, "timeout": 10, "name": "跳过SSL验证"},
            {"verify": True, "timeout": 30, "name": "延长超时"},
        ]
        
        # 检查代理设置
        proxies = {}
        if os.getenv('HTTP_PROXY') or os.getenv('HTTPS_PROXY'):
            proxies = {
                'http': os.getenv('HTTP_PROXY'),
                'https': os.getenv('HTTPS_PROXY')
            }
            print(f"🌐 检测到代理: {proxies}")
            configs.append({"verify": True, "timeout": 10, "proxies": proxies, "name": "使用代理"})
        
        success_count = 0
        
        for url in self.test_urls:
            print(f"\n测试URL: {url}")
            
            for config in configs:
                try:
                    print(f"  🔧 {config['name']}: ", end="")
                    
                    response = requests.get(url, **{k: v for k, v in config.items() if k != 'name'})
                    
                    if response.status_code == 200:
                        print(f"✅ 成功 (状态码: {response.status_code})")
                        success_count += 1
                        break
                    else:
                        print(f"⚠️ 状态码: {response.status_code}")
                        
                except requests.exceptions.SSLError as e:
                    print(f"🔒 SSL错误: {str(e)[:50]}...")
                except requests.exceptions.ConnectionError as e:
                    print(f"🔌 连接错误: {str(e)[:50]}...")
                except requests.exceptions.Timeout as e:
                    print(f"⏰ 超时: {str(e)[:50]}...")
                except Exception as e:
                    print(f"❌ 其他错误: {str(e)[:50]}...")
        
        return success_count > 0
    
    def check_gemini_api_access(self):
        """检查Gemini API访问"""
        print("\n🤖 Gemini API访问检查")
        print("=" * 40)
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ 未设置GEMINI_API_KEY环境变量")
            print("💡 请先设置API密钥:")
            print("   export GEMINI_API_KEY='your_api_key'")
            return False
        
        print(f"🔑 API密钥: {api_key[:10]}...{api_key[-4:]}")
        
        # 测试API端点
        models = ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"]
        
        for model in models:
            url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": "Hello"
                    }]
                }]
            }
            
            try:
                print(f"  🔄 测试模型: {model}")
                response = requests.post(
                    f"{url}?key={api_key}",
                    json=data,
                    timeout=30,
                    verify=True
                )
                
                if response.status_code == 200:
                    print(f"    ✅ 模型可用")
                    return True
                elif response.status_code == 404:
                    print(f"    ❌ 模型不存在")
                elif response.status_code == 401:
                    print(f"    🔑 API密钥错误")
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)[:50]}...")
        
        return False
    
    def suggest_solutions(self, results):
        """根据检查结果提供解决方案"""
        print("\n💡 解决方案建议")
        print("=" * 40)
        
        if not results['dns']:
            print("🌐 DNS问题解决方案:")
            print("   1. 更换DNS服务器:")
            print("      - macOS: 系统偏好设置 > 网络 > 高级 > DNS")
            print("      - Windows: 网络设置 > 更改适配器选项")
            print("   2. 使用公共DNS: *******, *******")
            print("   3. 刷新DNS缓存:")
            if platform.system() == "Darwin":
                print("      sudo dscacheutil -flushcache")
            elif platform.system() == "Windows":
                print("      ipconfig /flushdns")
        
        if not results['port']:
            print("\n🔌 端口连通性问题解决方案:")
            print("   1. 检查防火墙设置")
            print("   2. 配置代理服务器:")
            print("      export HTTPS_PROXY=http://proxy:port")
            print("   3. 尝试使用VPN")
        
        if not results['ssl']:
            print("\n🔒 SSL问题解决方案:")
            print("   1. 更新系统证书:")
            print("      pip install --upgrade certifi")
            print("   2. 检查系统时间是否正确")
            print("   3. 临时跳过SSL验证（仅测试）:")
            print("      requests.get(url, verify=False)")
        
        if not results['http']:
            print("\n🌍 HTTP请求问题解决方案:")
            print("   1. 使用代理:")
            print("      proxies = {'https': 'http://proxy:port'}")
            print("   2. 增加超时时间:")
            print("      requests.get(url, timeout=60)")
            print("   3. 使用VPN或其他网络")
        
        if not results['api']:
            print("\n🤖 API访问问题解决方案:")
            print("   1. 检查API密钥是否正确")
            print("   2. 确认API密钥权限")
            print("   3. 尝试不同的模型名称")
            print("   4. 使用增强版分析器:")
            print("      python robust_gemini_analyzer.py --input_dir output")
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🔍 Gemini API 网络连接完整诊断")
        print("=" * 60)
        
        results = {}
        
        # 1. 系统信息
        self.check_system_info()
        
        # 2. DNS解析
        results['dns'] = self.check_dns_resolution()
        
        # 3. 端口连通性
        results['port'] = self.check_port_connectivity()
        
        # 4. SSL握手
        results['ssl'] = self.check_ssl_handshake()
        
        # 5. HTTP请求
        results['http'] = self.check_http_requests()
        
        # 6. API访问
        results['api'] = self.check_gemini_api_access()
        
        # 7. 总结
        print("\n📊 诊断结果总结")
        print("=" * 40)
        
        checks = [
            ("DNS解析", results['dns']),
            ("端口连通", results['port']),
            ("SSL握手", results['ssl']),
            ("HTTP请求", results['http']),
            ("API访问", results['api'])
        ]
        
        for name, status in checks:
            icon = "✅" if status else "❌"
            print(f"{icon} {name}: {'正常' if status else '异常'}")
        
        success_rate = sum(results.values()) / len(results) * 100
        print(f"\n🎯 整体成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 网络连接完全正常！可以正常使用Gemini API")
        elif success_rate >= 60:
            print("⚠️ 网络连接基本正常，但可能需要一些调整")
        else:
            print("❌ 网络连接存在严重问题，需要解决后才能使用")
        
        # 8. 解决方案建议
        self.suggest_solutions(results)
        
        return results

def main():
    diagnostic = NetworkDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
