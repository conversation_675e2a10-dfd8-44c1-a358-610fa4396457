from src.backtest.ma10_pullback_backtest import MA10PullbackBacktester

def main():
    # 测试用例：获取平安银行2年日线数据
    stock_code = '600360.SH'  # 你可以替换为其他股票代码
    backtester = MA10PullbackBacktester(data_source='tushare')
    
    # 第一步：获取历史数据 - 增加获取数据量到5年
    print("="*50)
    print("第一步：获取历史数据")
    df = backtester.get_history_data(stock_code, period='5y', interval='1d')
    if df is None:
        print(f"获取 {stock_code} 的历史数据失败")
        return
    
    print(f"成功获取 {stock_code} 的历史数据，共 {len(df)} 行")
    print(f"原始数据列: {df.columns.tolist()}")
    
    # 第二步：生成历史信号 - 调整窗口大小
    print("="*50)
    print("第二步：生成历史信号")
    success = backtester.generate_signals(window_size=60)  # 增加窗口大小
    
    if not success:
        print("信号生成失败！")
        return
        
    print("信号生成成功！")
    
    # 显示信号统计信息
    total_days = len(backtester.df)
    signal_days = backtester.df['Signal'].sum()
    signal_ratio = signal_days / total_days * 100
    
    print("\n信号统计:")
    print(f"- 总交易日数: {total_days}")
    print(f"- 买入信号天数: {signal_days}")
    print(f"- 信号比例: {signal_ratio:.2f}%")
    
    # 第三步：执行回测
    print("="*50)
    print("第三步：执行回测")
    trades = backtester.run_backtest(hold_days=10, initial_capital=100000, position_sizing_ratio=0.1)
    
    if trades is None:
        print("回测执行失败！")
        return
    
    # 第四步：计算绩效指标
    print("="*50)
    print("第四步：计算绩效指标")
    performance = backtester.calculate_performance(initial_capital=100000)
    
    if performance is None:
        print("绩效指标计算失败！")
        return
    elif not performance:
        print("没有交易记录，无法计算绩效指标。")
        return
    
    # 显示详细的交易记录
    if trades:
        print("\n=== 详细交易记录 ===")
        for i, trade in enumerate(trades, 1):
            print(f"\n交易 #{i}:")
            print(f"买入: {trade['entry_date']} @ {trade['entry_price']:.2f}")
            print(f"卖出: {trade['exit_date']} @ {trade['exit_price']:.2f}")
            print(f"持仓天数: {trade['hold_days']}")
            print(f"交易股数: {trade['shares']}")
            print(f"盈亏: {trade['profit_amount']:.2f} ({trade['profit_pct']:.2f}%)")
            if 'note' in trade:
                print(f"备注: {trade['note']}")
    else:
        print("\n没有交易记录")

if __name__ == "__main__":
    main() 