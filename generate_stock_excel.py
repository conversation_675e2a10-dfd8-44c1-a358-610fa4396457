import pandas as pd
import argparse
import pyperclip
import xlsxwriter # 用于访问剪贴板
# xlsxwriter 是一个新的依赖，如果需要此脚本的全部功能，请确保已安装

def get_data_from_source(input_filepath=None):
    """
    从指定来源获取数据字符串。
    如果提供了 input_filepath，则从文件读取。
    否则，尝试从剪贴板读取。
    """
    data_string = None
    source_description = ""

    if input_filepath:
        try:
            with open(input_filepath, 'r', encoding='utf-8') as f:
                data_string = f.read()
            source_description = f"文件 '{input_filepath}'"
        except FileNotFoundError:
            print(f"错误：输入文件 {input_filepath} 未找到。")
            return None, source_description
        except Exception as e:
            print(f"读取文件 {input_filepath} 时出错: {e}")
            return None, source_description
    else:
        try:
            data_string = pyperclip.paste()
            source_description = "剪贴板"
            if not data_string or data_string.isspace():
                print("信息：剪贴板为空或只包含空白字符。")
                return None, source_description
        except pyperclip.PyperclipException as e:
            # 提示用户安装 pyperclip 及其依赖
            print(f"错误：访问剪贴板失败。请确保已安装 pyperclip 库及其系统依赖 (如 xclip/xsel on Linux, pbcopy/pbpaste on macOS)。错误信息: {e}")
            return None, source_description
        except Exception as e:
            print(f"从剪贴板读取数据时发生未知错误: {e}")
            return None, source_description
            
    if not data_string or data_string.isspace():
        print(f"错误：从 {source_description} 获取的数据为空。")
        return None, source_description
        
    return data_string.strip(), source_description

def write_data_to_excel(data_string, source_description, output_filename="stock_data.xlsx"):
    headers = [
        "序号", "股票名称", "股票代码", "流通市值", "成交额（亿）", "申万板块",
        "命中日期", "筛选日期", "当日大单买入金额（亿）", "昨日大单买入金额（亿）",
        "倍数", "同花顺概念", "开盘啦概念", "涨跌幅", "当天最低价"
    ]

    lines = data_string.split('\n')
    data_rows = []
    for line_number, line in enumerate(lines, 1):
        fields = line.split('\t')
        if len(fields) < len(headers):
            fields.extend([''] * (len(headers) - len(fields)))
        elif len(fields) > len(headers):
            fields = fields[:len(headers)]
        data_rows.append(fields)

    # 写入Excel，确保股票代码列为文本格式
    workbook = xlsxwriter.Workbook(output_filename)
    worksheet = workbook.add_worksheet()

    # 设置文本格式
    text_format = workbook.add_format({'num_format': '@'})

    # 写header
    for idx, h in enumerate(headers):
        worksheet.write(0, idx, h)
    
    # 写数据，股票代码用write_string强制文本
    code_col_idx = headers.index("股票代码")
    for row_idx, row in enumerate(data_rows, 1):
        for col_idx, value in enumerate(row):
            if col_idx == code_col_idx:
                worksheet.write_string(row_idx, col_idx, str(value).zfill(6), text_format)
            else:
                worksheet.write(row_idx, col_idx, value)
    workbook.close()
    print(f"已成功写入 {output_filename}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="将文本格式的股票数据 (从剪贴板或文件) 转换为 Excel 文件。\n确保已安装 pandas, pyperclip, openpyxl, xlsxwriter。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "--input",
        type=str,
        required=False,
        help="可选：输入的文本文件名。\n如果未提供，脚本将尝试从剪贴板读取数据。"
    )
    parser.add_argument(
        "--output",
        type=str,
        default="stock_data_output.xlsx",
        help="输出的 Excel 文件名 (默认: stock_data_output.xlsx)"
    )
    args = parser.parse_args()

    print("提示：此脚本需要 pandas, pyperclip, openpyxl, xlsxwriter 库。")
    print("如果遇到 ImportError，请使用 pip install <库名> 进行安装。")
    print("对于剪贴板功能，Linux 可能需要 xclip 或 xsel。\n")


    data_to_process, source_name = get_data_from_source(args.input)

    if data_to_process:
        write_data_to_excel(data_to_process, source_name, args.output)
    else:
        print("未能获取到有效数据进行处理。")