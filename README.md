# 🚀 股票趋势分析工具套件

一套完整的股票趋势分析工具，结合本地数据分析和AI智能分析，专门用于发现排名连续上升的股票和挖掘后排股票的上升潜力。

## 🎯 核心功能

### 📊 **本地分析工具**
- **排名趋势分析**: 识别排名连续上升的股票
- **板块概念分析**: 深度分析申万板块和热门概念表现
- **后排股票挖掘**: 独创的潜力股票评分算法
- **市值效应研究**: 分析不同市值股票的表现差异

### 🤖 **AI增强分析**
- **Gemini AI分析**: 基于Google Gemini的专业投资分析
- **强化提示词**: 20年A股实战经验的量化分析师角色
- **实战策略**: 具体的选股标准和操作建议
- **风险管理**: 智能识别投资风险和应对策略

### 🛡️ **技术特色**
- **多模型回退**: 支持4个Gemini模型自动切换
- **网络诊断**: 智能网络连接问题诊断
- **输出管理**: 所有结果输出到指定目录
- **批量处理**: 支持多种运行模式

## 🛠️ 工具矩阵

| 工具名称 | 主要功能 | 输出文件 | 使用场景 |
|----------|----------|----------|----------|
| `bottom_stock_analyzer.py` | 后排股票潜力分析 | `.md` + `.json` | 潜力股挖掘 |
| `enhanced_gemini_analyzer.py` | AI深度分析 | `.md` | 全面AI分析 |
| `robust_gemini_analyzer.py` | 增强连接分析 | `.md` | 网络问题时 |
| `gemini_stock_analyzer.py` | 标准AI分析 | `.md` | 基础AI分析 |
| `network_diagnostic.py` | 网络诊断 | 控制台输出 | 故障排除 |
| `run_analysis.py` | 统一运行脚本 | 多种 | 批量分析 |
| `cleanup_old_files.py` | 文件清理 | - | 维护管理 |

## 🚀 快速开始

### 1. **环境准备**
```bash
# 安装依赖
pip install pandas requests

# 设置API密钥（可选，用于AI分析）
export GEMINI_API_KEY="your_api_key_here"
```

### 2. **基础分析**（无需API密钥）
```bash
# 后排股票潜力分析
python bottom_stock_analyzer.py --input_dir output --output_dir analysis_reports

# 查看结果
ls analysis_reports/
```

### 3. **AI增强分析**（需要API密钥）
```bash
# 获取API密钥: https://makersuite.google.com/app/apikey

# 测试连接
python network_diagnostic.py

# 运行AI分析
python enhanced_gemini_analyzer.py --input_dir output --output_dir analysis_reports --api_key YOUR_KEY
```

### 4. **一键运行**
```bash
# 运行所有分析
python run_analysis.py --input_dir output --output_dir my_reports --api_key YOUR_KEY

# 只运行后排股票分析
python run_analysis.py --input_dir output --output_dir my_reports --mode bottom
```

## 📊 核心算法

### 🔮 **后排股票潜力评分模型**

我们开发了独特的100分制评分系统：

```
总分 = 板块得分(30) + 概念得分(25) + 市值得分(20) + 量比得分(15) + 涨幅得分(10)
```

#### 🏆 **评分标准**
- **板块得分**: 基于历史成功率，最高30分
- **概念得分**: 热门概念加分，最高25分  
- **市值得分**: 小盘股优势，<30亿得20分
- **量比得分**: 放量股票加分，≥3倍得15分
- **涨幅得分**: 温和上涨最优，0-3%得10分

### 🎯 **AI分析提示词特色**

#### 专业角色定位
```
你是一位顶级的量化分析师和投资策略专家，拥有20年A股市场实战经验
```

#### 8大分析维度
1. **核心发现与投资机会**: 超级强势特征、板块轮动规律
2. **后排潜力股挖掘**: 黑马识别、上升概率排序
3. **量化投资策略**: 多因子模型、概率预测
4. **操作策略细化**: 分批建仓、止盈止损
5. **市场前瞻与布局**: 趋势预判、资金轮动
6. **实战建议**: 今日操作、明日策略
7. **风险管理**: 风险识别、应急预案
8. **特别要求**: 数据驱动、可操作性

## 📈 实际分析结果

### 🏆 **TOP5 潜力股票**（示例）
1. **002177 御银股份** (83.0分) - 计算机设备 + 数字货币概念
2. **300386 飞天诚信** (81.0分) - 计算机设备 + 跨境支付
3. **002530 金财互联** (80.0分) - IT服务 + AI智能体
4. **300531 优博讯** (78.0分) - 计算机设备 + 华为鸿蒙
5. **300541 先进数通** (77.0分) - IT服务 + 英伟达概念

### 📊 **关键发现**
- **最成功板块**: IT服务Ⅱ (平均上升46位)
- **最热概念**: DeepSeek概念 (平均上升47.5位)
- **市值效应**: 小盘股(<50亿)表现最优

## 🎨 使用模式

### 📅 **日常使用流程**
```bash
# 1. 每日后排股票分析
python bottom_stock_analyzer.py --input_dir output --output_dir daily_reports

# 2. 深度AI分析（可选）
python enhanced_gemini_analyzer.py --input_dir output --output_dir daily_reports

# 3. 查看结果
ls daily_reports/
```

### 🔄 **批量分析**
```bash
# 按日期组织
DATE=$(date +%Y-%m-%d)
python run_analysis.py --input_dir output --output_dir "analysis/$DATE" --mode all

# 按类型组织
python run_analysis.py --input_dir output --output_dir "reports/bottom_stocks" --mode bottom
```

### 🧹 **维护管理**
```bash
# 清理根目录旧文件
python cleanup_old_files.py --execute

# 查看生成的报告
python run_analysis.py --input_dir output --output_dir test --mode bottom
```

## 🔧 故障排除

### 🌐 **网络连接问题**
```bash
# 1. 运行网络诊断
python network_diagnostic.py

# 2. 配置代理（如需要）
export HTTPS_PROXY=http://proxy:port

# 3. 使用增强版工具
python robust_gemini_analyzer.py --input_dir output --output_dir reports
```

### 🔑 **API密钥问题**
```bash
# 1. 获取密钥
# 访问: https://makersuite.google.com/app/apikey

# 2. 设置环境变量
export GEMINI_API_KEY="your_api_key"

# 3. 测试连接
python test_gemini_api.py
```

### 📁 **文件管理问题**
```bash
# 检查输出目录
ls -la analysis_reports/

# 清理旧文件
python cleanup_old_files.py --target_dir old_reports --execute

# 手动创建目录
mkdir -p my_analysis_reports
```

## 💡 投资策略建议

### 🎯 **选股策略**
1. **优先级**: 潜力得分>70的后排股票
2. **板块**: 计算机设备、IT服务、化学制药
3. **概念**: AI相关、跨境支付、数字货币
4. **市值**: 优选<100亿的小盘股
5. **技术**: 关注放量且涨幅适中的股票

### 📅 **操作策略**
1. **建仓**: 分2-3次建仓，首次30%
2. **止损**: 跌破买入价8%坚决止损
3. **止盈**: 排名上升20位或涨幅15%分批止盈
4. **周期**: 持仓3-10个交易日
5. **仓位**: 单只股票不超过总仓位的5%

### ⚠️ **风险控制**
1. **波动性**: 后排股票波动大，控制仓位
2. **基本面**: 注意财务状况和业绩支撑
3. **流动性**: 小盘股可能存在流动性不足
4. **市场**: 整体下跌时风险加大
5. **概念**: 避免追高概念炒作股票

## 📁 输出文件说明

### 📄 **文件类型**
- **`.md`**: Markdown格式的详细分析报告
- **`.json`**: JSON格式的结构化数据

### 🏷️ **命名规则**
```
{tool_name}_{timestamp}.{extension}

示例:
- bottom_stocks_analysis_20250604_071159.md
- enhanced_gemini_analysis_20250604_080000.md
```

### 📊 **默认输出目录**
- 所有工具默认输出到 `analysis_reports/` 目录
- 可通过 `--output_dir` 参数自定义
- 自动创建目录，无需手动创建

## 🎉 特色优势

### 🔥 **独创功能**
- **后排股票挖掘**: 市场首创的潜力股评分算法
- **AI增强分析**: 超级强化的投资分析提示词
- **多重保障**: 网络问题自动回退机制
- **实战导向**: 具体到股票代码和操作建议

### 📊 **数据驱动**
- 基于历史成功案例的量化分析
- 18个成功上升案例的模式识别
- 多维度综合评分系统
- 实时数据更新和分析

### 🛡️ **技术优势**
- 支持4个AI模型自动回退
- 智能网络连接诊断
- 详细的错误处理和重试机制
- 完善的文件管理和输出控制

## 📞 技术支持

### 🔗 **相关链接**
- [Google AI Studio](https://makersuite.google.com/app/apikey) - 获取API密钥
- [Gemini API文档](https://ai.google.dev/docs) - 官方文档

### 📚 **详细文档**
- `OUTPUT_DIRECTORY_GUIDE.md` - 输出目录管理指南
- `ENHANCED_ANALYSIS_GUIDE.md` - 增强分析功能指南
- `FAILURE_ANALYSIS.md` - 故障分析和解决方案
- `GEMINI_FIX_NOTES.md` - API修复说明

## 安装

1.  克隆或下载本仓库:
    ```bash
    git clone https://github.com/yourusername/stock_paul.git
    cd stock_paul
    ```

2.  安装依赖:
    ```bash
    pip install -r requirements.txt
    ```

## 🔄 传统功能（保留）

### GUI模式 (K线图)
```bash
# 启动K线图GUI界面
python run.py --mode gui
```

### Excel查看器模式
```bash
# 启动Excel查看器GUI界面
python run.py --mode excel
```

### CLI模式示例（传统功能）
详细的传统CLI功能请参考原有文档。主要包括：
- K线图分析和技术指标
- Excel数据处理
- 股票管理和分类
- 批量数据获取和分析

## 📚 详细文档

### 🔗 **相关文档**
- `OUTPUT_DIRECTORY_GUIDE.md` - 输出目录管理指南
- `ENHANCED_ANALYSIS_GUIDE.md` - 增强分析功能指南  
- `FAILURE_ANALYSIS.md` - 故障分析和解决方案
- `GEMINI_FIX_NOTES.md` - API修复说明
- `README_GEMINI.md` - Gemini API 详细使用指南

### 🎯 **核心价值**
通过数据驱动的分析和AI智能增强，帮助发现被市场忽视的投资机会，特别是后排股票的上升潜力！

---

**⚠️ 免责声明**: 本工具仅供参考，不构成投资建议。投资有风险，决策需谨慎！

**🚀 开始使用**: 
```bash
# 快速体验后排股票分析
python bottom_stock_analyzer.py --input_dir output --output_dir my_analysis

# 查看结果
ls my_analysis/
```
