# 🤖 Gemini AI 股票趋势分析工具

基于 Google Gemini API 的智能股票趋势分析工具，能够深度分析排名连续上升股票的特征和投资机会。

## 🎯 核心功能

### 📊 数据分析能力
- ✅ **多日数据整合**: 自动加载和分析多个交易日的股票数据
- ✅ **排名趋势识别**: 精准识别排名连续上升的股票
- ✅ **板块概念分析**: 深度分析申万板块和热门概念表现
- ✅ **市值效应研究**: 分析不同市值股票的表现差异

### 🤖 AI 分析维度
- **📈 涨幅特征**: 识别高涨幅股票的共同特点和规律
- **🏭 板块效应**: 找出表现最优的申万板块和原因分析
- **🎯 概念热点**: 追踪当前最热门的投资概念和轮动趋势
- **💎 市值分析**: 研究大中小盘股的表现差异
- **📊 投资策略**: 提供具体的选股标准和操作建议
- **⚠️ 风险识别**: 识别潜在的投资风险点和陷阱

## 🚀 快速开始

### 1. 获取 Gemini API 密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录 Google 账户并创建 API 密钥
3. 复制生成的 API 密钥

### 2. 安装依赖
```bash
pip install requests pandas
```

### 3. 运行分析

#### 方法一：使用一键脚本 (推荐)
```bash
# 直接提供 API 密钥
./run_gemini_analysis.sh YOUR_API_KEY

# 或者先设置环境变量
export GEMINI_API_KEY="YOUR_API_KEY"
./run_gemini_analysis.sh
```

#### 方法二：直接运行 Python 脚本
```bash
# 使用环境变量
export GEMINI_API_KEY="YOUR_API_KEY"
python gemini_stock_analyzer.py --input_dir output

# 或直接提供密钥
python gemini_stock_analyzer.py --input_dir output --api_key "YOUR_API_KEY"
```

### 4. 测试 API 连接
```bash
python test_gemini_api.py
```

## 📁 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `gemini_stock_analyzer.py` | 主分析脚本，核心功能实现 |
| `test_gemini_api.py` | API 连接测试工具 |
| `run_gemini_analysis.sh` | 一键运行脚本 |
| `gemini_setup_guide.md` | 详细设置指南 |
| `README_GEMINI.md` | 本说明文档 |

## 📊 分析输出示例

### 控制台输出
```
🚀 开始股票趋势 AI 分析
============================================================
📊 正在加载股票数据...
  ✅ 20250529: 156 只股票
  ✅ 20250530: 123 只股票
  ✅ 20250603: 164 只股票
🔍 正在分析排名上升股票...
  📈 发现 33 只排名上升股票
🤖 正在调用 Gemini API 进行分析...

============================================================
🎯 Gemini AI 分析结果
============================================================
[详细的 AI 分析内容...]

📄 分析报告已保存到: gemini_analysis_20250115_143022.md
```

### AI 分析报告内容
生成的 Markdown 报告包含：

#### 📊 核心发现分析
- **涨幅特征**: 高涨幅股票的共同特点
- **板块效应**: 表现最优的申万板块分析
- **概念热点**: 当前最热门的投资概念
- **市值效应**: 不同市值股票的表现差异

#### 🎯 投资洞察
- **强势板块**: 表现突出的板块及原因
- **概念机会**: 值得关注的概念及持续性
- **选股策略**: 基于数据的选股规律
- **风险提示**: 需要注意的风险点

#### 📈 市场趋势判断
- **资金偏好**: 当前资金偏好分析
- **热点轮动**: 概念和板块轮动特征
- **后市展望**: 基于数据的后市判断

#### 💡 实用建议
- **优选标准**: 具体的选股标准
- **操作策略**: 买卖时机和仓位建议
- **风险控制**: 风险管理措施

## 🎯 核心提示词

脚本使用的分析提示词包含以下维度：

```
你是一位资深的股票分析师，请基于排名连续上升股票的数据进行深度分析：

## 📊 核心发现分析
1. 涨幅特征：分析涨幅分布规律，识别高涨幅股票的共同特点
2. 板块效应：分析申万板块的表现差异，找出表现最优的板块
3. 概念热点：识别当前市场最热门的概念，分析概念轮动趋势
4. 市值效应：分析不同市值股票的表现差异

## 🎯 投资洞察
1. 强势板块：哪些板块表现最突出？原因是什么？
2. 概念机会：哪些概念最值得关注？持续性如何？
3. 选股策略：基于数据总结出的选股规律和策略
4. 风险提示：需要注意的风险点和陷阱

## 📈 市场趋势判断
1. 资金偏好：当前资金更偏好什么类型的股票？
2. 热点轮动：概念和板块的轮动特征
3. 后市展望：基于当前数据对后市的判断

## 💡 实用建议
1. 优选标准：给出具体的选股标准和组合建议
2. 操作策略：提供具体的买卖时机和仓位建议
3. 风险控制：风险管理的具体措施
```

## ⚙️ 自定义配置

### API 参数调整
在 `gemini_stock_analyzer.py` 中可以调整：

```python
"generationConfig": {
    "temperature": 0.7,        # 创造性程度 (0-1)
    "topK": 40,               # 候选词数量
    "topP": 0.95,             # 核心采样概率
    "maxOutputTokens": 2048,   # 最大输出长度
}
```

### 分析股票数量
```python
# 修改分析的股票数量
analysis_data["前10名股票详情"] = rising_stocks[:10]  # 改为其他数量
```

## 🔧 故障排除

### 常见问题及解决方案

1. **API 密钥错误**
   ```
   ❌ 错误: 请设置 GEMINI_API_KEY 环境变量或提供 API 密钥
   ```
   **解决**: 检查 API 密钥是否正确，访问 [Google AI Studio](https://makersuite.google.com/app/apikey) 重新获取

2. **网络连接问题**
   ```
   ❌ API 调用异常: timeout
   ```
   **解决**: 检查网络连接，可能需要使用代理或 VPN

3. **数据文件不存在**
   ```
   ❌ 没有找到有效的股票数据
   ```
   **解决**: 确认 `output` 目录存在且包含正确格式的 CSV 文件

4. **依赖包缺失**
   ```
   ModuleNotFoundError: No module named 'requests'
   ```
   **解决**: 运行 `pip install requests pandas`

### API 限制说明
- Gemini API 有请求频率限制
- 免费版本有每日请求次数限制
- 单次请求的 token 数量有限制

## 📈 最佳实践

1. **定期分析**: 建议每日或每周运行，跟踪市场变化
2. **结果验证**: 将 AI 分析与实际市场表现对比
3. **策略调整**: 根据分析结果调整投资策略
4. **风险控制**: 严格按照 AI 建议的风险控制措施执行

## 🎯 使用场景

- **个人投资者**: 获得专业的股票分析和投资建议
- **投资机构**: 辅助投资决策和风险评估
- **研究人员**: 分析市场趋势和板块轮动
- **量化交易**: 为量化策略提供基本面分析支持

---

**⚠️ 免责声明**: 本工具仅供参考，不构成投资建议。投资有风险，决策需谨慎！

**🔗 相关链接**:
- [Google AI Studio](https://makersuite.google.com/app/apikey)
- [Gemini API 文档](https://ai.google.dev/docs)
- [项目 GitHub](https://github.com/your-repo)
