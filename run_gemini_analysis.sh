#!/bin/bash

# Gemini API 股票分析运行脚本
# 使用方法: ./run_gemini_analysis.sh [API_KEY]

echo "🚀 Gemini API 股票分析工具"
echo "=================================="

# 检查参数
if [ $# -eq 1 ]; then
    export GEMINI_API_KEY="$1"
    echo "🔑 使用提供的 API 密钥"
elif [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ 错误: 请提供 API 密钥"
    echo ""
    echo "使用方法:"
    echo "  方法1: ./run_gemini_analysis.sh YOUR_API_KEY"
    echo "  方法2: export GEMINI_API_KEY=YOUR_API_KEY && ./run_gemini_analysis.sh"
    echo ""
    echo "💡 获取 API 密钥: https://makersuite.google.com/app/apikey"
    exit 1
else
    echo "🔑 使用环境变量中的 API 密钥"
fi

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到 Python3"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import requests, pandas" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少依赖，正在安装..."
    pip3 install requests pandas
fi

# 检查数据目录
if [ ! -d "output" ]; then
    echo "❌ 错误: 未找到 output 目录"
    echo "💡 请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 测试 API 连接
echo "🧪 测试 API 连接..."
python3 test_gemini_api.py
if [ $? -ne 0 ]; then
    echo "❌ API 测试失败，请检查密钥和网络连接"
    exit 1
fi

echo ""
echo "✅ API 测试成功，开始分析..."
echo ""

# 运行分析
python3 gemini_stock_analyzer.py --input_dir output --output_dir analysis_reports

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 分析完成!"
    echo "📄 查看生成的 Markdown 报告文件"
    ls -la gemini_analysis_*.md 2>/dev/null | tail -1
else
    echo "❌ 分析失败"
    exit 1
fi
