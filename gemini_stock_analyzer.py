#!/usr/bin/env python3
"""
使用 Gemini API 进行股票趋势深度分析
"""

import os
import json
import requests
import pandas as pd
from datetime import datetime
import argparse

class GeminiStockAnalyzer:
    def __init__(self, api_key=None, output_dir="analysis_reports"):
        """
        初始化 Gemini 分析器

        Args:
            api_key: Gemini API 密钥，如果不提供则从环境变量获取
            output_dir: 输出目录路径
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("请设置 GEMINI_API_KEY 环境变量或提供 API 密钥")

        self.output_dir = output_dir
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")

        # 支持多个模型的回退机制
        self.models = [
            "gemini-2.0-flash",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
        ]
        self.base_url_template = "https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
    
    def load_stock_data(self, input_dir):
        """加载股票数据"""
        print("📊 正在加载股票数据...")
        
        # 查找CSV文件
        csv_files = []
        for date_folder in sorted(os.listdir(input_dir)):
            date_folder_path = os.path.join(input_dir, date_folder)
            if os.path.isdir(date_folder_path):
                csv_file_path = os.path.join(date_folder_path, "above_cloud_daily_change.csv")
                if os.path.isfile(csv_file_path):
                    csv_files.append((date_folder, csv_file_path))
        
        # 加载数据
        all_data = {}
        for date_str, file_path in csv_files:
            try:
                df = pd.read_csv(file_path, dtype={'股票代码': str})
                df['排名'] = range(1, len(df) + 1)
                all_data[date_str] = df
                print(f"  ✅ {date_str}: {len(df)} 只股票")
            except Exception as e:
                print(f"  ❌ {date_str}: 加载失败 - {e}")
        
        return all_data
    
    def extract_rising_stocks(self, all_data):
        """提取排名连续上升的股票数据"""
        print("🔍 正在分析排名上升股票...")
        
        dates = sorted(all_data.keys())
        rising_stocks = []
        
        # 简化的排名上升检测逻辑
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            
            current_df = all_data[current_date]
            next_df = all_data[next_date]
            
            # 确保股票代码为字符串
            current_df['股票代码'] = current_df['股票代码'].astype(str)
            next_df['股票代码'] = next_df['股票代码'].astype(str)
            
            # 找到两天都出现的股票
            common_stocks = set(current_df['股票代码']) & set(next_df['股票代码'])
            
            for stock_code in common_stocks:
                current_rank = current_df[current_df['股票代码'] == stock_code]['排名'].iloc[0]
                next_rank = next_df[next_df['股票代码'] == stock_code]['排名'].iloc[0]
                
                # 排名上升（数字变小）
                if next_rank < current_rank:
                    stock_info = next_df[next_df['股票代码'] == stock_code].iloc[0]
                    
                    rising_stocks.append({
                        'stock_code': stock_code,
                        'stock_name': stock_info.get('股票名称', '未知'),
                        'start_date': current_date,
                        'end_date': next_date,
                        'start_rank': current_rank,
                        'end_rank': next_rank,
                        'rank_improvement': current_rank - next_rank,
                        'total_gain': stock_info.get('当日涨幅(%)', 0),
                        'sector': stock_info.get('申万板块', '未知板块'),
                        'concepts': stock_info.get('开盘啦概念', ''),
                        'market_cap': stock_info.get('流通市值', 0)
                    })
        
        # 按涨幅排序
        rising_stocks.sort(key=lambda x: x['total_gain'], reverse=True)
        print(f"  📈 发现 {len(rising_stocks)} 只排名上升股票")
        
        return rising_stocks
    
    def format_data_for_analysis(self, rising_stocks):
        """格式化数据用于 AI 分析"""
        if not rising_stocks:
            return "没有发现排名上升的股票数据"
        
        # 构建分析数据
        analysis_data = {
            "总体统计": {
                "排名上升股票数量": len(rising_stocks),
                "平均涨幅": sum(stock['total_gain'] for stock in rising_stocks) / len(rising_stocks),
                "最大涨幅": max(stock['total_gain'] for stock in rising_stocks),
                "最小涨幅": min(stock['total_gain'] for stock in rising_stocks),
                "上涨股票数": len([s for s in rising_stocks if s['total_gain'] > 0])
            },
            "前10名股票详情": []
        }
        
        # 添加前10名股票详情
        for i, stock in enumerate(rising_stocks[:10], 1):
            analysis_data["前10名股票详情"].append({
                "排名": i,
                "股票代码": stock['stock_code'],
                "股票名称": stock['stock_name'],
                "申万板块": stock['sector'],
                "概念标签": stock['concepts'],
                "流通市值": f"{float(stock['market_cap']):.1f}亿" if stock['market_cap'] else "未知",
                "排名变化": f"从{stock['start_rank']}→{stock['end_rank']} (上升{stock['rank_improvement']}位)",
                "涨幅": f"{stock['total_gain']:.2f}%",
                "日期": f"{stock['start_date']}至{stock['end_date']}"
            })
        
        return json.dumps(analysis_data, ensure_ascii=False, indent=2)
    
    def create_analysis_prompt(self, data_str):
        """创建分析提示词"""
        prompt = f"""
你是一位资深的股票分析师，请基于以下排名连续上升股票的数据进行深度分析：

{data_str}

请从以下几个维度进行专业分析：

## 📊 **核心发现分析**
1. **涨幅特征**：分析涨幅分布规律，识别高涨幅股票的共同特点
2. **板块效应**：分析申万板块的表现差异，找出表现最优的板块
3. **概念热点**：识别当前市场最热门的概念，分析概念轮动趋势
4. **市值效应**：分析不同市值股票的表现差异

## 🎯 **投资洞察**
1. **强势板块**：哪些板块表现最突出？原因是什么？
2. **概念机会**：哪些概念最值得关注？持续性如何？
3. **选股策略**：基于数据总结出的选股规律和策略
4. **风险提示**：需要注意的风险点和陷阱

## 📈 **市场趋势判断**
1. **资金偏好**：当前资金更偏好什么类型的股票？
2. **热点轮动**：概念和板块的轮动特征
3. **后市展望**：基于当前数据对后市的判断

## 💡 **实用建议**
1. **优选标准**：给出具体的选股标准和组合建议
2. **操作策略**：提供具体的买卖时机和仓位建议
3. **风险控制**：风险管理的具体措施

请用专业、简洁的语言进行分析，重点突出数据背后的投资逻辑和实用价值。使用适当的emoji让分析更生动易读。
"""
        return prompt
    
    def call_gemini_api(self, prompt):
        """调用 Gemini API，支持多模型回退"""
        print("🤖 正在调用 Gemini API 进行分析...")

        headers = {
            'Content-Type': 'application/json',
        }

        data = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048,
            }
        }

        # 尝试不同的模型
        for i, model in enumerate(self.models):
            try:
                url = self.base_url_template.format(model=model)
                print(f"  🔄 尝试模型: {model}")

                response = requests.post(
                    f"{url}?key={self.api_key}",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and len(result['candidates']) > 0:
                        print(f"  ✅ 模型 {model} 调用成功!")
                        return result['candidates'][0]['content']['parts'][0]['text']
                    else:
                        print(f"  ⚠️ 模型 {model} 返回空结果")
                        continue
                elif response.status_code == 404:
                    print(f"  ❌ 模型 {model} 不可用 (404)")
                    continue
                else:
                    print(f"  ❌ 模型 {model} 调用失败: {response.status_code}")
                    if i == len(self.models) - 1:  # 最后一个模型也失败了
                        return f"所有模型调用失败。最后错误: {response.status_code} - {response.text}"
                    continue

            except requests.exceptions.Timeout:
                print(f"  ⏰ 模型 {model} 请求超时")
                if i == len(self.models) - 1:
                    return "所有模型请求超时，请检查网络连接"
                continue
            except Exception as e:
                print(f"  ❌ 模型 {model} 异常: {str(e)}")
                if i == len(self.models) - 1:
                    return f"所有模型调用异常。最后错误: {str(e)}"
                continue

        return "所有可用模型都调用失败"
    
    def analyze(self, input_dir):
        """执行完整的分析流程"""
        print("🚀 开始股票趋势 AI 分析")
        print("=" * 60)
        
        # 1. 加载数据
        all_data = self.load_stock_data(input_dir)
        if not all_data:
            print("❌ 没有找到有效的股票数据")
            return
        
        # 2. 提取排名上升股票
        rising_stocks = self.extract_rising_stocks(all_data)
        if not rising_stocks:
            print("❌ 没有发现排名上升的股票")
            return
        
        # 3. 格式化数据
        data_str = self.format_data_for_analysis(rising_stocks)
        
        # 4. 创建分析提示词
        prompt = self.create_analysis_prompt(data_str)
        
        # 5. 调用 AI 分析
        analysis_result = self.call_gemini_api(prompt)
        
        # 6. 输出结果
        print("\n" + "=" * 60)
        print("🎯 Gemini AI 分析结果")
        print("=" * 60)
        print(analysis_result)
        
        # 7. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(self.output_dir, f"gemini_analysis_{timestamp}.md")

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 股票趋势 AI 分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**数据源**: {input_dir}\n\n")
            f.write(f"**分析股票数**: {len(rising_stocks)}\n\n")
            f.write("---\n\n")
            f.write(analysis_result)

        print(f"\n📄 分析报告已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="使用 Gemini AI 分析股票趋势")
    parser.add_argument("--input_dir", type=str, required=True,
                       help="包含股票数据的目录路径")
    parser.add_argument("--output_dir", type=str, default="analysis_reports",
                       help="输出目录路径 (默认: analysis_reports)")
    parser.add_argument("--api_key", type=str,
                       help="Gemini API 密钥 (也可通过环境变量 GEMINI_API_KEY 设置)")

    args = parser.parse_args()

    try:
        analyzer = GeminiStockAnalyzer(api_key=args.api_key, output_dir=args.output_dir)
        analyzer.analyze(args.input_dir)
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
