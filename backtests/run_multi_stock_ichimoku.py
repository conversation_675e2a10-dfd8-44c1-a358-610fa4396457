# run_multi_stock_ichimoku.py
import pandas as pd
from src.backtest.ichimoku_backtest import IchimokuBacktester
from src.utils.logger import Logger # 假设 Logger 已配置好
import traceback

def run_multiple_backtests(stock_list, data_source='tushare', period='2y', interval='1d', hold_days=10, initial_capital=100000, position_sizing_ratio=0.1, signal_days=30):
    """
    对多个股票执行 Ichimoku 回测。
    """
    all_results = []
    Logger.info(f"开始对 {len(stock_list)} 个股票进行 Ichimoku 回测...")

    for stock_code in stock_list:
        Logger.info("-" * 30)
        Logger.info(f"开始处理股票: {stock_code}")
        try:
            backtester = IchimokuBacktester(data_source=data_source)

            # 1. 获取数据
            df = backtester.get_history_data(stock_code, period=period, interval=interval)
            if df is None or df.empty:
                Logger.warning(f"跳过股票 {stock_code}: 获取数据失败或数据为空")
                all_results.append({'stock_code': stock_code, 'error': 'Failed to get data or data is empty'})
                continue

            # 2. 计算指标
            if not backtester.calculate_indicators():
                Logger.warning(f"跳过股票 {stock_code}: 指标计算失败")
                all_results.append({'stock_code': stock_code, 'error': 'Indicator calculation failed'})
                continue

            # 3. 生成信号
            if not backtester.generate_signals(days=signal_days):
                Logger.warning(f"跳过股票 {stock_code}: 信号生成失败")
                all_results.append({'stock_code': stock_code, 'error': 'Signal generation failed'})
                continue

            # 4. 执行回测
            trades = backtester.run_backtest(hold_days=hold_days, initial_capital=initial_capital, position_sizing_ratio=position_sizing_ratio)
            if trades is None:
                Logger.warning(f"跳过股票 {stock_code}: 回测执行失败")
                all_results.append({'stock_code': stock_code, 'error': 'Backtest execution failed'})
                continue

            # 5. 计算绩效
            performance = backtester.calculate_performance(initial_capital=initial_capital)
            if performance is None:
                 Logger.warning(f"股票 {stock_code}: 绩效计算失败")
                 # 即使绩效计算失败，也可能记录一些基本信息
                 performance = {'stock_code': stock_code, 'error': 'Performance calculation failed'}
            elif not performance: # 空字典表示无交易
                 Logger.info(f"股票 {stock_code}: 回测期间无交易")
                 performance = {'stock_code': stock_code, 'total_trades': 0, 'total_return_pct': 0.0} # 记录无交易状态
            else:
                 performance['stock_code'] = stock_code # 添加股票代码字段

            all_results.append(performance)
            Logger.info(f"股票 {stock_code} 处理完成。")

        except Exception as e:
            Logger.error(f"处理股票 {stock_code} 时发生意外错误: {str(e)}")
            Logger.error(traceback.format_exc())
            all_results.append({'stock_code': stock_code, 'error': str(e)}) # 记录错误

    Logger.info(f"所有股票处理完毕。共获得 {len(all_results)} 条结果。")
    return all_results

def analyze_results(results):
    """
    分析和展示回测结果。
    """
    if not results:
        Logger.info("没有有效的回测结果可供分析。")
        return

    # 过滤掉包含错误的记录，单独处理
    valid_results = [r for r in results if 'error' not in r and r.get('total_trades', -1) != -1] # 确保有 total_trades 键
    error_results = [r for r in results if 'error' in r or r.get('total_trades', -1) == -1]

    if error_results:
        Logger.warning(f"\n以下 {len(error_results)} 个股票在处理过程中遇到问题:")
        for res in error_results:
            Logger.warning(f"- {res['stock_code']}: {res.get('error', 'Unknown issue or performance calc failed')}")

    if not valid_results:
        Logger.info("没有成功的有效回测结果可供分析。")
        return

    # 将有效结果转换为 DataFrame
    results_df = pd.DataFrame(valid_results)

    # 移除 equity_curve 列，因为它可能很大且不适合直接显示在表格中
    if 'equity_curve' in results_df.columns:
        results_df = results_df.drop(columns=['equity_curve'])

    # 选择一些关键指标进行展示
    key_metrics = [
        'stock_code', 'total_trades', 'win_rate_pct', 'total_return_pct',
        'profit_factor', 'avg_win_loss_ratio', 'max_drawdown_pct',
        'avg_hold_days'
    ]
    # 确保所有关键指标都存在于DataFrame中，不存在的用 NaN 填充
    for col in key_metrics:
        if col not in results_df.columns:
            results_df[col] = pd.NA

    results_df = results_df[key_metrics]


    # 按总回报率排序
    results_df_sorted = results_df.sort_values(by='total_return_pct', ascending=False).reset_index(drop=True)

    Logger.info("\n=== 多股票回测结果汇总 (按总回报率排序) ===")
    # 调整 Pandas 显示选项
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)
    pd.set_option('display.colheader_justify', 'center')
    pd.set_option('display.precision', 2) # 设置浮点数精度

    # 打印格式化后的 DataFrame
    print(results_df_sorted.to_string(index=False, float_format='%.2f')) # 使用 to_string 获得更好的控制台输出

    # (可选) 保存到文件
    try:
        output_filename = 'multi_stock_ichimoku_results.csv'
        results_df_sorted.to_csv(output_filename, index=False, encoding='utf-8-sig')
        Logger.info(f"\n详细结果已保存到: {output_filename}")
    except Exception as e:
        Logger.error(f"保存结果到 CSV 文件失败: {e}")


if __name__ == "__main__":
    # --- 配置区 ---
    stocks_to_backtest = [
        '600360.SH', # 示例股票1
        '000001.SZ', # 示例股票2 (平安银行)
        '600519.SH', # 示例股票3 (贵州茅台)
        '000651.SZ', # 示例股票4 (格力电器)
        'invalid_code', # 测试错误处理
        '601318.SH'  # 示例股票5 (中国平安)
        # ... 添加更多股票代码
    ]
    backtest_parameters = {
        'data_source': 'tushare',
        'period': '2y',
        'interval': '1d',
        'hold_days': 15,         # 修改持仓天数示例
        'initial_capital': 100000,
        'position_sizing_ratio': 0.1,
        'signal_days': 30        # Ichimoku 信号生成窗口
    }
    # --- 执行区 ---
    results = run_multiple_backtests(stocks_to_backtest, **backtest_parameters)

    # --- 分析区 ---
    analyze_results(results)