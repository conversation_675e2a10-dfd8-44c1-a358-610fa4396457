# backtests/oos_test_low_vol_breakout.py
# Script for Out-of-Sample (OOS) testing of the Low Volume Breakout strategy
# using the best parameters found during optimization.
import pandas as pd
import numpy as np
import itertools
import time
import os
import traceback
from multiprocessing import Pool, cpu_count
import sys
# import multiprocessing as mp # For spawn context if needed

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# --- Module Imports ---
try:
    # Import the new backtester
    from src.backtest.low_vol_breakout_backtest import LowVolBreakoutBacktester
    # Import utility modules
    from src.utils.logger import Logger
    from src.data_providers.stock_data_provider import StockDataProviderFactory # Use the factory
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure the script is run from the project root directory or paths are correctly configured.")
    exit(1)

# --- Configuration Section ---

# 1. Define the stocks to use for OOS testing
#    Should ideally be the same list used for optimization, or a relevant subset/superset.
OOS_STOCK_LIST = [
    '600360.SH',
    '000025.SZ', '002194.SZ', '600622.SH', '000158.SZ', '600961.SH', '601162.SH', '000797.SZ', '600250.SH', '300204.SZ', '603002.SH', '002162.SZ', '002985.SZ', '300805.SZ', '601007.SH', '300505.SZ', '601068.SH', '300530.SZ', '600187.SH', '000428.SZ', '605188.SH', '300401.SZ', '600498.SH', '603677.SH', '300348.SZ', '300541.SZ', '300465.SZ', '300841.SZ', '601156.SH', '002039.SZ', '600868.SH', '002324.SZ', '300986.SZ', '002555.SZ', '002827.SZ', '000032.SZ', '002255.SZ', '002987.SZ', '002734.SZ', '002138.SZ', '600053.SH', '601010.SH', '605009.SH', '600060.SH', '002284.SZ', '002843.SZ', '002501.SZ', '600707.SH', '002897.SZ', '600105.SH', '300558.SZ', '300674.SZ', '601717.SH', '002773.SZ', '603626.SH', '002383.SZ', '600751.SH', '300136.SZ', '603766.SH', '000417.SZ', '300386.SZ', '603583.SH', '600793.SH', '603127.SH', '000628.SZ', '002803.SZ', '002693.SZ', '603338.SH', '002093.SZ', '002276.SZ', '300866.SZ', '300191.SZ', '300533.SZ', '600711.SH', '002204.SZ', '601608.SH', '600177.SH', '300803.SZ', '601456.SH', '002588.SZ', '300035.SZ', '600326.SH', '000859.SZ', '300627.SZ', '300810.SZ', '300065.SZ', '601890.SH', '600320.SH', '600611.SH', '300008.SZ', '000065.SZ', '002201.SZ', '603228.SH', '603800.SH', '002277.SZ', '600601.SH', '002008.SZ', '603220.SH', '002792.SZ', '002195.SZ', '002342.SZ'
]
# *** USER ACTION REQUIRED: Verify this stock list is appropriate for OOS testing ***

# 2. Define the BEST parameter set identified from optimization
#    *** USER ACTION REQUIRED: Update these parameters based on your optimization results ***
BEST_PARAMS = {
    'volatility_period': 10,             # Updated based on limited snippet analysis
    'volatility_history': 60,            # Kept fixed from original grid
    'volatility_quantile': 0.2,            # Updated based on limited snippet analysis
    'volume_threshold_multiplier': 1.5,    # Updated based on limited snippet analysis
    'volume_short_ma': 5,                # Kept fixed from original grid
    'volume_long_ma': 20,                # Kept fixed from original grid
    'bbands_period': 20,                 # Kept fixed from original grid
    'bbands_stddev': 2,                  # Kept fixed from original grid
    'ma_long_period': 30,                # Updated based on limited snippet analysis
    'rsi_period': 14,                    # Kept fixed from original grid
    'rsi_threshold': 50,                 # Updated based on limited snippet analysis
    'hold_days': 7,                      # Updated based on limited snippet analysis
    'position_sizing_ratio': 0.3,        # Updated based on limited snippet analysis
    'use_rsi_confirm': False             # Updated based on limited snippet analysis
}
# WARNING: These parameters were derived from an incomplete, unsorted log snippet.
# It is highly recommended to use the actual best parameters from the full, sorted optimization results.
# We will run only this single combination for OOS test
PARAM_GRID = {k: [v] for k, v in BEST_PARAMS.items()} # Create a grid with single values

# 3. Define fixed parameters and the OOS period
#    *** USER ACTION REQUIRED: Define the Out-of-Sample date range ***
FIXED_PARAMS = {
    'data_source': 'tushare', # Or 'yfinance'
    'start_date': '20220424', # OOS Start Date (e.g., day after optimization end)
    'end_date': '20240423',   # OOS End Date (e.g., today or a recent date)
    'interval': '1d',
    'initial_capital': 100000,
    # Add any other fixed parameters needed by the backtester if not in PARAM_GRID
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_signal': 9,
}

# 4. Define how to aggregate results across stocks for the single parameter set
def aggregate_performance(stock_performances):
    """
    Aggregates performance metrics from multiple stocks for a single parameter set.
    Filters out None or error results automatically.
    """
    valid_performances = [p for p in stock_performances if p and 'error' not in p and p.get('total_trades', 0) > 0]

    num_valid = len(valid_performances)
    num_total = len(stock_performances)
    num_no_trades = len([p for p in stock_performances if p and 'error' not in p and p.get('total_trades', 0) == 0])
    num_errors = len([p for p in stock_performances if p and 'error' in p])

    if not valid_performances:
        return {
            'avg_total_return_pct': None,
            'avg_win_rate_pct': None,
            'avg_profit_factor': None,
            'avg_max_drawdown_pct': None,
            'avg_sharpe_ratio': None, # Added Sharpe
            'num_valid_stocks': num_valid,
            'num_no_trade_stocks': num_no_trades,
            'num_error_stocks': num_errors,
            'num_total_stocks': num_total
        }

    perf_df = pd.DataFrame(valid_performances)

    # Calculate mean of key metrics - handle potential NaN/inf values
    aggregated = {
        'avg_total_return_pct': pd.to_numeric(perf_df['total_return_pct'], errors='coerce').mean(),
        'avg_win_rate_pct': pd.to_numeric(perf_df['win_rate_pct'], errors='coerce').mean(),
        'avg_profit_factor': pd.to_numeric(perf_df['profit_factor'].replace([np.inf, -np.inf], np.nan), errors='coerce').mean(),
        'avg_max_drawdown_pct': pd.to_numeric(perf_df['max_drawdown_pct'], errors='coerce').mean(),
        'avg_sharpe_ratio': pd.to_numeric(perf_df['sharpe_ratio'], errors='coerce').mean(), # Added Sharpe
        'num_valid_stocks': num_valid,
        'num_no_trade_stocks': num_no_trades,
        'num_error_stocks': num_errors,
        'num_total_stocks': num_total
    }
    return aggregated

# 5. Define the metric to display (not for sorting, as there's only one result)
DISPLAY_METRIC = 'avg_sharpe_ratio' # Or any other relevant metric

# 6. Multiprocessing configuration (usually 1 for OOS)
NUM_PROCESSES = 1 # Force single process for OOS test

# 7. Output directory and file name base for OOS test
OUTPUT_DIR = 'backtests/optimization_results/low_vol_breakout'
OUTPUT_FILENAME_BASE = 'low_vol_breakout_OOS_test_results' # Specific filename for OOS results

# --- Data Fetching Function ---
def fetch_all_stock_data(stock_list, data_source, data_fetch_params, rate_limit_interval=0.4):
    """
    Fetches historical data for all stocks using StockDataProvider.
    Includes rate limiting.
    """
    stock_data_cache = {}
    print(f"Fetching data for {len(stock_list)} stocks using {data_source} for OOS period...")
    data_provider = StockDataProviderFactory.get_provider(data_source)
    fetch_start_time = time.time()

    for i, stock_code in enumerate(stock_list):
        print(f"  Fetching {stock_code} ({i+1}/{len(stock_list)})...", end='', flush=True)
        try:
            df = data_provider.get_stock_data_by_date(
                stock_code,
                data_fetch_params['start_date'],
                data_fetch_params['end_date']
            )
            if df is not None and not df.empty:
                df.columns = [col.lower() for col in df.columns]
                if not isinstance(df.index, pd.DatetimeIndex):
                    df.index = pd.to_datetime(df.index)
                df.sort_index(inplace=True)
                stock_data_cache[stock_code] = df
                print(" Done.")
            else:
                print(" Failed (No data).")
                Logger.warning(f"No data fetched for {stock_code}")
        except Exception as e:
            print(f" Failed (Error: {e}).")
            Logger.error(f"Error fetching data for {stock_code}: {e}")

    fetch_end_time = time.time()
    print(f"Data fetching complete. Fetched data for {len(stock_data_cache)} out of {len(stock_list)} stocks.")
    print(f"Total data fetching time: {fetch_end_time - fetch_start_time:.2f} seconds.")
    print("-" * 30)
    return stock_data_cache


# --- Worker Function (Adapted for single parameter set) ---
def run_backtest_for_params_worker(args):
    """
    Worker function for LowVolBreakoutBacktester using cached data for OOS test.
    """
    param_combination, stock_list, stock_data_cache, fixed_params_global, combo_index, total_combos = args
    param_id_str = "; ".join(f"{k}={v}" for k, v in sorted(param_combination.items()))
    print(f"[Worker-{os.getpid()}] Running OOS test with Params: {param_id_str}", flush=True)

    individual_stock_results = []
    current_run_params = {**fixed_params_global, **param_combination}

    single_run_config = {
        'initial_capital': current_run_params.get('initial_capital'),
        'position_sizing_ratio': current_run_params.get('position_sizing_ratio'),
        'hold_days': current_run_params.get('hold_days'),
        'use_rsi_confirm': current_run_params.get('use_rsi_confirm'),
        'params': {k: v for k, v in current_run_params.items() if k not in [
            'initial_capital', 'position_sizing_ratio', 'hold_days', 'use_rsi_confirm',
            'data_source', 'start_date', 'end_date', 'interval' # Adjusted keys
        ]}
    }
    # Add fixed MACD params if they exist
    if 'macd_fast' in fixed_params_global: single_run_config['params']['macd_fast'] = fixed_params_global['macd_fast']
    if 'macd_slow' in fixed_params_global: single_run_config['params']['macd_slow'] = fixed_params_global['macd_slow']
    if 'macd_signal' in fixed_params_global: single_run_config['params']['macd_signal'] = fixed_params_global['macd_signal']

    try:
        stock_backtester = LowVolBreakoutBacktester(data_source=current_run_params['data_source'])
    except Exception as init_e:
        Logger.error(f"[Worker-{os.getpid()}] Failed to initialize backtester: {init_e}")
        error_result = {'error': f'Backtester init failed: {init_e}'}
        aggregated_result = aggregate_performance([error_result] * len(stock_list))
        return {**param_combination, **aggregated_result}


    for stock_code in stock_list:
        stock_result = {'stock_code': stock_code}
        df_cached = stock_data_cache.get(stock_code)

        if df_cached is None or df_cached.empty:
            stock_result['error'] = 'No cached data available for OOS period'
            individual_stock_results.append(stock_result)
            continue

        try:
            stock_backtester.df = df_cached.copy()
            stock_backtester.set_parameters(single_run_config['params'])

            if not stock_backtester.calculate_indicators():
                 stock_result['error'] = 'Indicator calculation failed'
                 individual_stock_results.append(stock_result)
                 continue

            stock_backtester.params['hold_days'] = single_run_config['hold_days']
            if not stock_backtester.generate_signals(use_rsi_confirm=single_run_config['use_rsi_confirm']):
                 stock_result['error'] = 'Signal generation failed'
                 individual_stock_results.append(stock_result)
                 continue

            trades = stock_backtester.run_backtest(
                initial_capital=single_run_config['initial_capital'],
                position_sizing_ratio=single_run_config['position_sizing_ratio']
            )

            if trades is None:
                 stock_result['error'] = 'Backtest execution failed'
                 max_drawdown = 0.0
                 if stock_backtester.df is not None and 'portfolio_value' in stock_backtester.df.columns:
                     portfolio_values = stock_backtester.df['portfolio_value']
                     rolling_max = portfolio_values.cummax()
                     drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan)
                     max_drawdown = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0
                 stock_result['max_drawdown_pct'] = max_drawdown
                 stock_result['total_trades'] = 0
                 stock_result['total_return_pct'] = (stock_backtester.df['portfolio_value'].iloc[-1] / single_run_config['initial_capital'] - 1) * 100 if stock_backtester.df is not None and not stock_backtester.df.empty else 0.0
                 individual_stock_results.append(stock_result)
                 continue

            performance = stock_backtester.calculate_performance(initial_capital=single_run_config['initial_capital'])

            if performance is None:
                 stock_result['error'] = 'Performance calculation failed'
                 max_drawdown = 0.0
                 if stock_backtester.df is not None and 'portfolio_value' in stock_backtester.df.columns:
                     portfolio_values = stock_backtester.df['portfolio_value']
                     rolling_max = portfolio_values.cummax()
                     drawdown = (portfolio_values - rolling_max) / rolling_max.replace(0, np.nan)
                     max_drawdown = abs(drawdown.min()) * 100 if drawdown.notna().any() else 0.0
                 stock_result['max_drawdown_pct'] = max_drawdown
                 stock_result['total_trades'] = len(trades) if trades is not None else 0
                 stock_result['total_return_pct'] = (stock_backtester.df['portfolio_value'].iloc[-1] / single_run_config['initial_capital'] - 1) * 100 if stock_backtester.df is not None and not stock_backtester.df.empty else 0.0
                 individual_stock_results.append(stock_result)
                 continue

            stock_result.update(performance)
            individual_stock_results.append(stock_result)

        except Exception as e:
            Logger.error(f"[Worker-{os.getpid()}] Error processing {stock_code} with params {param_id_str}: {e}")
            Logger.error(traceback.format_exc())
            stock_result['error'] = str(e)
            individual_stock_results.append(stock_result)

    aggregated_result = aggregate_performance(individual_stock_results)
    return {**param_combination, **aggregated_result}


# --- Main Execution ---
if __name__ == "__main__":
    try:
        Logger.setup_logging(log_level='INFO')
        Logger.info("Logger setup complete for OOS test script.")
    except AttributeError:
        print("Warning: Could not explicitly call Logger.setup_logging(). Assuming logger is pre-configured.")
        import logging
        logging.basicConfig(level=logging.INFO)
        Logger.info = logging.info
        Logger.error = logging.error
        Logger.warning = logging.warning
        Logger.debug = logging.debug

    Logger.info("Starting Low Volume Breakout Out-of-Sample (OOS) Test...")

    start_time = time.time()

    # 1. Parameter combination (only the best one)
    param_names = PARAM_GRID.keys()
    param_values = PARAM_GRID.values()
    all_param_combinations = [dict(zip(param_names, v)) for v in itertools.product(*param_values)]
    num_combinations = len(all_param_combinations) # Should be 1

    if num_combinations != 1:
         Logger.warning(f"Expected 1 parameter combination for OOS test, but got {num_combinations}. Using the first one.")
         if num_combinations == 0:
             Logger.error("No parameter combination found for OOS test. Check BEST_PARAMS.")
             print("ERROR: No parameter combination found for OOS test. Check BEST_PARAMS.")
             exit(1)
         all_param_combinations = all_param_combinations[:1] # Use only the first one

    print(f"Running OOS test with 1 parameter combination:")
    print(f"  Parameters: {all_param_combinations[0]}")
    Logger.info(f"OOS Test Parameters: {all_param_combinations[0]}")
    print(f"Using stock list: {OOS_STOCK_LIST}")
    Logger.info(f"OOS Stock list: {OOS_STOCK_LIST}")
    print(f"Fixed parameters (including OOS period): {FIXED_PARAMS}")
    Logger.info(f"OOS Fixed parameters: {FIXED_PARAMS}")
    print(f"Using {NUM_PROCESSES} process(es).")
    Logger.info(f"Using {NUM_PROCESSES} process(es).")

    output_csv_path = os.path.join(OUTPUT_DIR, f"{OUTPUT_FILENAME_BASE}.csv")
    output_html_path = os.path.join(OUTPUT_DIR, f"{OUTPUT_FILENAME_BASE}.html")
    print(f"Output directory: {OUTPUT_DIR}")
    print(f"Output CSV file: {output_csv_path}")
    print(f"Output HTML file: {output_html_path}")
    Logger.info(f"Output directory: {OUTPUT_DIR}")
    Logger.info(f"Output CSV: {output_csv_path}")
    Logger.info(f"Output HTML: {output_html_path}")
    print("-" * 30)

    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        print(f"Ensured output directory exists: {OUTPUT_DIR}")
    except OSError as e:
        Logger.error(f"Could not create output directory {OUTPUT_DIR}: {e}")
        print(f"ERROR: Could not create output directory {OUTPUT_DIR}: {e}")
        exit(1)

    # 2. Pre-fetch all stock data for the OOS period
    data_fetch_params_oos = {k: v for k, v in FIXED_PARAMS.items() if k in ['start_date', 'end_date', 'interval']}
    rate_limit = 0.5 if FIXED_PARAMS.get('data_source') == 'tushare' else 0.1
    stock_data_cache = fetch_all_stock_data(
        OOS_STOCK_LIST,
        FIXED_PARAMS.get('data_source', 'tushare'),
        data_fetch_params_oos,
        rate_limit_interval=rate_limit
    )

    if not stock_data_cache:
        Logger.error("No stock data could be fetched for OOS period. Exiting test.")
        print("ERROR: No stock data could be fetched for OOS period. Exiting test.")
        exit(1)
    print(f"Proceeding with OOS test using data for {len(stock_data_cache)} stocks.")
    Logger.info(f"Proceeding with OOS test using data for {len(stock_data_cache)} stocks.")
    print("-" * 30)


    # 3. Prepare arguments for worker function
    worker_args = [
        (combo, OOS_STOCK_LIST, stock_data_cache, FIXED_PARAMS, i, num_combinations)
        for i, combo in enumerate(all_param_combinations)
    ]


    # 4. Run the single backtest
    oos_results = []
    print("Running OOS backtest...")
    Logger.info("Starting OOS backtest execution.")
    try:
        # No need for pool if NUM_PROCESSES is 1
        result = run_backtest_for_params_worker(worker_args[0])
        oos_results.append(result)
    except Exception as e:
         param_id_str = "; ".join(f"{k}={v}" for k, v in sorted(all_param_combinations[0].items()))
         Logger.error(f"Unhandled exception in OOS run for params {param_id_str}: {e}")
         Logger.error(traceback.format_exc())
         print(f"ERROR: Unhandled exception in OOS run for params {param_id_str}: {e}")
         print(traceback.format_exc())
         oos_results.append({**all_param_combinations[0], 'error': 'Unhandled exception in worker'})


    # 5. Process and display OOS results
    print("-" * 30)
    print("OOS test complete. Processing results...")
    Logger.info("OOS test complete. Processing results.")

    if not oos_results:
        Logger.error("No results were generated during the OOS test.")
        print("ERROR: No results were generated during the OOS test.")
    else:
        results_df = pd.DataFrame(oos_results)

        # Clean up results - ensure numeric types for metric columns
        metric_cols = ['avg_total_return_pct', 'avg_win_rate_pct', 'avg_profit_factor',
                       'avg_max_drawdown_pct', 'avg_sharpe_ratio']
        for col in metric_cols:
            if col in results_df.columns:
                results_df[col] = pd.to_numeric(results_df[col], errors='coerce')

        print("\n=== Out-of-Sample (OOS) Test Results ===")
        pd.set_option('display.max_rows', None)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', 200)
        pd.set_option('display.precision', 4)
        print(results_df.to_string(index=False))

        # Save results
        try:
            results_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
            print(f"\nOOS test results saved to CSV: {output_csv_path}")
            Logger.info(f"OOS results saved to CSV: {output_csv_path}")

            # Save to HTML
            try:
                html_content = results_df.to_html(index=False, border=1, classes='dataframe table table-striped table-hover', justify='center', na_rep='NaN')
                full_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低位放量突破策略 - 样本外测试结果</title>
    <style>
        body {{ font-family: sans-serif; padding: 20px; }}
        h1 {{ text-align: center; }}
        .dataframe {{ border-collapse: collapse; width: 100%; margin: 20px auto; font-size: 0.9em; border: 1px solid #ccc; }}
        .dataframe th, .dataframe td {{ text-align: center; padding: 8px 12px; border: 1px solid #ddd; }}
        .dataframe thead th {{ background-color: #f2f2f2; font-weight: bold; position: sticky; top: 0; z-index: 1; }}
        .dataframe tbody tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .dataframe tbody tr:hover {{ background-color: #f1f1f1; }}
        .table-container {{ overflow-x: auto; max-height: 80vh; overflow-y: auto; }} /* Scrollable container */
    </style>
</head>
<body>
    <h1>低位放量突破策略 - 样本外测试结果</h1>
    <p>测试参数: {all_param_combinations[0]}</p>
    <div class="table-container">
        {html_content}
    </div>
</body>
</html>
"""
                with open(output_html_path, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                print(f"OOS test results also saved to HTML report: {output_html_path}")
                Logger.info(f"OOS results saved to HTML: {output_html_path}")
            except Exception as e:
                Logger.error(f"Failed to save OOS results to HTML ({output_html_path}): {e}")
                print(f"ERROR: Failed to save OOS results to HTML ({output_html_path}): {e}")

        except Exception as e:
            Logger.error(f"Failed to save OOS results to CSV ({output_csv_path}): {e}")
            print(f"ERROR: Failed to save OOS results to CSV ({output_csv_path}): {e}")

        # Display result details
        if not results_df.empty:
             oos_result_dict = results_df.iloc[0].to_dict()
             print("\n--- OOS Test Performance Details ---")
             Logger.info("OOS Test Performance Details:")
             print("Parameters Used:")
             oos_params_only = {k: v for k, v in oos_result_dict.items() if k in PARAM_GRID}
             for key, value in oos_params_only.items():
                 print(f"  {key}: {value}")
                 Logger.info(f"  {key}: {value}")
             print("Aggregated Performance:")
             Logger.info("Aggregated Performance:")
             for key, value in oos_result_dict.items():
                 if key not in PARAM_GRID and key != 'error': # Show performance metrics
                     if isinstance(value, (float, np.floating)):
                         print(f"  {key}: {value:.4f}")
                         Logger.info(f"  {key}: {value:.4f}")
                     else:
                         print(f"  {key}: {value}")
                         Logger.info(f"  {key}: {value}")
        else:
             print("\nCould not display OOS result details (no valid results).")
             Logger.warning("Could not display OOS result details.")


    end_time = time.time()
    total_time = end_time - start_time
    print("-" * 30)
    print(f"Total OOS test time: {total_time:.2f} seconds.")
    Logger.info(f"Total OOS test time: {total_time:.2f} seconds.")