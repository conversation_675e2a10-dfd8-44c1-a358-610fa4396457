# optimize_ichimoku_params.py
import pandas as pd
import itertools
import time
from multiprocessing import Pool, cpu_count
import traceback
import os

# 配置输出路径
OUTPUT_BASE_DIR = os.path.join('output', 'backtests', 'ichimoku')
OPTIMIZATION_RESULTS_DIR = os.path.join(OUTPUT_BASE_DIR, 'optimization_results')
LOGS_DIR = os.path.join(OUTPUT_BASE_DIR, 'logs')

# 确保输出目录存在
os.makedirs(OPTIMIZATION_RESULTS_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# Assuming these modules exist and are correctly set up
# Ensure paths are correct if running from a different directory
try:
    from src.backtest.ichimoku_backtest import IchimokuBacktester
    from src.utils.logger import Logger # Make sure Logger is configured appropriately
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure the script is run from the project root directory or paths are correctly configured.")
    exit(1)

# --- Configuration Section ---

# 1. Define the stocks to use for optimization
#    Using a smaller, representative set might be faster for initial optimization
OPTIMIZATION_STOCK_LIST = [
    "002048",
    "002527",
    "300573",
    "002530",
    "603985",
    "300758",
    "301069",
    "002376",
    "300879",
    "600828",
    "002821",
    "300119",
    "300622",
    "600114",
    "300253",
    "300229",
    "603129",
    "300328",
    "000581",
    "000034",
    "600315",
    "603666",
    "603686",
    "603108",
    "002851",
    "002891",
    "002465",
    "002568",
    "002222",
    "603379",
    "300652",
    "600143",
    "300777",
    "300451",
    "600456",
    "600120",
    "002033",
    "001287",
    "300005",
    "000062",
    "002414",
    "600889",
    "000690",
    "601997",
    "603882",
    "601177",
    "301000",
    "603809",
    "003021",
    "301498",
    "000837",
    "300972",
    "300888",
    "002683",
    "003010",
    "002324",
    "605188",
    "600711",
    "002553",
    "300185",
    "600008",
    "002643",
    "600812",
    "002229",
    "605488",
    "002827",
    "002284",
    "600521",
    "600326",
    "603127",
    "300415",
    "002126",
    "001696",
    "300458",
    "301160",
    "300918",
    "300403",
    "002292",
    "300718",
    "002009",
    "603662",
    "002747",
    "603236",
    "300496",
    "301368",
    "600879",
    "300432",
    "603667",
    "301413",
    "300007",
    "002965",
    "002472",
    "300258",
    "300098",
    "300660",
    "002979",
    "300580",
    "603319",
    "300503",
    "600699",
    "301510",
    "300680",
    "002383",
    "002501",
    "600320",
    "300008",
    "002276",
    "002572",
    "603883",
    "600258",
    "002655",
    "603629",
    "600777",
    "600794",
    "300083",
    "603612",
    "300677",
    "601137",
    "000887",
    "300245",
    "603707",
    "600429",
    "600718",
    "002434",
    "002046",
    "300019",
    "002326",
    "603179",
    "300611",
    "300842",
    "002642",
    "603915",
    "300115",
    "600100",
    "000981",
    "002765",
    "601188",
    "002526",
    "605088",
    "300397",
    "002428",
    "300353",
    "300493",
    "002345",
    "300347",
    "002131",
    "300674",
    "300065",
    "300054",
    "002402",
    "300708",
    "000927",
    "600704",
    "300067",
    "301358",
    "002837",
    "301301",
    "002603",
    "601000",
    "601717",
    "300779",
    "300699",
    "002936",
    "300261",
    "601020",
    "300360",
    "603697",
    "300223",
    "600141",
    "301526",
    "002757",
    "300271",
    "603899",
    "300763",
    "603579",
    "600498",
    "300035",
    "600808",
    "603938",
    "000921",
    "003000",
    "600158",
    "300568",
    "603816",
    "300757",
    "300775",
    "300973",
    "002410",
    "600685",
    "600744",
    "300911",
    "002701",
    "603508",
    "603983",
    "603317",
    "600422",
    "600301",
    "600824",
    "002430",
    "000597",
    "000727",
    "002886",
    "603109",
    "002015",
    "300785",
    "002508",
    "601869",
    "002483",
    "002563",
    "002458",
    "603822",
    "301196",
    "603100",
    "601187",
    "300056",
    "600605",
    "000720",
    "601956",
    "603118",
    "603313",
    "601126",
    "600185",
    "301029",
    "600546",
    "600998",
    "002440",
    "002870",
    "600499",
    "002243",
    "603855",
    "000703",
    "300473",
    "300037",
    "002989",
    "600528",
    "300735",
    "600110",
    "603678",
    "002777",
    "300331",
    "603368",
    "300984",
    "003037",
    "603565",
    "603758",
    "603113",
    "300598",
    "002939",
    "605599",
    "603589",
    "600186",
    "600338",
    "000733",
    "300249",
    "603299",
    "300531",
    "002354",
    "000815",
    "002036",
    "002163",
    "600822",
    "300687",
    "002624",
    "000681",
    "002392",
    "600726",
    "600578",
    "600116",
    "300624",
    "600592",
    "300913",
    "600732",
    "002575",
    "000519",
    "600410",
    "600062",
    "000415",
    "000966",
    "600316",
    "002438",
    "002366",
    "603444",
    "002102",
    "600884",
    "000777",
    "002897",
    "002095",
    "603166",
    "301382",
    "300068",
    "002881",
    "300738",
    "600480",
    "002206",
    "603887",
    "600446",
    "002249",
    "603583",
    "300797",
    "600353",
    "301165",
    "300413",
    "002639",
    "000980",
    "603516",
    "002640",
    "002130",
    "300975",
    "002611",
    "300457",
    "600392",
    "000682",
    "601611",
    "002812",
    "002698",
    "000059",
    "002389",
    "300001",
    "300290",
    "300541",
    "002734",
    "603078",
    "600763",
    "600490",
    "002906",
    "002545",
    "600368",
    "002649",
    "002342",
    "300322",
    "600967",
    "301050",
    "002123",
    "300378",
    "600531",
    "600628",
    "002335",
    "002170",
    "000422",
    "300459",
    "601128",
    "600633",
    "600505",
    "600693",
    "603533",
    "002400",
    "600664",
    "300042",
    "300383",
    "603486",
    "002555",
    "002011",
    "600754",
    "300722",
    "002948",
    "600138",
    "002120",
    "600641",
    "600367",
    "601330",
    "002685",
    "000629",
    "600550",
    "000723",
    "600827",
    "600566",
    "002266",
    "300702",
    "002174",
    "000532",
    "000959",
    "002373",
    "002727",
    "601865",
    "300002",
    "301127",
    "002169",
    "000825",
    "300287",
    "601666",
    "605333",
    "002768",
    "601900",
    "603858",
    "300494",
    "002125",
    "301039",
    "000908",
    "603266",
    "300226",
    "601222",
    "601001",
    "600348",
    "000830",
    "300772",
    "601019",
    "603315",
    "601615",
    "600782",
    "600483",
    "600820",
    "002051",
    "603189",
    "300662",
    "002539",
    "001301",
    "600285",
    "605196",
    "600859",
    "601963",
    "002895",
    "002544",
    "002215",
    "600863",
    "000875",
    "002039",
    "000155",
    "002793",
    "000791",
    "600452",
    "002946",
    "002285",
    "000767",
    "600278",
    "603067",
    "300672",
    "300548",
    "002065",
    "600590",
    "300638",
    "300895",
    "002273",
    "300766",
    "300182",
    "300475",
    "300570",
    "603083",
    "000021",
    "300846",
    "603009",
    "603496",
    "300085",
    "000032",
    "603626",
    "603979",
    "002850",
    "000065",
    "300627",
    "603228",
    "603300",
    "300866",
    "002396",
    "002044",
    "002444",
    "603920",
    "603345",
    "000670",
    "000818",
    "002361",
    "603112",
    "300382",
    "601966",
    "002068",
    "300620",
    "002429",
    "301091",
    "002484",
    "300212",
    "002063",
    "000096",
    "002386",
    "002970",
    "300266",
    "000779",
    "002915",
    "603918",
    "603056",
    "002182",
    "300917",
    "600032",
    "600095",
    "600208",
    "002707",
    "600657",
    "002146",
    "002279",
    "600279",
    "002210",
    "000507",
    "300604",
    "300373",
    "603087",
    "300181",
    "002436",
    "603069",
    "000957",
    "000683",
    "600510",
    "600648",
    "600668",
    "002958",
    "000722",
    "300416",
    "002479",
    "600318",
    "300402",
    "601515",
    "600686",
    "002739",
    "600602",
    "300525",
    "601579",
    "300693",
    "600206",
    "300058",
    "300634",
    "000426",
    "002558",
    "002122",
    "000563",
    "002364",
    "002025",
    "000863",
    "000960",
    "300454",
    "603360",
    "002368",
    "300017",
    "600645",
    "300188",
    "000878",
    "300232",
    "300184",
    "601860",
    "603663",
    "002183",
    "600622",
    "600187",
    "300319",
    "600868",
    "300986",
    "600053",
    "300558",
    "601010",
    "603800",
    "000006",
    "002042",
    "000628",
    "300499",
    "000750",
    "000402",
    "002797",
    "002244",
    "600383",
    "002507",
    "000721",
    "600280",
    "600325",
    "002271",
    "603039",
    "603881",
    "300910",
    "000069",
    "600606",
    "002043",
    "600595",
    "603227",
    "605011",
    "000888",
    "000686",
    "002379",
    "601233",
    "603000",
    "600509",
    "300957",
    "002468",
    "300128",
    "002580",
    "000601",
    "301316",
    "000558",
    "000710",
    "601619",
    "300088",
    "000657",
    "600101",
    "000016",
    "002518",
    "600771",
    "002670",
    "000923",
    "002395",
    "002461",
    "600259",
    "600121",
    "603991",
    "600821",
    "600529",
    "603888",
    "301486",
    "600728",
    "002293",
    "002214",
    "000709",
    "603929",
    "002020",
    "300676",
    "000537",
    "600059",
    "002196",
    "603871",
    "600780",
    "300395",
    "603648",
    "300441",
    "002967",
    "600109",
    "603235",
    "002654",
    "300244",
    "600971",
    "600525",
    "300355",
    "300217",
    "000514",
    "002937",
    "000881",
    "300456",
    "002134",
    "002092",
    "000885",
    "000524",
    "300203",
    "300399",
    "000534",
    "300294",
    "000591",
    "000920",
    "002469",
    "002255",
    "600216",
    "000528",
    "300655",
    "002369",
    "300474",
    "605133",
    "002094",
    "300762",
    "300567",
    "600308",
    "002984",
    "301171",
    "300822",
    "600789",
    "300204",
    "000848",
    "300046",
    "002124",
    "002481",
    "603121",
    "600129",
    "300724",
    "002007",
    "002488",
    "300386",
    "300705",
    "002407",
    "002688",
    "000902",
    "600501",
    "002080",
    "002556",
    "600860",
    "000603",
    "002738",
    "000993",
    "002657",
    "002202",
    "300034",
    "603308",
    "300435",
    "002630",
    "000797",
    "300530",
    "002773",
    "605009",
    "002693",
    "002165",
    "600810",
    "300252",
    "605179",
    "000560",
    "300327",
    "002697",
    "600266",
    "300850",
    "300613",
    "000600",
    "600982",
    "600629",
    "000893",
    "601086",
    "600610",
    "002557",
    "300438",
    "600979",
    "600292",
    "000899",
    "000973",
    "603890",
    "603693",
    "002550",
    "000627",
    "002176",
    "600702",
    "000967",
    "300048",
    "300231",
    "300277",
    "000678",
    "002332",
    "300593",
    "002703",
    "000539",
    "300267",
    "000937",
    "603178",
    "600157",
    "600507",
    "000877",
    "300442",
    "600790",
    "301015",
    "000400",
    "300194",
    "601059",
    "002537",
    "002192",
    "600298",
    "002582",
    "600688",
    "600021",
    "001896",
    "002756",
    "603969",
    "000922",
    "603901",
    "601136",
    "000659",
    "300725",
    "002746",
    "300569",
    "300769",
    "603131",
    "600310",
    "002016",
    "603322",
    "300343",
    "002387",
    "300390",
    "600503",
    "002100",
    "002240",
    "300363",
    "605300",
    "603267",
    "300773",
    "601156",
    "600105",
    "300061",
    "002457",
    "300398",
    "603535",
    "300079",
    "600787",
    "000965",
    "002695",
    "600166",
    "002353",
    "002627",
    "300679",
    "600497",
    "002896",
    "603063",
    "002334",
    "300870",
    "002056",
    "300682",
    "300681",
    "002139",
    "000099",
    "603119",
    "300953",
    "300024",
    "300607",
    "300233",
    "300628",
    "300469",
    "600571",
    "300248",
    "002997",
    "002221",
    "600312",
    "301550",
    "002681",
    "300276",
    "300827",
    "002947",
    "002536",
    "300031",
    "300507",
    "300401",
    "603338",
    "300602",
    "000766",
    "300342",
    "000852",
    "000541",
    "603677",
    "002706",
    "601608",
    "600582",
    "603011",
    "600081",
    "600738",
    "600006",
    "002409",
    "603123",
    "301116",
    "600835",
    "000516",
    "600761",
    "000503",
    "600583",
    "000039",
    "300102",
    "600549",
    "002031",
    "300657",
    "002847",
    "002117",
    "600797",
    "002283",
    "000903",
    "300263",
    "300307",
    "300666",
    "002664",
    "300073",
    "002291",
    "000810",
    "601231",
    "003019",
    "601008",
    "000612",
    "603283",
    "300440",
    "300863",
    "600865",
    "002140",
    "300748",
    "300450",
    "603015",
    "300695",
    "600843",
    "002154",
    "000969",
    "000795",
    "000970",
    "002307",
    "300790",
    "300012",
    "002892",
    "603528",
    "300709",
    "000559",
    "600986",
    "001267",
    "603897",
    "300285",
    "002101",
    "300880",
    "603197",
    "002559",
    "603708",
    "603416",
    "603171",
    "000420",
    "000409",
    "600212",
    "000700",
    "300811",
    "603767",
    "300563",
    "002542",
    "002106",
    "300222",
    "300256",
    "002437",
    "600246",
    "002151",
    "603305",
    "300224",
    "002520",
    "300127",
    "603507",
    "002549",
    "300278",
    "603960",
    "300304",
    "600366",
    "000623",
    "300045",
    "300377",
    "301061",
    "300446",
    "002708",
    "601677",
    "600363",
    "001212",
    "600935",
    "300793",
    "002745",
    "002406",
    "300809",
    "300856",
    "300284",
    "000536",
    "002045",
    "002617",
    "300925",
    "600933",
    "300586",
    "301215",
    "300465",
    "603613",
    "000801",
    "300663",
    "300130",
    "002583",
    "300075",
    "002297",
    "601069",
    "600619",
    "603728",
    "000555",
    "603169",
    "600866",
    "300047",
    "002423",
    "600963",
    "605358",
    "002194",
    "300348",
    "002843",
    "301293",
    "002201",
    "002008",
    "002317",
    "300671",
    "300547",
    "300077",
    "600649",
    "300346",
    "603990",
    "300576",
    "000948",
    "002077",
    "603967",
    "300872",
    "600391",
    "002510",
    "002675",
    "600179",
    "600273",
    "002262",
    "002264",
    "601388",
    "000582",
    "000886",
    "300436",
    "300429",
    "600073",
    "600486",
    "300087",
    "601595",
    "000048",
    "000913",
    "003040",
    "002385",
    "300315",
    "601519",
    "002110",
    "603690",
    "300685",
    "301153",
    "600389",
    "600639",
    "600390",
    "603885",
    "002338",
    "002299",
    "600327",
    "002626",
    "000592",
    "603988",
    "300189",
    "002265",
    "000762",
    "002427",
    "002439",
    "603556",
    "603077",
    "002668",
    "300003",
    "000042",
    "600621",
    "300579",
    "600017",
    "000050",
    "002865",
    "300596",
    "600651",
    "601918",
    "000070",
    "002421",
    "300122",
    "600975",
    "300260",
    "301205",
    "000998",
    "600850",
    "002138",
    "603766",
    "002792",
    "002446",
    "002277",
    "000738",
    "600916",
    "002237",
    "603005",
    "300170",
    "300351",
    "000822",
    "000915",
    "002779",
    "300364",
    "300180",
    "603466",
    "300139",
    "001337",
    "002104",
    "300592",
    "002487",
    "000833",
    "300466",
    "603456",
    "002315",
    "301101",
    "000566",
    "300688",
    "600397",
    "603366",
    "600562",
    "603977",
    "600360",
    "000785",
    "002839",
    "002988",
    "301031",
    "000990",
    "600609",
    "002922",
    "300153",
    "002212",
    "300409",
    "301220",
    "300303",
    "300255",
    "600033",
    "300344",
    "603035",
    "605158",
    "002570",
    "603323",
    "000880",
    "601212",
    "603733",
    "601789",
    "002709",
    "002803",
    "600577",
    "300027",
    "003006",
    "002097",
    "600567",
    "300049",
    "300324",
    "301367",
    "002449",
    "002245",
    "002716",
    "300684",
    "000060",
    "000680",
    "002167",
    "002067",
    "300298",
    "000949",
    "300296",
    "600103",
    "000526",
    "601665",
    "301308",
    "002929",
    "000025",
    "002985",
    "600961",
    "603002",
    "601068",
    "300841",
    "002987",
    "300133",
    "600751",
    "600707",
    "300136",
    "001286",
    "000417",
    "300199",
    "002300",
    "601099",
    "601456",
    "002258",
    "600552",
    "601890",
    "301219",
    "002597",
    "002178",
    "300430",
    "000859",
    "002204",
    "000719",
    "000712",
    "300587",
    "603220",
    "002115",
    "002588",
    "600611",
    "002093",
    "601005",
    "600601",
    "300618",
    "300468",
    "002467",
    "300380",
    "600776",
    "000676",
    "300134",
    "002195",
    "300810",
    "600340",
    "600575",
    "603719",
    "000793",
    "000823",
    "002127",
    "603777",
    "600746",
    "603866",
    "600644",
    "600171",
    "600814",
    "601116",
    "002075",
    "300318",
    "603931",
    "300292",
    "605108",
    "000989",
    "300213",
    "001309",
    "000811",
    "000882",
    "300113",
    "601369",
    "002653",
    "600826",
    "300236",
    "603128",
    "603650",
    "002609",
    "300293",
    "002838",
    "600727",
    "002372",
    "600764",
    "600300",
    "601678",
    "603712",
    "603103",
    "002408",
    "603290",
    "300379",
    "300144",
    "603358",
    "600872",
    "600267",
    "600439",
    "300729",
    "000061",
    "601226",
    "603569",
    "300520",
    "603027",
    "300581",
    "605111",
    "600249",
    "000716",
    "002145",
    "300857",
    "000951",
    "600765",
    "002928",
    "000850",
    "002882",
    "603099",
    "605589",
    "002153",
    "002532",
    "300900",
    "603199",
    "002269",
    "002006",
    "002121",
    "300641",
    "600054",
    "000905",
    "002607",
    "002254",
    "601921",
    "300529",
    "603517",
    "000917",
    "300781",
    "605337",
    "603477",
    "603200",
    "002471",
    "000598",
    "000505",
    "000935",
    "002991",
    "300173",
    "000656",
    "301256",
    "300595",
    "000813",
    "300997",
    "603630",
    "605008",
    "300121",
    "000521",
    "002301",
    "600750",
    "000567",
    "605338",
    "600981",
    "002515",
    "002612",
    "601598",
    "301487",
    "300927",
    "002239",
    "300071",
    "000753",
    "300109",
    "600373",
    "300783",
    "600223",
    "002605",
    "300100",
    "002416",
    "002956",
    "000520",
    "000636",
    "601155",
    "601969",
    "002538",
    "002320",
    "002749",
    "605199",
    "000550",
    "000756",
    "603688",
    "000997",
    "300044",
    "300792",
    "000572",
    "002730",
    "603588",
    "300147",
    "600380",
    "300145",
    "002419",
    "300727",
    "301155",
    "002073",
    "300200",
    "600060",
    "000936",
    "600563",
    "300723",
    "601399",
    "002959",
    "601002",
    "600172",
    "002741",
    "600203",
    "300527",
    "600478",
    "000735",
    "301018",
    "002171"
]

# 2. Define the parameter grid to explore
#    Keys should match the argument names expected by IchimokuBacktester or the backtest process
#    Values are lists of settings to try for that parameter.
#    *** USER ACTION REQUIRED: Define the actual parameters and ranges here ***
PARAM_GRID = {
    # --- Ichimoku Parameters (More aggressive options included) ---
    'conversion': [3, 5, 7, 9, 12],       # 转换线周期 (更短更激进)
    'base': [15, 20, 26, 30, 40],         # 基准线周期 (更短更激进)
    'span_b': [30, 40, 52, 60, 70],       # 先行带B周期 (更短更激进)
    # --- Backtest Strategy Parameters ---
    # 'hold_days' is no longer optimized as we use dynamic exit based on base line cross
    'signal_days': [15, 20, 30, 40],      # 信号生成回看天数
    # 'position_sizing_ratio': [0.05, 0.1, 0.15, 0.2, 0.25] # 仓位比例 (每次交易投入总权益的百分比)
    # --- End Parameters ---
    # 注意: 如果你认为仓位比例对策略影响不大，或者你想使用固定的仓位，
    # 可以将 'position_sizing_ratio' 从 PARAM_GRID 移到下面的 FIXED_PARAMS 中，
    # 并设置一个你认为合适的值 (例如 0.1 代表 10%)。这将减少优化组合数量。
}

# 3. Define fixed parameters (not being optimized)
#    These are passed to the backtester or its methods if not present in the current PARAM_GRID combination
FIXED_PARAMS = {
    'data_source': 'tushare',
    'start_date': '2025-01-01',
    'end_date': '2025-04-30',
    'interval': '1d',
    'initial_capital': 100000,
    'signal_days': 30, # Fixed if 'signal_days' is not in PARAM_GRID
    'position_sizing_ratio': 0.1, # 如果决定固定仓位比例，取消此行注释并设置固定值
    'stop_loss_pct': 8, # 新增：固定止损百分比 (例如 8 代表 8%)，设为 None 则禁用
    # Add other fixed parameters needed by IchimokuBacktester or run_backtest
}

# 4. Define how to aggregate results across stocks for a single parameter set
#    *** USER ACTION REQUIRED: Review and potentially modify this aggregation logic ***
def aggregate_performance(stock_performances):
    """
    Aggregates performance metrics from multiple stocks for a single parameter set.

    Args:
        stock_performances (list): A list of performance dictionaries, one for each stock.
                                   Filters out None or error results automatically.

    Returns:
        dict: A dictionary containing aggregated metrics.
              Returns metrics with None values if no valid performances are found.
    """
    # Filter out results with errors or no trades (unless you want to include them differently)
    valid_performances = [p for p in stock_performances if p and 'error' not in p and p.get('total_trades', 0) > 0]

    num_valid = len(valid_performances)
    num_total = len(stock_performances)
    num_no_trades = len([p for p in stock_performances if p and 'error' not in p and p.get('total_trades', 0) == 0])
    num_errors = len([p for p in stock_performances if p and 'error' in p])


    if not valid_performances:
        # Return default values indicating no successful trades across the stock list for this param set
        return {
            'avg_total_return_pct': None,
            'avg_win_rate_pct': None,
            'avg_profit_factor': None,
            'avg_max_drawdown_pct': None,
            'num_valid_stocks': num_valid,
            'num_no_trade_stocks': num_no_trades,
            'num_error_stocks': num_errors,
            'num_total_stocks': num_total
        }

    perf_df = pd.DataFrame(valid_performances)

    # Calculate mean of key metrics - adjust aggregation method if needed (e.g., median)
    aggregated = {
        'avg_total_return_pct': perf_df['total_return_pct'].mean(),
        'avg_win_rate_pct': perf_df['win_rate_pct'].mean(),
        # Profit factor can be infinite or NaN, handle carefully
        'avg_profit_factor': perf_df['profit_factor'].replace([float('inf'), -float('inf')], pd.NA).mean(),
        'avg_max_drawdown_pct': perf_df['max_drawdown_pct'].mean(),
        'num_valid_stocks': num_valid,
        'num_no_trade_stocks': num_no_trades,
        'num_error_stocks': num_errors,
        'num_total_stocks': num_total
    }
    return aggregated

# 5. Define the metric to sort results by (higher is generally better, adjust if needed)
#    *** USER ACTION REQUIRED: Choose the primary metric for ranking parameter sets ***
SORT_METRIC = 'avg_total_return_pct' # Examples: 'avg_win_rate_pct', 'avg_profit_factor'

# 6. Multiprocessing configuration
#    Set to 1 or 0 to disable multiprocessing
# NUM_PROCESSES = max(1, cpu_count() - 1) # Use all cores except one
NUM_PROCESSES = cpu_count() # Try using all available CPU cores
# 或者你可以手动设置一个数字，例如 NUM_PROCESSES = 8

# 7. Output directory and file name base
OUTPUT_FILENAME_BASE = 'optimization_results' # Base name for output files

# --- Data Fetching Function ---

def fetch_all_stock_data(stock_list, data_source, data_fetch_params, rate_limit_interval=0.4):
    """
    Fetches historical data for all stocks in the list and stores it in a dictionary.
    Includes rate limiting to avoid hitting API limits.
    """
    stock_data_cache = {}
    print(f"Fetching data for {len(stock_list)} stocks...")
    # Use a temporary backtester instance just for fetching data
    temp_backtester = IchimokuBacktester(data_source=data_source)
    fetch_start_time = time.time()

    for i, stock_code in enumerate(stock_list):
        print(f"  Fetching {stock_code} ({i+1}/{len(stock_list)})...", end='', flush=True)
        try:
            # Apply rate limiting before each call
            time.sleep(rate_limit_interval)
            df = temp_backtester.get_history_data(stock_code, **data_fetch_params)
            if df is not None and not df.empty:
                stock_data_cache[stock_code] = df
                print(" Done.")
            else:
                print(" Failed (No data).")
                Logger.warning(f"No data fetched for {stock_code}")
        except Exception as e:
            print(f" Failed (Error: {e}).")
            Logger.error(f"Error fetching data for {stock_code}: {e}")
            # Optionally log traceback: Logger.error(traceback.format_exc())

    fetch_end_time = time.time()
    print(f"Data fetching complete. Fetched data for {len(stock_data_cache)} out of {len(stock_list)} stocks.")
    print(f"Total data fetching time: {fetch_end_time - fetch_start_time:.2f} seconds.")
    print("-" * 30)
    return stock_data_cache


# --- Worker Function ---

def run_backtest_for_params_worker(args):
    """
    Worker function to run multi-stock backtest for a single parameter combination using cached data.
    Designed to be used with multiprocessing.Pool.map. Handles exceptions internally.
    """
    param_combination, stock_list, stock_data_cache, fixed_params, combo_index, total_combos = args # Unpack arguments including cache and progress info
    # Create a unique identifier string for the parameter combination for logging
    param_id_str = "; ".join(f"{k}={v}" for k, v in sorted(param_combination.items()))
    # --- 添加带百分比的进度打印 ---
    percentage = (combo_index + 1) / total_combos * 100
    # 打印进程ID、百分比、当前组合编号和总数
    print(f"[Worker-{os.getpid()}] Progress: {percentage:.1f}% ({combo_index+1}/{total_combos})", flush=True)
    # Logger.info(f"[Worker-{os.getpid()}] START Params: {param_id_str}") # 更详细的日志（可选）

    individual_stock_results = []
    # Combine fixed and current combination parameters for this run
    current_run_params = {**fixed_params, **param_combination}

    # --- Parameter Extraction for Clarity (adjust keys as needed) ---
    ichimoku_config_params = {k: v for k, v in current_run_params.items() if k in ['conversion', 'base', 'span_b']}
    signal_gen_params = {k: v for k, v in current_run_params.items() if k in ['signal_days']}
    # Extract parameters for the updated run_backtest method
    backtest_run_params = {
        k: v for k, v in current_run_params.items()
        if k in ['initial_capital', 'position_sizing_ratio', 'stop_loss_pct'] # Keep these and add stop_loss_pct
    }
    # Add the dynamic exit parameter (assuming we always want it for optimization)
    backtest_run_params['exit_on_base_cross'] = True # Keep dynamic exit based on base line
    # data_fetch_params are no longer needed here as data is cached
    # --- End Parameter Extraction ---

    # --- No Rate Limiting needed inside worker anymore ---

    for stock_code in stock_list:
        stock_result = {'stock_code': stock_code} # Start with stock code
        df = stock_data_cache.get(stock_code) # Get data from cache

        if df is None or df.empty:
            # Skip if data wasn't fetched successfully earlier
            # Logger.debug(f"Skipping {stock_code} for params {param_id_str} - No cached data.")
            stock_result['error'] = 'No cached data available'
            individual_stock_results.append(stock_result)
            continue

        try:
            # --- Instantiation and Parameter Setting ---
            # Instantiate a new backtester for each stock to ensure isolation
            backtester = IchimokuBacktester(data_source=current_run_params['data_source'])

            # CRITICAL: Set the fetched data directly onto the backtester instance.
            # This assumes the backtester uses an internal 'df' attribute.
            # Verify this assumption based on IchimokuBacktester's implementation.
            backtester.df = df.copy() # Use a copy to prevent modification issues across runs

            # Apply Ichimoku parameters if they exist
            if hasattr(backtester, 'ichimoku_params') and ichimoku_config_params:
                 backtester.ichimoku_params = ichimoku_config_params
            # --- End Instantiation ---

            # --- Execute backtest steps using the cached data ---
            # 1. Calculate Indicators (uses backtester.df)
            if not backtester.calculate_indicators():
                stock_result['error'] = 'Indicator calculation failed'
                individual_stock_results.append(stock_result)
                continue

            # 2. Generate Signals (uses backtester.df)
            days_value = current_run_params.get('signal_days', 30) # Default if not in params
            if not backtester.generate_signals(days=days_value):
                stock_result['error'] = 'Signal generation failed'
                individual_stock_results.append(stock_result)
                continue

            # 3. Run Backtest (uses backtester.df)
            trades = backtester.run_backtest(**backtest_run_params)
            if trades is None: # Indicates an error during backtest run
                stock_result['error'] = 'Backtest execution failed'
                individual_stock_results.append(stock_result)
                continue

            # 4. Calculate Performance
            performance = backtester.calculate_performance(initial_capital=current_run_params['initial_capital'])

            if performance: # Performance calculated successfully
                stock_result.update(performance)
            elif isinstance(trades, list) and not trades: # No trades occurred
                stock_result.update({'total_trades': 0, 'total_return_pct': 0.0}) # Default metrics
            else: # Performance calculation failed after trades occurred
                 stock_result['error'] = 'Performance calculation failed'

            individual_stock_results.append(stock_result)

        except Exception as e:
            Logger.error(f"[Worker-{os.getpid()}] Error processing {stock_code} with params {param_id_str}: {e}")
            # Logger.error(traceback.format_exc()) # Uncomment for detailed traceback
            stock_result['error'] = str(e) # Store error message
            individual_stock_results.append(stock_result)

    # Aggregate results across all stocks for this parameter combination
    aggregated_result = aggregate_performance(individual_stock_results)
    # Logger.info(f"[Worker-{os.getpid()}] DONE. Params: {param_id_str}. Aggregated: {aggregated_result}") # Reduced logging

    # Return the original parameters combined with the aggregated result metrics
    return {**param_combination, **aggregated_result}


# --- Main Execution ---

if __name__ == "__main__":
    # Basic check if logger might be available
    if 'Logger' in globals() and hasattr(Logger, 'info'):
        Logger.info("Starting Ichimoku Parameter Optimization...")
    else:
        print("Starting Ichimoku Parameter Optimization... (Logger not fully configured)")

    start_time = time.time()

    # 1. Generate all parameter combinations
    param_names = PARAM_GRID.keys()
    param_values = PARAM_GRID.values()
    all_param_combinations = [dict(zip(param_names, v)) for v in itertools.product(*param_values)]
    num_combinations = len(all_param_combinations)

    if num_combinations == 0:
         print("ERROR: No parameter combinations generated. Check PARAM_GRID.")
         exit(1)

    print(f"Generated {num_combinations} parameter combinations to test.")
    print(f"Using stock list: {OPTIMIZATION_STOCK_LIST}")
    print(f"Fixed parameters: {FIXED_PARAMS}")
    print(f"Using {NUM_PROCESSES} processes for parallel execution.")
    print(f"Results will be sorted by: {SORT_METRIC} (higher is better)")
    output_csv_path = os.path.join(OPTIMIZATION_RESULTS_DIR, f"{OUTPUT_FILENAME_BASE}.csv")
    output_html_path = os.path.join(OPTIMIZATION_RESULTS_DIR, f"{OUTPUT_FILENAME_BASE}.html") # Path for HTML report
    print(f"Output directory: {OPTIMIZATION_RESULTS_DIR}")
    print(f"Output CSV file: {output_csv_path}")
    print(f"Output HTML file: {output_html_path}")
    print("-" * 30)

    # Ensure output directory exists
    try:
        os.makedirs(OPTIMIZATION_RESULTS_DIR, exist_ok=True)
        print(f"Ensured output directory exists: {OPTIMIZATION_RESULTS_DIR}")
    except OSError as e:
        print(f"ERROR: Could not create output directory {OPTIMIZATION_RESULTS_DIR}: {e}")
        exit(1)

    # --- ADDED: Pre-fetch all stock data ---
    data_fetch_params_main = {k: v for k, v in FIXED_PARAMS.items() if k in ['start_date', 'end_date', 'interval']}
    # Use a default rate limit interval, adjust if needed based on Tushare limits
    # You might need to increase this if you hit limits frequently.
    stock_data_cache = fetch_all_stock_data(
        OPTIMIZATION_STOCK_LIST,
        FIXED_PARAMS.get('data_source', 'tushare'), # Get data source from fixed params
        data_fetch_params_main,
        rate_limit_interval=0.4 # Keep the original interval for fetching
    )
    # --- END ADDED SECTION ---

    if not stock_data_cache:
        print("ERROR: No stock data could be fetched. Exiting optimization.")
        exit(1)
    print(f"Proceeding with optimization using data for {len(stock_data_cache)} stocks.")
    print("-" * 30)


    # Prepare arguments for the worker function, now including the data cache and progress info
    # Each worker still processes one parameter combination across all stocks in the list
    worker_args = [
        (combo, OPTIMIZATION_STOCK_LIST, stock_data_cache, FIXED_PARAMS, i, num_combinations)
        for i, combo in enumerate(all_param_combinations)
    ]


    # 2. Run backtests (serially or in parallel) using cached data
    optimization_results = []
    actual_processes = NUM_PROCESSES if num_combinations > 1 else 1 # Don't use pool for 1 combo

    if actual_processes > 1:
        print(f"Running backtests in parallel using {actual_processes} processes... (Progress printed by workers)")
        try:
            # Using 'spawn' context might be more stable on some systems (macOS, Windows)
            # import multiprocessing as mp
            # ctx = mp.get_context('spawn')
            # with ctx.Pool(actual_processes) as pool:
            with Pool(actual_processes) as pool:
                optimization_results = pool.map(run_backtest_for_params_worker, worker_args)
        except Exception as e:
             print(f"ERROR: Multiprocessing pool failed: {e}")
             print(traceback.format_exc())
             print("WARNING: Falling back to serial execution.")
             actual_processes = 1 # Force serial execution

    if actual_processes <= 1:
        print("Running backtests serially...")
        for i, args in enumerate(worker_args):
            # 从参数中提取进度信息
            _, _, _, _, combo_idx, total_combos = args
            percentage = (combo_idx + 1) / total_combos * 100
            print(f"--- Serial Progress: {percentage:.1f}% ({combo_idx+1}/{total_combos}) ---", flush=True)
            try:
                # 注意：在串行模式下，run_backtest_for_params_worker 内部的打印也会显示，但这里提供一个主循环的进度
                result = run_backtest_for_params_worker(args)
                optimization_results.append(result)
            except Exception as e:
                 param_combination, _, _ = args
                 param_id_str = "; ".join(f"{k}={v}" for k, v in sorted(param_combination.items()))
                 print(f"ERROR: Unhandled exception in serial run for params {param_id_str}: {e}")
                 print(traceback.format_exc())
                 # Add error placeholder to results
                 optimization_results.append({**param_combination, 'error': 'Unhandled exception in worker'})


    # 3. Process and analyze results
    print("-" * 30)
    print("Optimization runs complete. Processing results...")
    if not optimization_results:
        print("ERROR: No results were generated during optimization.")
    else:
        results_df = pd.DataFrame(optimization_results)

        # Ensure the sort metric column exists before sorting
        if SORT_METRIC in results_df.columns:
             # Convert sort column to numeric, coercing errors to NaN, before sorting
             results_df[SORT_METRIC] = pd.to_numeric(results_df[SORT_METRIC], errors='coerce')
             results_df = results_df.sort_values(by=SORT_METRIC, ascending=False, na_position='last')
        else:
             print(f"WARNING: Sort metric '{SORT_METRIC}' not found in results. Cannot sort.")

        print("\n=== Optimization Results Summary ===")
        # Adjust display options for potentially wide table
        pd.set_option('display.max_rows', None)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', 200) # Adjust width as needed
        pd.set_option('display.precision', 4)
        print(results_df.to_string(index=False))

        # Save results to CSV
        try:
            # Save to CSV
            results_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
            print(f"\nOptimization results saved to CSV: {output_csv_path}")

            # Save to HTML for better visualization
            try:
                # Add some basic styling and make the table scrollable if wide
                html_content = results_df.to_html(index=False, border=1, classes='dataframe table table-striped table-hover', justify='center')
                # Wrap in a basic HTML structure with some CSS for better presentation
                full_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ichimoku Optimization Results</title>
    <style>
        body {{ font-family: sans-serif; padding: 20px; }}
        h1 {{ text-align: center; }}
        .dataframe {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px auto;
            font-size: 0.9em;
            border: 1px solid #ccc;
        }}
        .dataframe th, .dataframe td {{
            text-align: center;
            padding: 8px 12px;
            border: 1px solid #ddd;
        }}
        .dataframe thead th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .dataframe tbody tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .dataframe tbody tr:hover {{ background-color: #f1f1f1; }}
        /* Make table scrollable horizontally if needed */
        .table-container {{ overflow-x: auto; }}
    </style>
</head>
<body>
    <h1>Ichimoku Strategy Parameter Optimization Results</h1>
    <p>Sorted by: {SORT_METRIC} (descending)</p>
    <div class="table-container">
        {html_content}
    </div>
</body>
</html>
"""
                with open(output_html_path, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                print(f"Optimization results also saved to HTML report: {output_html_path}")
            except Exception as e:
                print(f"ERROR: Failed to save optimization results to HTML ({output_html_path}): {e}")

        except Exception as e:
            print(f"ERROR: Failed to save optimization results to CSV ({output_csv_path}): {e}")

        # Display best result based on sorting
        if not results_df.empty and SORT_METRIC in results_df.columns and results_df[SORT_METRIC].notna().any():
             best_result = results_df.iloc[0].to_dict()
             print("\n--- Best Parameter Combination (based on highest " + SORT_METRIC + ") ---")
             # Exclude aggregation metrics and non-optimized params from the 'best params' display
             optimized_params_keys = list(PARAM_GRID.keys()) # Get keys that were actually optimized
             best_params_only = {k: v for k, v in best_result.items() if k in optimized_params_keys}
             print("Optimized Parameters:")
             for key, value in best_params_only.items():
                 print(f"  {key}: {value}")
             print("Aggregated Performance:")
             for key, value in best_result.items():
                 if key not in PARAM_GRID: # Show the performance metrics
                     if isinstance(value, float):
                         print(f"  {key}: {value:.4f}")
                     else:
                         print(f"  {key}: {value}")
        else:
             print("\nCould not determine the best result (no valid results or sort metric missing/invalid).")


    end_time = time.time()
    print("-" * 30)
    print(f"Total optimization time: {end_time - start_time:.2f} seconds.")

def save_optimization_results(results_df, filename):
    """保存优化结果到指定目录"""
    output_path = os.path.join(OPTIMIZATION_RESULTS_DIR, filename)
    results_df.to_csv(output_path, index=False)
    print(f"Results saved to {output_path}")

def setup_logger(stock_code):
    """为每个股票设置独立的日志文件"""
    log_file = os.path.join(LOGS_DIR, f"ichimoku_optimization_{stock_code}.log")
    return Logger(log_file)