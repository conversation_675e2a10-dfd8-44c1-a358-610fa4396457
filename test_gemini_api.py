#!/usr/bin/env python3
"""
测试 Gemini API 连接
"""

import os
import requests
import json

def test_gemini_api(api_key=None):
    """测试 Gemini API 连接"""
    
    # 获取 API 密钥
    api_key = api_key or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 错误: 请设置 GEMINI_API_KEY 环境变量或提供 API 密钥")
        print("💡 获取 API 密钥: https://makersuite.google.com/app/apikey")
        return False
    
    print("🔑 API 密钥已获取")
    print(f"🔑 密钥前缀: {api_key[:10]}...")
    
    # 支持多个模型的回退机制
    models = [
        "gemini-1.5-flash",
        "gemini-1.5-pro",
        "gemini-pro",
        "gemini-1.0-pro"
    ]
    base_url_template = "https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"

    headers = {
        'Content-Type': 'application/json',
    }

    # 测试数据
    test_data = {
        "contents": [{
            "parts": [{
                "text": "请简单介绍一下股票投资的基本原则，用中文回答，不超过100字。"
            }]
        }],
        "generationConfig": {
            "temperature": 0.7,
            "topK": 40,
            "topP": 0.95,
            "maxOutputTokens": 200,
        }
    }

    print("🚀 正在测试 API 连接...")

    # 尝试不同的模型
    for i, model in enumerate(models):
        try:
            url = base_url_template.format(model=model)
            print(f"  🔄 测试模型: {model}")

            response = requests.post(
                f"{url}?key={api_key}",
                headers=headers,
                json=test_data,
                timeout=30
            )

            print(f"  📡 HTTP 状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()

                if 'candidates' in result and len(result['candidates']) > 0:
                    ai_response = result['candidates'][0]['content']['parts'][0]['text']
                    print(f"  ✅ 模型 {model} 连接成功!")
                    print("🤖 AI 回复:")
                    print("-" * 40)
                    print(ai_response)
                    print("-" * 40)
                    return True
                else:
                    print(f"  ⚠️ 模型 {model} 返回空结果")
                    continue
            elif response.status_code == 404:
                print(f"  ❌ 模型 {model} 不可用 (404)")
                continue
            else:
                print(f"  ❌ 模型 {model} 调用失败: {response.status_code}")
                print(f"  📄 错误信息: {response.text}")
                if i == len(models) - 1:  # 最后一个模型也失败了
                    return False
                continue

        except requests.exceptions.Timeout:
            print(f"  ⏰ 模型 {model} 请求超时")
            if i == len(models) - 1:
                print("❌ 所有模型请求超时，请检查网络连接")
                return False
            continue
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 模型 {model} 网络连接错误")
            if i == len(models) - 1:
                print("❌ 网络连接错误，请检查网络设置")
                return False
            continue
        except Exception as e:
            print(f"  ❌ 模型 {model} 发生异常: {str(e)}")
            if i == len(models) - 1:
                print(f"❌ 所有模型都失败了")
                return False
            continue

    return False

def main():
    print("🧪 Gemini API 连接测试")
    print("=" * 40)
    
    # 测试 API
    success = test_gemini_api()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 测试完成! API 工作正常")
        print("💡 现在可以运行主分析脚本:")
        print("   python gemini_stock_analyzer.py --input_dir output")
    else:
        print("❌ 测试失败! 请检查:")
        print("   1. API 密钥是否正确")
        print("   2. 网络连接是否正常")
        print("   3. 是否需要使用代理")
        print("\n💡 获取 API 密钥: https://makersuite.google.com/app/apikey")

if __name__ == "__main__":
    main()
