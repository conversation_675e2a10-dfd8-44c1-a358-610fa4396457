#!/usr/bin/env python3
"""
后排股票上升潜力分析器 - 专门分析排名靠后股票的上升概率
"""

import os
import pandas as pd
from datetime import datetime
import argparse
from collections import defaultdict
import json

class BottomStockAnalyzer:
    """专门分析后排股票上升潜力的工具"""
    
    def __init__(self, input_dir, output_dir="analysis_reports"):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.all_data = self.load_data()

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
    
    def load_data(self):
        """加载数据"""
        print("📊 正在加载股票数据...")
        all_data = {}
        for date_folder in sorted(os.listdir(self.input_dir)):
            date_folder_path = os.path.join(self.input_dir, date_folder)
            if os.path.isdir(date_folder_path):
                csv_file_path = os.path.join(date_folder_path, "above_cloud_daily_change.csv")
                if os.path.isfile(csv_file_path):
                    try:
                        df = pd.read_csv(csv_file_path, dtype={'股票代码': str})
                        df['排名'] = range(1, len(df) + 1)
                        all_data[date_folder] = df
                        print(f"  ✅ {date_folder}: {len(df)} 只股票")
                    except Exception as e:
                        print(f"  ❌ {date_folder}: 加载失败 - {e}")
        return all_data
    
    def analyze_rising_success_factors(self):
        """分析历史上升成功因子"""
        print("🔍 分析历史上升成功因子...")
        
        dates = sorted(self.all_data.keys())
        success_factors = {
            'sector_performance': defaultdict(list),
            'concept_performance': defaultdict(list),
            'market_cap_ranges': {'small': [], 'medium': [], 'large': []},
            'volume_patterns': [],
            'gain_patterns': [],
            'rank_jump_patterns': [],
            'successful_stocks': []  # 记录成功的股票案例
        }
        
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            
            current_df = self.all_data[current_date]
            next_df = self.all_data[next_date]
            
            # 确保股票代码为字符串
            current_df['股票代码'] = current_df['股票代码'].astype(str)
            next_df['股票代码'] = next_df['股票代码'].astype(str)
            
            # 找到后排股票（排名后50%）
            total_stocks = len(current_df)
            bottom_threshold = total_stocks // 2
            bottom_stocks = current_df[current_df['排名'] > bottom_threshold]
            
            for _, stock in bottom_stocks.iterrows():
                stock_code = str(stock['股票代码'])
                current_rank = stock['排名']
                
                # 查找次日排名
                next_stock = next_df[next_df['股票代码'] == stock_code]
                if not next_stock.empty:
                    next_rank = next_stock.iloc[0]['排名']
                    rank_improvement = current_rank - next_rank
                    
                    # 记录成功上升的因子（排名上升超过10位）
                    if rank_improvement > 10:
                        # 记录成功案例
                        success_factors['successful_stocks'].append({
                            'date': current_date,
                            'stock_code': stock_code,
                            'stock_name': stock.get('股票名称', '未知'),
                            'rank_improvement': rank_improvement,
                            'sector': stock.get('申万板块', '未知'),
                            'concepts': stock.get('开盘啦概念', ''),
                            'market_cap': stock.get('流通市值', 0),
                            'volume_ratio': stock.get('量比', 1),
                            'current_gain': stock.get('当日涨幅(%)', 0)
                        })
                        
                        # 板块因子
                        sector = stock.get('申万板块', '未知')
                        success_factors['sector_performance'][sector].append(rank_improvement)
                        
                        # 概念因子
                        concepts = stock.get('开盘啦概念', '')
                        if concepts and pd.notna(concepts):
                            for concept in concepts.split(','):
                                concept = concept.strip()
                                if concept:
                                    success_factors['concept_performance'][concept].append(rank_improvement)
                        
                        # 市值因子
                        market_cap = stock.get('流通市值', 0)
                        if market_cap and pd.notna(market_cap):
                            cap = float(market_cap)
                            if cap < 50:
                                success_factors['market_cap_ranges']['small'].append(rank_improvement)
                            elif cap < 200:
                                success_factors['market_cap_ranges']['medium'].append(rank_improvement)
                            else:
                                success_factors['market_cap_ranges']['large'].append(rank_improvement)
                        
                        # 量比和涨幅因子
                        volume_ratio = stock.get('量比', 1)
                        if volume_ratio and pd.notna(volume_ratio):
                            success_factors['volume_patterns'].append(float(volume_ratio))
                        
                        current_gain = stock.get('当日涨幅(%)', 0)
                        if current_gain and pd.notna(current_gain):
                            success_factors['gain_patterns'].append(float(current_gain))
                        
                        success_factors['rank_jump_patterns'].append(rank_improvement)
        
        print(f"  📈 发现 {len(success_factors['successful_stocks'])} 个成功上升案例")
        return success_factors
    
    def calculate_stock_potential_score(self, stock, success_factors):
        """计算股票潜力得分"""
        score = 0
        score_details = {}
        
        # 1. 板块得分 (30分)
        sector = stock.get('申万板块', '未知')
        if sector in success_factors['sector_performance']:
            improvements = success_factors['sector_performance'][sector]
            avg_improvement = sum(improvements) / len(improvements)
            success_count = len(improvements)
            
            # 根据历史表现给分
            if avg_improvement >= 20 and success_count >= 3:
                sector_score = 30
            elif avg_improvement >= 15 and success_count >= 2:
                sector_score = 25
            elif avg_improvement >= 10:
                sector_score = 20
            else:
                sector_score = 10
        else:
            sector_score = 5  # 无历史数据的板块给基础分
        
        score += sector_score
        score_details['板块得分'] = sector_score
        
        # 2. 概念得分 (25分)
        concepts = stock.get('开盘啦概念', '')
        concept_score = 0
        if concepts and pd.notna(concepts):
            concept_scores = []
            for concept in concepts.split(','):
                concept = concept.strip()
                if concept in success_factors['concept_performance']:
                    improvements = success_factors['concept_performance'][concept]
                    avg_improvement = sum(improvements) / len(improvements)
                    if avg_improvement >= 20:
                        concept_scores.append(25)
                    elif avg_improvement >= 15:
                        concept_scores.append(20)
                    elif avg_improvement >= 10:
                        concept_scores.append(15)
                    else:
                        concept_scores.append(10)
            
            if concept_scores:
                concept_score = max(concept_scores)  # 取最高概念得分
        
        score += concept_score
        score_details['概念得分'] = concept_score
        
        # 3. 市值得分 (20分)
        market_cap = stock.get('流通市值', 0)
        if market_cap and pd.notna(market_cap):
            cap = float(market_cap)
            if cap < 30:
                market_cap_score = 20  # 超小盘
            elif cap < 50:
                market_cap_score = 18  # 小盘
            elif cap < 100:
                market_cap_score = 15  # 中小盘
            elif cap < 200:
                market_cap_score = 10  # 中盘
            else:
                market_cap_score = 5   # 大盘
        else:
            market_cap_score = 5
        
        score += market_cap_score
        score_details['市值得分'] = market_cap_score
        
        # 4. 量比得分 (15分)
        volume_ratio = stock.get('量比', 1)
        if volume_ratio and pd.notna(volume_ratio):
            vol_ratio = float(volume_ratio)
            if vol_ratio >= 3:
                volume_score = 15  # 大幅放量
            elif vol_ratio >= 2:
                volume_score = 12  # 明显放量
            elif vol_ratio >= 1.5:
                volume_score = 10  # 适度放量
            elif vol_ratio >= 1.2:
                volume_score = 8   # 小幅放量
            else:
                volume_score = 5   # 缩量
        else:
            volume_score = 5
        
        score += volume_score
        score_details['量比得分'] = volume_score
        
        # 5. 涨幅得分 (10分)
        current_gain = stock.get('当日涨幅(%)', 0)
        if current_gain and pd.notna(current_gain):
            gain = float(current_gain)
            if 0 <= gain <= 3:
                gain_score = 10  # 温和上涨
            elif 3 < gain <= 6:
                gain_score = 8   # 适度上涨
            elif gain > 6:
                gain_score = 5   # 涨幅过大
            elif -2 <= gain < 0:
                gain_score = 7   # 小幅调整
            else:
                gain_score = 3   # 大幅下跌
        else:
            gain_score = 5
        
        score += gain_score
        score_details['涨幅得分'] = gain_score
        
        score_details['总分'] = score
        return score, score_details
    
    def generate_bottom_stock_report(self):
        """生成后排股票分析报告"""
        print("📝 生成后排股票潜力分析报告...")
        
        # 分析成功因子
        success_factors = self.analyze_rising_success_factors()
        
        # 获取最新数据
        latest_date = max(self.all_data.keys())
        latest_df = self.all_data[latest_date]
        
        # 定义后排股票
        total_stocks = len(latest_df)
        bottom_threshold = total_stocks // 2
        bottom_stocks = latest_df[latest_df['排名'] > bottom_threshold].copy()
        
        print(f"  📊 分析范围: 排名 > {bottom_threshold} 的后排股票 (共{len(bottom_stocks)}只)")
        
        # 计算每只股票的潜力得分
        potential_stocks = []
        for _, stock in bottom_stocks.iterrows():
            score, score_details = self.calculate_stock_potential_score(stock, success_factors)
            
            potential_stocks.append({
                'stock_code': str(stock['股票代码']),
                'stock_name': stock.get('股票名称', '未知'),
                'current_rank': stock['排名'],
                'sector': stock.get('申万板块', '未知'),
                'concepts': stock.get('开盘啦概念', ''),
                'market_cap': stock.get('流通市值', 0),
                'current_gain': stock.get('当日涨幅(%)', 0),
                'volume_ratio': stock.get('量比', 1),
                'turnover_rate': stock.get('换手率(%)', 0),
                'score': score,
                'score_details': score_details
            })
        
        # 按得分排序
        potential_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        # 生成报告
        report = self.create_detailed_report(success_factors, potential_stocks, latest_date, bottom_threshold)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"bottom_stocks_analysis_{timestamp}.md")

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📄 后排股票分析报告已保存到: {filename}")

        # 同时保存JSON格式的数据
        json_filename = os.path.join(self.output_dir, f"bottom_stocks_data_{timestamp}.json")
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'analysis_date': latest_date,
                'total_bottom_stocks': len(bottom_stocks),
                'success_factors': {
                    'sector_stats': {k: {'avg_improvement': sum(v)/len(v), 'count': len(v)} 
                                   for k, v in success_factors['sector_performance'].items()},
                    'concept_stats': {k: {'avg_improvement': sum(v)/len(v), 'count': len(v)} 
                                    for k, v in success_factors['concept_performance'].items()},
                    'successful_cases_count': len(success_factors['successful_stocks'])
                },
                'top_potential_stocks': potential_stocks[:30]
            }, f, ensure_ascii=False, indent=2)
        
        print(f"📊 数据文件已保存到: {json_filename}")
        
        return filename, potential_stocks[:20]
    
    def create_detailed_report(self, success_factors, potential_stocks, latest_date, bottom_threshold):
        """创建详细的分析报告"""
        report = f"""# 🔮 后排股票上升潜力深度分析报告

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据源**: {self.input_dir}
**分析日期**: {latest_date}
**分析范围**: 排名 > {bottom_threshold} 的后排股票 (共{len(potential_stocks)}只)
**历史成功案例**: {len(success_factors['successful_stocks'])} 个

## 📊 历史成功因子分析

### 🏆 最成功的板块 (按平均排名上升幅度排序)

| 排名 | 板块名称 | 平均上升幅度 | 成功次数 | 推荐指数 |
|------|----------|--------------|----------|----------|"""
        
        # 板块成功率分析
        sector_stats = []
        for sector, improvements in success_factors['sector_performance'].items():
            if len(improvements) >= 2:  # 至少2次成功记录
                avg_improvement = sum(improvements) / len(improvements)
                sector_stats.append({
                    'sector': sector,
                    'avg_improvement': avg_improvement,
                    'count': len(improvements),
                    'max_improvement': max(improvements)
                })
        
        sector_stats.sort(key=lambda x: x['avg_improvement'], reverse=True)
        
        for i, data in enumerate(sector_stats[:15], 1):
            stars = "⭐" * min(5, int(data['avg_improvement'] / 5))
            report += f"\n| {i} | {data['sector']} | {data['avg_improvement']:.1f}位 | {data['count']}次 | {stars} |"
        
        # 概念成功率分析
        report += "\n\n### 🎯 最热门的概念 (按平均排名上升幅度排序)\n\n"
        report += "| 排名 | 概念名称 | 平均上升幅度 | 成功次数 | 推荐指数 |\n"
        report += "|------|----------|--------------|----------|----------|\n"
        
        concept_stats = []
        for concept, improvements in success_factors['concept_performance'].items():
            if len(improvements) >= 2:
                avg_improvement = sum(improvements) / len(improvements)
                concept_stats.append({
                    'concept': concept,
                    'avg_improvement': avg_improvement,
                    'count': len(improvements)
                })
        
        concept_stats.sort(key=lambda x: x['avg_improvement'], reverse=True)
        
        for i, data in enumerate(concept_stats[:20], 1):
            stars = "⭐" * min(5, int(data['avg_improvement'] / 5))
            report += f"| {i} | {data['concept']} | {data['avg_improvement']:.1f}位 | {data['count']}次 | {stars} |\n"
        
        # 市值效应分析
        report += "\n### 💎 市值效应分析\n\n"
        market_cap_analysis = {
            'small': ('小盘股(<50亿)', success_factors['market_cap_ranges']['small']),
            'medium': ('中盘股(50-200亿)', success_factors['market_cap_ranges']['medium']),
            'large': ('大盘股(>200亿)', success_factors['market_cap_ranges']['large'])
        }
        
        for cap_type, (name, improvements) in market_cap_analysis.items():
            if improvements:
                avg_improvement = sum(improvements) / len(improvements)
                max_improvement = max(improvements)
                report += f"- **{name}**: 平均上升 {avg_improvement:.1f}位，最大上升 {max_improvement}位 ({len(improvements)}次)\n"
        
        # 技术指标分析
        if success_factors['volume_patterns']:
            avg_volume = sum(success_factors['volume_patterns']) / len(success_factors['volume_patterns'])
            max_volume = max(success_factors['volume_patterns'])
            report += f"\n### 📈 成功股票技术特征\n\n"
            report += f"- **平均量比**: {avg_volume:.2f} (最高: {max_volume:.2f})\n"
        
        if success_factors['gain_patterns']:
            avg_gain = sum(success_factors['gain_patterns']) / len(success_factors['gain_patterns'])
            max_gain = max(success_factors['gain_patterns'])
            min_gain = min(success_factors['gain_patterns'])
            report += f"- **平均当日涨幅**: {avg_gain:.2f}% (区间: {min_gain:.2f}% ~ {max_gain:.2f}%)\n"
        
        if success_factors['rank_jump_patterns']:
            avg_jump = sum(success_factors['rank_jump_patterns']) / len(success_factors['rank_jump_patterns'])
            max_jump = max(success_factors['rank_jump_patterns'])
            report += f"- **平均排名上升**: {avg_jump:.1f}位 (最大: {max_jump}位)\n"
        
        # TOP20潜力股票
        report += f"\n## 🎯 TOP20 最具潜力后排股票 ({latest_date})\n\n"
        report += "| 排名 | 股票代码 | 股票名称 | 当前排名 | 潜力得分 | 申万板块 | 流通市值 | 量比 | 当日涨幅 |\n"
        report += "|------|----------|----------|----------|----------|----------|----------|------|----------|\n"
        
        for i, stock in enumerate(potential_stocks[:20], 1):
            market_cap = stock['market_cap']
            market_cap_str = f"{float(market_cap):.1f}亿" if market_cap and pd.notna(market_cap) else "未知"
            
            volume_ratio = stock['volume_ratio']
            volume_str = f"{float(volume_ratio):.2f}" if volume_ratio and pd.notna(volume_ratio) else "N/A"
            
            current_gain = stock['current_gain']
            gain_str = f"{float(current_gain):+.2f}%" if current_gain and pd.notna(current_gain) else "N/A"
            
            report += f"| {i} | {stock['stock_code']} | {stock['stock_name']} | {stock['current_rank']} | {stock['score']:.1f} | {stock['sector']} | {market_cap_str} | {volume_str} | {gain_str} |\n"
        
        # 详细得分分析
        report += "\n## 📊 TOP10 详细得分分析\n\n"
        
        for i, stock in enumerate(potential_stocks[:10], 1):
            score_details = stock['score_details']
            concepts = stock['concepts'] if stock['concepts'] and pd.notna(stock['concepts']) else "无特殊概念"
            
            report += f"### {i}. {stock['stock_code']} {stock['stock_name']} (总分: {stock['score']:.1f})\n\n"
            report += f"- **当前排名**: {stock['current_rank']}\n"
            report += f"- **申万板块**: {stock['sector']}\n"
            report += f"- **概念标签**: {concepts}\n"
            report += f"- **得分详情**:\n"
            report += f"  - 板块得分: {score_details['板块得分']}/30\n"
            report += f"  - 概念得分: {score_details['概念得分']}/25\n"
            report += f"  - 市值得分: {score_details['市值得分']}/20\n"
            report += f"  - 量比得分: {score_details['量比得分']}/15\n"
            report += f"  - 涨幅得分: {score_details['涨幅得分']}/10\n\n"
        
        # 投资策略建议
        report += """## 💡 投资策略建议

### 🎯 选股策略
1. **优先级排序**: 按潜力得分选择，重点关注得分>70的股票
2. **板块配置**: 重点关注历史成功率高的板块
3. **概念追踪**: 关注热门概念的轮动机会
4. **市值偏好**: 优先选择小盘股（<100亿）
5. **技术确认**: 关注量比>1.5且涨幅适中的股票

### 📅 操作策略
1. **分批建仓**: 分2-3次建仓，首次建仓30%
2. **止损设置**: 跌破买入价8%坚决止损
3. **止盈策略**: 排名上升20位以上或涨幅15%开始分批止盈
4. **持仓周期**: 建议持仓3-10个交易日
5. **仓位控制**: 单只股票不超过总仓位的5%

### ⚠️ 风险提示
1. **高波动性**: 后排股票波动较大，注意仓位控制
2. **基本面风险**: 部分股票可能存在基本面问题
3. **流动性风险**: 小盘股可能存在流动性不足
4. **市场风险**: 整体市场下跌时风险加大
5. **概念炒作**: 避免追高概念炒作股票

### 🔍 监控指标
1. **排名变化**: 每日关注排名变化趋势
2. **成交量**: 关注放量突破信号
3. **板块轮动**: 跟踪热点板块切换
4. **市场情绪**: 关注整体市场风险偏好
5. **个股公告**: 及时关注重要公告信息
"""
        
        return report

def main():
    parser = argparse.ArgumentParser(description="后排股票上升潜力分析")
    parser.add_argument("--input_dir", type=str, required=True, help="包含股票数据的目录路径")
    parser.add_argument("--output_dir", type=str, default="analysis_reports", help="输出目录路径 (默认: analysis_reports)")

    args = parser.parse_args()

    try:
        analyzer = BottomStockAnalyzer(args.input_dir, args.output_dir)
        filename, top_stocks = analyzer.generate_bottom_stock_report()
        
        print(f"\n🎯 分析完成!")
        print(f"📄 报告文件: {filename}")
        print(f"📊 发现 {len(top_stocks)} 只高潜力股票")
        
        # 显示TOP5
        print(f"\n🏆 TOP5 最具潜力股票:")
        for i, stock in enumerate(top_stocks[:5], 1):
            print(f"  {i}. {stock['stock_code']} {stock['stock_name']} (得分: {stock['score']:.1f})")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
