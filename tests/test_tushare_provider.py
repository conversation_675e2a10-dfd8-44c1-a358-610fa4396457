import unittest
import pandas as pd
from src.data_providers.stock_data_provider import <PERSON><PERSON>reProvider, DataNotFoundError, DataSourceError

class TestTushareProvider(unittest.TestCase):
    """测试 TushareProvider 类"""

    @classmethod
    def setUpClass(cls):
        """在所有测试开始前运行一次"""
        cls.provider = TushareProvider()

    def test_get_sw_index_members_by_l3_code(self):
        """测试通过三级行业代码获取申万行业成分股"""
        try:
            # 使用一个已知的黄金行业三级代码进行测试
            df = self.provider.get_sw_index_members(l3_code='850531.SI')
            self.assertIsNotNone(df, "返回的DataFrame不应为None")
            self.assertIsInstance(df, pd.DataFrame, "返回的应为DataFrame实例")
            self.assertGreater(len(df), 0, "返回的DataFrame不应为空")
            self.assertIn('ts_code', df.columns, "DataFrame应包含'ts_code'列")
            self.assertIn('name', df.columns, "DataFrame应包含'name'列")
            self.assertIn('l1_code', df.columns, "DataFrame应包含'l1_code'列")
            self.assertIn('l3_name', df.columns, "DataFrame应包含'l3_name'列")
            print("\n申万行业成分股 (黄金 l3_code='850531.SI'):")
            print(df.head())
        except DataNotFoundError:
            self.fail("不应抛出DataNotFoundError，因为该行业代码应该有数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败: {e}")

    def test_get_sw_index_members_by_ts_code(self):
        """测试通过股票代码获取其所属申万行业信息"""
        try:
            # 使用一个已知的股票代码进行测试
            df = self.provider.get_sw_index_members(ts_code='000001.SZ')
            self.assertIsNotNone(df, "返回的DataFrame不应为None")
            self.assertIsInstance(df, pd.DataFrame, "返回的应为DataFrame实例")
            self.assertGreater(len(df), 0, "返回的DataFrame不应为空")
            self.assertEqual(df['ts_code'].iloc[0], '000001.SZ', "股票代码应匹配")
            self.assertIn('l1_name', df.columns, "DataFrame应包含'l1_name'列")
            print(f"\n股票 000001.SZ 所属行业:")
            print(df.head())
        except DataNotFoundError:
            self.fail("不应抛出DataNotFoundError，因为该股票代码应该有数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败: {e}")

    def test_get_sw_index_members_is_new_filter(self):
        """测试 is_new 参数过滤功能"""
        try:
            # 测试获取最新的成分股
            df_new = self.provider.get_sw_index_members(l3_code='850531.SI', is_new='Y')
            self.assertIsNotNone(df_new)
            if not df_new.empty:
                self.assertTrue(all(df_new['is_new'] == 'Y'), "当is_new='Y'时, 所有记录的is_new应为'Y'")

            # 测试获取非最新的成分股
            df_all = self.provider.get_sw_index_members(l3_code='850531.SI', is_new='N')
            self.assertIsNotNone(df_all)
            if not df_all.empty: # 确保 df_all 有数据再检查
                self.assertTrue(all(df_all['is_new'] == 'N'), "当is_new='N'时，所有记录的is_new字段应为'N'")
            
            print(f"\n申万行业成分股 (黄金 l3_code='850531.SI', is_new='N' - 非最新/历史): {len(df_all)} 条")
            print(f"申万行业成分股 (黄金 l3_code='850531.SI', is_new='Y' - 最新): {len(df_new)} 条")

        except DataNotFoundError:
            # is_new='N' 或 'Y' 时，如果行业代码有效，不应抛出 DataNotFoundError，除非该行业没有任何历史或当前成分股
            self.fail("DataNotFoundError不应在此处抛出，除非行业代码本身无任何数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败: {e}")

    def test_get_sw_index_members_no_data(self):
        """测试查询不存在的行业代码时是否抛出DataNotFoundError"""
        # 使用一个虚构的行业代码
        with self.assertRaisesRegex(DataNotFoundError, "未能获取到申万行业成分数据，参数: {'l3_code': 'NONEXISTENT.SI', 'is_new': 'Y'}", msg="查询不存在的行业代码应抛出DataNotFoundError并包含正确的消息"):
            self.provider.get_sw_index_members(l3_code='NONEXISTENT.SI')

    def test_get_sw_index_members_invalid_params(self):
        """测试不提供任何有效代码参数时的情况"""
        # 当不提供任何代码参数时，Tushare的 pro.index_member_all()
        # 预期会因缺少必要参数在其SDK层面直接报错 (如 TypeError 或 Tushare 内部错误)。
        # 这个错误会被我们的代码捕获并包装成 DataSourceError。
        with self.assertRaisesRegex(DataSourceError, "从Tushare获取申万行业成分失败:", msg="不提供有效代码参数应抛出DataSourceError"):
             self.provider.get_sw_index_members()


    def test_get_sw_index_members_by_industry_name(self):
        """测试通过申万行业名称获取成分股"""
        try:
            # 使用一个常见且大概率有数据的行业名称，例如 "银行"
            # 注意：Tushare的行业名称可能随时间更新，选择一个相对稳定的
            industry_name_to_test = "银行" # 或者其他如 "白酒", "证券" 等
            df = self.provider.get_sw_index_members(industry_name=industry_name_to_test)
            self.assertIsNotNone(df, f"返回的DataFrame不应为None (行业: {industry_name_to_test})")
            self.assertIsInstance(df, pd.DataFrame, "返回的应为DataFrame实例")
            self.assertGreater(len(df), 0, f"返回的DataFrame不应为空 (行业: {industry_name_to_test})")
            self.assertIn('ts_code', df.columns, "DataFrame应包含'ts_code'列")
            self.assertIn('name', df.columns, "DataFrame应包含'name'列")
            # 检查返回的成分股是否真的属于该行业或其子行业 (这一步较复杂，暂简化)
            # 例如，可以检查l1_name, l2_name, l3_name 是否与预期相关
            print(f"\n申万行业 '{industry_name_to_test}' 成分股 (前5条):")
            print(df.head())
        except DataNotFoundError:
            self.fail(f"不应抛出DataNotFoundError，行业 '{industry_name_to_test}' 应该有数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败 (行业: {industry_name_to_test}): {e}")

    def test_get_sw_index_members_by_invalid_industry_name(self):
        """测试通过无效的申万行业名称获取成分股，应抛出DataNotFoundError"""
        invalid_industry_name = "一个不存在的行业名称XYZ123"
        with self.assertRaisesRegex(DataNotFoundError, f"未找到申万行业名称: '{invalid_industry_name}'", msg="查询不存在的行业名称应抛出DataNotFoundError"):
            self.provider.get_sw_index_members(industry_name=invalid_industry_name)

    def test_get_sw_index_members_industry_name_priority(self):
        """测试当同时提供 industry_name 和其他代码参数时，industry_name 优先"""
        try:
            # 银行行业 vs 黄金行业的三级代码
            # 期望结果是银行行业的成分股
            industry_name_to_test = "银行"
            l3_code_ignored = '850531.SI' # 黄金
            df = self.provider.get_sw_index_members(industry_name=industry_name_to_test, l3_code=l3_code_ignored)
            self.assertIsNotNone(df)
            self.assertGreater(len(df), 0)
            # 简单验证是否是银行股，例如检查是否有银行股的常见股票
            # 此处仅作打印，实际测试可能需要更具体的验证逻辑
            print(f"\n测试 industry_name 优先于 l3_code (行业: {industry_name_to_test}, 忽略 l3_code: {l3_code_ignored}):")
            print(df.head())
            # 假设银行行业至少包含一只股票，其名称中不应包含“黄金”等字样（这是一个弱断言）
            # 更强的断言需要检查返回股票的行业分类是否确实是“银行”
            is_bank_stock_present = any("银行" in name for name in df['name'].head().tolist())
            is_gold_stock_present_in_l3 = any("黄金" in name for name in df['l3_name'].head().tolist() if 'l3_name' in df.columns)

            # 如果返回的是银行股，那么l3_name不应该是黄金相关
            if 'l3_name' in df.columns and not df[df['l3_name'] == '黄金III'].empty:
                 self.fail(f"当提供 industry_name='银行' 时，不应返回 l3_code='{l3_code_ignored}' (黄金) 的结果")
            
            # 这是一个更通用的检查，如果返回的股票中，其一级行业名大部分是“银行”，则认为industry_name优先
            if 'l1_name' in df.columns:
                bank_count = df['l1_name'].str.contains('银行', na=False).sum()
                if len(df) > 0 : # 确保df不为空
                     self.assertTrue(bank_count / len(df) > 0.5, "当industry_name='银行'提供时，大部分返回股票应属于银行一级行业")


        except DataNotFoundError:
            self.fail(f"不应抛出DataNotFoundError，行业 '{industry_name_to_test}' 应该有数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败: {e}")

    def test_get_sw_index_members_by_index_code(self):
        """测试通过申万行业代码(index_code)获取成分股"""
        try:
            # 使用一个已知的银行行业代码进行测试
            index_code_to_test = "801780.SI" # 申万一级 - 银行
            df = self.provider.get_sw_index_members(index_code=index_code_to_test)
            self.assertIsNotNone(df, f"返回的DataFrame不应为None (index_code: {index_code_to_test})")
            self.assertIsInstance(df, pd.DataFrame, "返回的应为DataFrame实例")
            self.assertGreater(len(df), 0, f"返回的DataFrame不应为空 (index_code: {index_code_to_test})")
            self.assertIn('ts_code', df.columns, "DataFrame应包含'ts_code'列")
            self.assertIn('name', df.columns, "DataFrame应包含'name'列")
            print(f"\n申万行业成分股 (index_code='{index_code_to_test}'):")
            print(df.head())
        except DataNotFoundError:
            self.fail(f"不应抛出DataNotFoundError，index_code '{index_code_to_test}' 应该有数据")
        except DataSourceError as e:
            self.fail(f"Tushare API 调用失败 (index_code: {index_code_to_test}): {e}")

if __name__ == '__main__':
    unittest.main()