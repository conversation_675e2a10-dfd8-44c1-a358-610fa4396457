import unittest
import pandas as pd
from datetime import datetime, timedelta
from src.cli.excel_processor import ExcelProcessor
from src.data_providers.stock_data_provider import StockDataProviderFactory

class TestExcelProcessor(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        self.processor = ExcelProcessor()
        self.test_stock_code = "000001"  # 使用平安银行的股票代码作为测试
        
    def test_get_stock_data_for_analysis_with_sina(self):
        """测试从新浪获取数据并处理实时数据的功能"""
        # 准备测试参数
        params = {
            'data_source': 'sina',
            'period': '5d',
            'interval': '1d'
        }
        
        # 执行测试
        stock_df = self.processor._get_stock_data_for_analysis(self.test_stock_code, params)
        
        # 验证结果
        self.assertIsNotNone(stock_df, "返回的数据不应该为None")
        self.assertFalse(stock_df.empty, "返回的数据不应该为空")
        
        # 验证数据结构
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            self.assertIn(col, stock_df.columns, f"数据应该包含{col}列")
        
        # 验证数据时间范围
        today = pd.Timestamp.now().normalize()
        self.assertLessEqual((today - stock_df.index.max()).days, 1, 
                           "最新数据应该是今天或昨天的")
        
        # 打印详细信息以供分析
        print("\n测试结果详情:")
        print(f"数据时间范围: {stock_df.index.min()} 到 {stock_df.index.max()}")
        print(f"数据条数: {len(stock_df)}")
        print("\n最新的数据行:")
        print(stock_df.iloc[-1])
        
    def test_get_stock_data_for_analysis_without_realtime(self):
        """测试使用非新浪数据源时的情况"""
        # 准备测试参数
        params = {
            'data_source': 'yahoo',  # 使用雅虎数据源
            'period': '5d',
            'interval': '1d'
        }
        
        # 执行测试
        stock_df = self.processor._get_stock_data_for_analysis(self.test_stock_code, params)
        
        # 验证结果
        self.assertIsNotNone(stock_df, "返回的数据不应该为None")
        self.assertFalse(stock_df.empty, "返回的数据不应该为空")
        
        # 验证数据结构
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            self.assertIn(col, stock_df.columns, f"数据应该包含{col}列")
        
        # 打印详细信息以供分析
        print("\n非实时数据测试结果详情:")
        print(f"数据时间范围: {stock_df.index.min()} 到 {stock_df.index.max()}")
        print(f"数据条数: {len(stock_df)}")
        print("\n最新的数据行:")
        print(stock_df.iloc[-1])
        
    def test_realtime_data_update(self):
        """测试实时数据更新逻辑"""
        # 准备测试参数
        params = {
            'data_source': 'sina',
            'period': '5d',
            'interval': '1d'
        }
        
        # 第一次获取数据
        stock_df_first = self.processor._get_stock_data_for_analysis(self.test_stock_code, params)
        
        # 记录第一次获取的数据信息
        first_latest_date = stock_df_first.index.max()
        first_latest_close = stock_df_first.loc[first_latest_date, 'Close']
        
        # 等待一小段时间后再次获取数据
        import time
        time.sleep(5)  # 等待5秒
        
        # 第二次获取数据
        stock_df_second = self.processor._get_stock_data_for_analysis(self.test_stock_code, params)
        
        # 记录第二次获取的数据信息
        second_latest_date = stock_df_second.index.max()
        second_latest_close = stock_df_second.loc[second_latest_date, 'Close']
        
        # 打印对比信息
        print("\n实时数据更新测试结果:")
        print(f"第一次获取 - 最新日期: {first_latest_date}, 收盘价: {first_latest_close}")
        print(f"第二次获取 - 最新日期: {second_latest_date}, 收盘价: {second_latest_close}")
        
        # 如果是同一天的数据，验证是否有更新
        if first_latest_date == second_latest_date:
            if first_latest_close != second_latest_close:
                print("实时数据已更新")
            else:
                print("实时数据未发生变化")

if __name__ == '__main__':
    unittest.main() 