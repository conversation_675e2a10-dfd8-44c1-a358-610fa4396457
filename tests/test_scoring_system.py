"""
打分系统测试模块
"""
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from src.technical_indicators.scoring_system import StockScorer

class TestStockScorer(unittest.TestCase):
    """股票打分系统测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        print("\n" + "="*50)
        print("开始测试前的准备工作")
        # 创建模拟数据
        self.test_data = self._create_test_data()
        print(f"- 创建了 {len(self.test_data)} 天的模拟数据")
        print(f"- 数据列: {', '.join(self.test_data.columns)}")
        
        # 创建打分器实例
        self.scorer = StockScorer()
        print("- 创建打分器实例")
        print("="*50 + "\n")
        
    def _create_test_data(self) -> pd.DataFrame:
        """
        创建测试用的股票数据
        
        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        # 生成120天的日期序列
        dates = [datetime.now() - timedelta(days=x) for x in range(120)]
        dates.reverse()
        
        # 生成模拟数据
        np.random.seed(42)  # 设置随机种子，确保结果可重现
        
        # 基础价格
        base_price = 100
        prices = []
        current_price = base_price
        
        for _ in range(120):
            # 生成随机波动
            change = np.random.normal(0, 2)  # 均值为0，标准差为2的正态分布
            current_price = current_price * (1 + change/100)
            prices.append(current_price)
            
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.5)/100) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.5))/100) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.5))/100) for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000000, 10000000, 120)
        })
        
        # 确保High >= Open, Close >= Low
        df['High'] = df[['Open', 'Close']].max(axis=1) + abs(np.random.normal(0, 0.5, 120))
        df['Low'] = df[['Open', 'Close']].min(axis=1) - abs(np.random.normal(0, 0.5, 120))
        
        return df
        
    def test_total_score(self):
        """测试总得分计算"""
        print("\n测试总得分计算")
        print("-"*30)
        
        # 计算总得分
        total_score, details = self.scorer.get_total_score(self.test_data)
        print(f"总得分: {total_score:.2f}")
        
        # 验证返回值类型
        self.assertIsInstance(total_score, float)
        self.assertIsInstance(details, dict)
        print("✓ 返回值类型验证通过")
        
        # 验证得分范围
        self.assertGreaterEqual(total_score, 0)
        self.assertLessEqual(total_score, 100)
        print("✓ 得分范围验证通过")
        
        # 验证详细信息结构
        self.assertIn('price_action', details)
        self.assertIn('quantitative', details)
        self.assertIn('total_score', details)
        print("✓ 详细信息结构验证通过")
        
        # 验证子得分范围
        self.assertGreaterEqual(details['price_action']['score'], 0)
        self.assertLessEqual(details['price_action']['score'], 100)
        self.assertGreaterEqual(details['quantitative']['score'], 0)
        self.assertLessEqual(details['quantitative']['score'], 100)
        print("✓ 子得分范围验证通过")
        
        print(f"价格行为得分: {details['price_action']['score']:.2f}")
        print(f"量化识别得分: {details['quantitative']['score']:.2f}")
        print("-"*30 + "\n")
        
    def test_price_action_signals(self):
        """测试价格行为信号计算"""
        print("\n测试价格行为信号计算")
        print("-"*30)
        
        # 计算价格行为信号得分
        price_action_score, price_action_details = self.scorer.price_action.calculate(self.test_data)
        print(f"价格行为得分: {price_action_score:.2f}")
        
        # 验证返回值类型
        self.assertIsInstance(price_action_score, float)
        self.assertIsInstance(price_action_details, dict)
        print("✓ 返回值类型验证通过")
        
        # 验证得分范围
        self.assertGreaterEqual(price_action_score, 0)
        self.assertLessEqual(price_action_score, 100)
        print("✓ 得分范围验证通过")
        
        # 验证子信号详情
        self.assertIn('trend_strength', price_action_details)
        self.assertIn('support_resistance', price_action_details)
        self.assertIn('candlestick', price_action_details)
        self.assertIn('price_pattern', price_action_details)
        self.assertIn('momentum', price_action_details)
        print("✓ 子信号详情验证通过")
        
        print("-"*30 + "\n")
        
    def test_quantitative_signals(self):
        """测试量化识别信号计算"""
        print("\n测试量化识别信号计算")
        print("-"*30)
        
        # 计算量化识别信号得分
        quantitative_score, quantitative_details = self.scorer.quantitative.calculate(self.test_data)
        print(f"量化识别得分: {quantitative_score:.2f}")
        
        # 验证返回值类型
        self.assertIsInstance(quantitative_score, float)
        self.assertIsInstance(quantitative_details, dict)
        print("✓ 返回值类型验证通过")
        
        # 验证得分范围
        self.assertGreaterEqual(quantitative_score, 0)
        self.assertLessEqual(quantitative_score, 100)
        print("✓ 得分范围验证通过")
        
        # 验证子信号详情
        self.assertIn('rsi', quantitative_details)
        self.assertIn('macd', quantitative_details)
        self.assertIn('kdj', quantitative_details)
        self.assertIn('bollinger', quantitative_details)
        print("✓ 子信号详情验证通过")
        
        print("-"*30 + "\n")
        
    def test_custom_weights(self):
        """测试自定义权重"""
        print("\n测试自定义权重")
        print("-"*30)
        
        # 创建自定义权重
        custom_weights = {
            'price_action': 0.7,
            'quantitative': 0.3
        }
        print(f"自定义权重: {custom_weights}")
        
        # 使用自定义权重创建打分器
        scorer = StockScorer(weights=custom_weights)
        
        # 计算总得分
        total_score, details = scorer.get_total_score(self.test_data)
        print(f"总得分: {total_score:.2f}")
        
        # 验证权重是否正确应用
        expected_score = (
            details['price_action']['score'] * custom_weights['price_action'] +
            details['quantitative']['score'] * custom_weights['quantitative']
        )
        self.assertAlmostEqual(total_score, expected_score, places=2)
        print("✓ 权重计算验证通过")
        
        print("-"*30 + "\n")
        
    def test_data_requirements(self):
        """测试数据要求"""
        print("\n测试数据要求")
        print("-"*30)
        
        # 测试缺少必要列的情况
        print("测试缺少必要列的情况...")
        invalid_data = self.test_data.drop('Close', axis=1)
        with self.assertRaises(KeyError) as context:
            self.scorer.get_total_score(invalid_data)
        self.assertIn("数据缺少必要的列", str(context.exception))
        print("✓ 缺少列异常测试通过")
            
        # 测试数据长度不足的情况
        print("测试数据长度不足的情况...")
        short_data = self.test_data.head(30)  # 使用30天的数据，小于最小要求的60天
        with self.assertRaises(IndexError) as context:
            self.scorer.get_total_score(short_data)
        self.assertIn("数据长度不足", str(context.exception))
        print("✓ 数据长度不足异常测试通过")
        
        print("-"*30 + "\n")
            
    def test_score_details(self):
        """测试详细得分信息"""
        print("\n测试详细得分信息")
        print("-"*30)
        
        # 获取详细得分信息
        details = self.scorer.get_score_details(self.test_data)
        
        # 验证返回的详细信息
        self.assertIn('trend', details)
        self.assertIn('volume', details)
        print("✓ 基本信息结构验证通过")
        
        # 验证趋势详情
        trend_details = details['trend']
        self.assertIn('ma_trend', trend_details)
        self.assertIn('macd', trend_details)
        print("✓ 趋势详情验证通过")
        
        # 验证成交量详情
        volume_details = details['volume']
        self.assertIn('volume_trend', volume_details)
        self.assertIn('volume_stability', volume_details)
        self.assertIn('price_volume_match', volume_details)
        self.assertIn('large_volume', volume_details)
        print("✓ 成交量详情验证通过")
        
        print("-"*30 + "\n")

if __name__ == '__main__':
    unittest.main(verbosity=2)  # 使用更详细的输出级别 