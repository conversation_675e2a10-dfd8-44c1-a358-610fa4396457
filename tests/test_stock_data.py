import unittest
from datetime import datetime
import pandas as pd
import numpy as np
from src.data_providers.stock_data_provider import StockDataProviderFactory

class StockDataTest:
    """股票数据测试类"""
    
    def __init__(self):
        """初始化，默认使用 Tushare 数据源"""
        self.provider = StockDataProviderFactory.get_provider('tushare')
    
    def get_stock_data(self, stock_code, start_date=None, end_date=None):
        """
        获取指定股票在给定日期范围内的数据，并计算技术指标
        
        参数:
            stock_code (str): 股票代码，例如 "600519"
            start_date (str): 开始日期，格式为 "YYYY-MM-DD"，默认为 None
            end_date (str): 结束日期，格式为 "YYYY-MM-DD"，默认为 None
            
        返回:
            DataFrame: 包含股票数据和技术指标的DataFrame
        """
        try:
            # 如果没有提供日期，使用默认值
            if not start_date:
                # 默认获取最近3个月的数据
                start_date = (datetime.now() - pd.DateOffset(months=6)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            # 为了确保有足够的数据计算技术指标，额外获取60个交易日的数据
            calc_start_date = (pd.to_datetime(start_date) - pd.DateOffset(days=90)).strftime('%Y-%m-%d')
            
            # 获取数据
            df = self.provider.get_stock_data_by_date(stock_code, calc_start_date, end_date)
            
            # 检查数据是否为空
            if df.empty:
                print(f"未能获取到股票 {stock_code} 的数据")
                return pd.DataFrame()
            
            # 计算技术指标（使用完整数据集）
            df = self._calculate_technical_indicators(df)
            
            # 过滤出请求的日期范围
            mask = (df.index >= pd.to_datetime(start_date)) & (df.index <= pd.to_datetime(end_date))
            df = df[mask]
            
            # 重置索引，保留日期列
            df = df.reset_index()
            
            return df

        except Exception as e:
            print(f"获取股票数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def _calculate_technical_indicators(self, df):
        """
        计算各种技术指标
        
        参数:
            df: 原始数据DataFrame
        
        返回:
            添加了技术指标的DataFrame
        """
        try:
            # 确保数据按日期排序
            df = df.sort_index()
            
            # 移动平均线（MA）
            df['ma_5'] = df['Close'].rolling(window=5, min_periods=5).mean()
            df['ma_20'] = df['Close'].rolling(window=20, min_periods=20).mean()
            
            # 相对强弱指数（RSI）- 使用更小的min_periods
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14, min_periods=14).mean()
            avg_loss = loss.rolling(window=14, min_periods=14).mean()
            rs = avg_gain / avg_loss
            df['rsi_14'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['Close'].ewm(span=12, adjust=False, min_periods=12).mean()
            exp2 = df['Close'].ewm(span=26, adjust=False, min_periods=26).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9, adjust=False, min_periods=9).mean()
            df['macd_diff'] = df['macd'] - df['macd_signal']
            
            # 布林带 - 使用更小的min_periods
            df['boll_mid'] = df['Close'].rolling(window=20, min_periods=20).mean()
            df['boll_std'] = df['Close'].rolling(window=20, min_periods=20).std()
            df['boll_upper'] = df['boll_mid'] + 2 * df['boll_std']
            df['boll_lower'] = df['boll_mid'] - 2 * df['boll_std']
            
            # 成交量均线
            df['vol_ma_5'] = df['Volume'].rolling(window=5, min_periods=5).mean()
            df['vol_ma_20'] = df['Volume'].rolling(window=20, min_periods=20).mean()
            
            # 处理可能的无穷大和NaN值
            df = df.replace([np.inf, -np.inf], np.nan)
            
            return df
            
        except Exception as e:
            print(f"计算技术指标时出错: {str(e)}")
            return df

# 使用示例
if __name__ == '__main__':
    # 创建测试实例
    stock_test = StockDataTest()
    
    # 示例1：获取指定日期范围的数据
    df1 = stock_test.get_stock_data(
        stock_code="600519",  # 贵州茅台
        start_date="2024-01-01",
        end_date="2024-03-20"
    )
    print("\n示例1 - 指定日期范围的数据:")
    print("\n基本数据:")
    print(df1[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].head())
    print("\n技术指标:")
    print(df1[['Date', 'ma_5', 'ma_20', 'rsi_14', 'macd', 'boll_upper', 'boll_lower']].head())
    
    # 示例2：获取最近三个月的数据（使用默认日期）
    df2 = stock_test.get_stock_data(
        stock_code="000001"  # 平安银行
    )
    print("\n示例2 - 最近三个月的数据:")
    print("\n基本数据:")
    print(df2[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].head())
    print("\n技术指标:")
    print(df2[['Date', 'ma_5', 'ma_20', 'rsi_14', 'macd', 'boll_upper', 'boll_lower']].head()) 