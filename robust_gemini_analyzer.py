#!/usr/bin/env python3
"""
增强版 Gemini API 股票分析器 - 专门处理连接问题
"""

import os
import json
import requests
import pandas as pd
import time
import ssl
from datetime import datetime
import argparse
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# 禁用SSL警告（仅在测试时使用）
urllib3.disable_warnings(InsecureRequestWarning)

class RobustGeminiAnalyzer:
    def __init__(self, api_key=None, output_dir="analysis_reports"):
        """
        初始化增强版 Gemini 分析器
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("请设置 GEMINI_API_KEY 环境变量或提供 API 密钥")

        self.output_dir = output_dir
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")
        
        # 多个模型和端点配置
        self.models = [
            "gemini-1.5-flash",
            "gemini-1.5-pro",
            "gemini-pro",
            "gemini-1.0-pro"
        ]
        
        self.api_endpoints = [
            "https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent",
            "https://ai.google.dev/v1beta/models/{model}:generateContent"
        ]
        
        # 连接配置（按优先级排序）
        self.connection_configs = [
            {"verify": True, "timeout": 30, "name": "标准HTTPS连接"},
            {"verify": True, "timeout": 60, "name": "延长超时连接"},
            {"verify": False, "timeout": 30, "name": "跳过SSL验证"},
            {"verify": False, "timeout": 60, "name": "跳过SSL验证+延长超时"}
        ]
        
        # 代理配置（如果环境变量中有）
        self.proxies = {}
        if os.getenv('HTTP_PROXY') or os.getenv('HTTPS_PROXY'):
            self.proxies = {
                'http': os.getenv('HTTP_PROXY'),
                'https': os.getenv('HTTPS_PROXY')
            }
            print(f"🌐 检测到代理配置: {self.proxies}")
    
    def diagnose_connection(self):
        """诊断网络连接状态"""
        print("🔍 开始网络连接诊断...")
        
        # 1. 检查SSL版本
        print(f"🔐 SSL版本: {ssl.OPENSSL_VERSION}")
        
        # 2. 检查证书路径
        try:
            cert_path = requests.certs.where()
            print(f"📜 证书路径: {cert_path}")
        except Exception as e:
            print(f"⚠️ 证书检查失败: {e}")
        
        # 3. 测试域名解析
        import socket
        try:
            ip = socket.gethostbyname('generativelanguage.googleapis.com')
            print(f"🌐 DNS解析成功: generativelanguage.googleapis.com -> {ip}")
        except Exception as e:
            print(f"❌ DNS解析失败: {e}")
            return False
        
        # 4. 测试基本连接
        test_urls = [
            "https://www.google.com",
            "https://generativelanguage.googleapis.com"
        ]
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10, verify=True)
                print(f"✅ {url} 连接成功 (状态码: {response.status_code})")
            except requests.exceptions.SSLError as e:
                print(f"🔒 {url} SSL错误: {str(e)[:100]}...")
            except requests.exceptions.ConnectionError as e:
                print(f"🔌 {url} 连接错误: {str(e)[:100]}...")
            except Exception as e:
                print(f"❌ {url} 其他错误: {str(e)[:100]}...")
        
        return True
    
    def robust_api_call(self, prompt, max_retries=3):
        """
        增强的API调用，支持多种连接方式和错误恢复
        """
        print("🤖 开始增强版 API 调用...")
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'RobustGeminiAnalyzer/1.0'
        }
        
        data = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048,
            }
        }
        
        # 尝试所有模型和连接配置的组合
        for model in self.models:
            print(f"\n🔄 尝试模型: {model}")
            
            for endpoint_template in self.api_endpoints:
                endpoint = endpoint_template.format(model=model)
                print(f"  📡 端点: {endpoint}")
                
                for config in self.connection_configs:
                    print(f"    🔧 连接方式: {config['name']}")
                    
                    for attempt in range(max_retries):
                        try:
                            # 准备请求参数
                            request_kwargs = {
                                'headers': headers,
                                'json': data,
                                'timeout': config['timeout'],
                                'verify': config['verify']
                            }
                            
                            # 添加代理（如果有）
                            if self.proxies:
                                request_kwargs['proxies'] = self.proxies
                            
                            # 发送请求
                            response = requests.post(
                                f"{endpoint}?key={self.api_key}",
                                **request_kwargs
                            )
                            
                            print(f"      📊 状态码: {response.status_code}")
                            
                            if response.status_code == 200:
                                result = response.json()
                                if 'candidates' in result and len(result['candidates']) > 0:
                                    print(f"      ✅ 成功! 使用模型: {model}")
                                    return result['candidates'][0]['content']['parts'][0]['text']
                                else:
                                    print(f"      ⚠️ 返回空结果")
                                    continue
                            elif response.status_code == 404:
                                print(f"      ❌ 模型不存在")
                                break  # 跳到下一个模型
                            elif response.status_code == 401:
                                print(f"      🔑 API密钥错误")
                                return "API密钥验证失败，请检查密钥是否正确"
                            elif response.status_code == 429:
                                print(f"      🚫 请求限制，等待重试...")
                                time.sleep(2 ** attempt)
                                continue
                            else:
                                print(f"      ❌ HTTP错误: {response.status_code}")
                                print(f"      📄 错误详情: {response.text[:200]}...")
                                continue
                                
                        except requests.exceptions.SSLError as e:
                            print(f"      🔒 SSL错误 (尝试 {attempt+1}/{max_retries}): {str(e)[:100]}...")
                            if attempt < max_retries - 1:
                                time.sleep(1)
                            continue
                            
                        except requests.exceptions.ConnectionError as e:
                            print(f"      🔌 连接错误 (尝试 {attempt+1}/{max_retries}): {str(e)[:100]}...")
                            if attempt < max_retries - 1:
                                time.sleep(2)
                            continue
                            
                        except requests.exceptions.Timeout as e:
                            print(f"      ⏰ 超时错误 (尝试 {attempt+1}/{max_retries}): {str(e)[:100]}...")
                            if attempt < max_retries - 1:
                                time.sleep(1)
                            continue
                            
                        except Exception as e:
                            print(f"      ❌ 未知错误 (尝试 {attempt+1}/{max_retries}): {str(e)[:100]}...")
                            if attempt < max_retries - 1:
                                time.sleep(1)
                            continue
        
        return "所有连接方式都失败了。请检查网络连接、API密钥或尝试使用代理。"
    
    def load_stock_data(self, input_dir):
        """加载股票数据（复用原有逻辑）"""
        print("📊 正在加载股票数据...")
        
        csv_files = []
        for date_folder in sorted(os.listdir(input_dir)):
            date_folder_path = os.path.join(input_dir, date_folder)
            if os.path.isdir(date_folder_path):
                csv_file_path = os.path.join(date_folder_path, "above_cloud_daily_change.csv")
                if os.path.isfile(csv_file_path):
                    csv_files.append((date_folder, csv_file_path))
        
        all_data = {}
        for date_str, file_path in csv_files:
            try:
                df = pd.read_csv(file_path, dtype={'股票代码': str})
                df['排名'] = range(1, len(df) + 1)
                all_data[date_str] = df
                print(f"  ✅ {date_str}: {len(df)} 只股票")
            except Exception as e:
                print(f"  ❌ {date_str}: 加载失败 - {e}")
        
        return all_data
    
    def extract_rising_stocks(self, all_data):
        """提取排名上升股票（复用原有逻辑）"""
        print("🔍 正在分析排名上升股票...")
        
        dates = sorted(all_data.keys())
        rising_stocks = []
        
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            
            current_df = all_data[current_date]
            next_df = all_data[next_date]
            
            current_df['股票代码'] = current_df['股票代码'].astype(str)
            next_df['股票代码'] = next_df['股票代码'].astype(str)
            
            common_stocks = set(current_df['股票代码']) & set(next_df['股票代码'])
            
            for stock_code in common_stocks:
                current_rank = current_df[current_df['股票代码'] == stock_code]['排名'].iloc[0]
                next_rank = next_df[next_df['股票代码'] == stock_code]['排名'].iloc[0]
                
                if next_rank < current_rank:
                    stock_info = next_df[next_df['股票代码'] == stock_code].iloc[0]
                    
                    rising_stocks.append({
                        'stock_code': stock_code,
                        'stock_name': stock_info.get('股票名称', '未知'),
                        'start_date': current_date,
                        'end_date': next_date,
                        'start_rank': current_rank,
                        'end_rank': next_rank,
                        'rank_improvement': current_rank - next_rank,
                        'total_gain': stock_info.get('当日涨幅(%)', 0),
                        'sector': stock_info.get('申万板块', '未知板块'),
                        'concepts': stock_info.get('开盘啦概念', ''),
                        'market_cap': stock_info.get('流通市值', 0)
                    })
        
        rising_stocks.sort(key=lambda x: x['total_gain'], reverse=True)
        print(f"  📈 发现 {len(rising_stocks)} 只排名上升股票")
        
        return rising_stocks
    
    def analyze(self, input_dir):
        """执行完整的分析流程"""
        print("🚀 开始增强版股票趋势 AI 分析")
        print("=" * 60)
        
        # 1. 网络诊断
        if not self.diagnose_connection():
            print("❌ 网络诊断失败，但继续尝试分析...")
        
        # 2. 加载数据
        all_data = self.load_stock_data(input_dir)
        if not all_data:
            print("❌ 没有找到有效的股票数据")
            return
        
        # 3. 提取排名上升股票
        rising_stocks = self.extract_rising_stocks(all_data)
        if not rising_stocks:
            print("❌ 没有发现排名上升的股票")
            return
        
        # 4. 格式化数据
        data_str = json.dumps({
            "总体统计": {
                "排名上升股票数量": len(rising_stocks),
                "平均涨幅": sum(stock['total_gain'] for stock in rising_stocks) / len(rising_stocks),
                "最大涨幅": max(stock['total_gain'] for stock in rising_stocks),
                "最小涨幅": min(stock['total_gain'] for stock in rising_stocks),
                "上涨股票数": len([s for s in rising_stocks if s['total_gain'] > 0])
            },
            "前10名股票详情": [
                {
                    "排名": i,
                    "股票代码": stock['stock_code'],
                    "股票名称": stock['stock_name'],
                    "申万板块": stock['sector'],
                    "概念标签": stock['concepts'],
                    "流通市值": f"{float(stock['market_cap']):.1f}亿" if stock['market_cap'] else "未知",
                    "排名变化": f"从{stock['start_rank']}→{stock['end_rank']} (上升{stock['rank_improvement']}位)",
                    "涨幅": f"{stock['total_gain']:.2f}%",
                    "日期": f"{stock['start_date']}至{stock['end_date']}"
                }
                for i, stock in enumerate(rising_stocks[:10], 1)
            ]
        }, ensure_ascii=False, indent=2)
        
        # 5. 创建分析提示词
        prompt = f"""
你是一位资深的股票分析师，请基于以下排名连续上升股票的数据进行深度分析：

{data_str}

请从以下几个维度进行专业分析：

## 📊 **核心发现分析**
1. **涨幅特征**：分析涨幅分布规律，识别高涨幅股票的共同特点
2. **板块效应**：分析申万板块的表现差异，找出表现最优的板块
3. **概念热点**：识别当前市场最热门的概念，分析概念轮动趋势
4. **市值效应**：分析不同市值股票的表现差异

## 🎯 **投资洞察**
1. **强势板块**：哪些板块表现最突出？原因是什么？
2. **概念机会**：哪些概念最值得关注？持续性如何？
3. **选股策略**：基于数据总结出的选股规律和策略
4. **风险提示**：需要注意的风险点和陷阱

## 📈 **市场趋势判断**
1. **资金偏好**：当前资金更偏好什么类型的股票？
2. **热点轮动**：概念和板块的轮动特征
3. **后市展望**：基于当前数据对后市的判断

## 💡 **实用建议**
1. **优选标准**：给出具体的选股标准和组合建议
2. **操作策略**：提供具体的买卖时机和仓位建议
3. **风险控制**：风险管理的具体措施

请用专业、简洁的语言进行分析，重点突出数据背后的投资逻辑和实用价值。使用适当的emoji让分析更生动易读。
"""
        
        # 6. 调用增强版 API
        analysis_result = self.robust_api_call(prompt)
        
        # 7. 输出结果
        print("\n" + "=" * 60)
        print("🎯 增强版 Gemini AI 分析结果")
        print("=" * 60)
        print(analysis_result)
        
        # 8. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(self.output_dir, f"robust_gemini_analysis_{timestamp}.md")

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 增强版股票趋势 AI 分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**数据源**: {input_dir}\n\n")
            f.write(f"**分析股票数**: {len(rising_stocks)}\n\n")
            f.write("---\n\n")
            f.write(analysis_result)

        print(f"\n📄 分析报告已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="增强版 Gemini AI 股票趋势分析")
    parser.add_argument("--input_dir", type=str, required=True,
                       help="包含股票数据的目录路径")
    parser.add_argument("--output_dir", type=str, default="analysis_reports",
                       help="输出目录路径 (默认: analysis_reports)")
    parser.add_argument("--api_key", type=str,
                       help="Gemini API 密钥")

    args = parser.parse_args()

    try:
        analyzer = RobustGeminiAnalyzer(api_key=args.api_key, output_dir=args.output_dir)
        analyzer.analyze(args.input_dir)
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
